# Security Environment Variables Setup

This document outlines the required environment variables for the enhanced security features in BH-CRM.

## Critical Security Environment Variables

### 1. ENCRYPTION_MASTER_KEY (REQUIRED)
- **Description**: Master encryption key for AES-256-GCM encryption of sensitive data
- **Format**: String, minimum 32 characters (recommended 64+ characters)
- **Example**: `your-super-secure-64-character-encryption-key-here-make-it-random`
- **Security**: This key encrypts Twilio credentials and other sensitive data
- **Required**: ✅ YES - Application will not start without this

### 2. ENCRYPTION_KEY_VERSION (OPTIONAL)
- **Description**: Current encryption key version for key rotation
- **Format**: Integer
- **Default**: `1`
- **Example**: `2`
- **Required**: ❌ Optional (defaults to 1)

### 3. ENCRYPTION_KEY_V1, ENCRYPTION_KEY_V2, etc. (OPTIONAL)
- **Description**: Historical encryption keys for decrypting old data during key rotation
- **Format**: String, same format as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_MASTER_KEY
- **Example**: `<PERSON>NCRYPTION_KEY_V1=old-encryption-key-from-previous-version`
- **Required**: ❌ Optional (only needed for key rotation)

## Existing Environment Variables

### Email Configuration
- `CONVEX_RESEND_API_KEY` - Resend API key for email sending
- `FROM_EMAIL` - Sender email address
- `FROM_NAME` - Sender name

### Convex Configuration
- `CONVEX_SITE_URL` - Convex site URL for authentication
- `VITE_CONVEX_URL` - Convex URL for frontend

## How to Set Up Environment Variables

### For Development (.env.local)
```bash
# Security Keys
ENCRYPTION_MASTER_KEY=your-super-secure-64-character-encryption-key-here-make-it-random
ENCRYPTION_KEY_VERSION=1

# Email Configuration
CONVEX_RESEND_API_KEY=re_your_resend_api_key_here
FROM_EMAIL=<EMAIL>
FROM_NAME=Your Company Name

# Convex Configuration
CONVEX_SITE_URL=https://your-app.convex.site
VITE_CONVEX_URL=https://your-deployment.convex.cloud
```

### For Production (Convex Dashboard)
1. Go to https://dashboard.convex.dev
2. Select your project
3. Go to "Settings" → "Environment Variables"
4. Add each variable with its value

## Encryption Key Generation

### Generate a Secure Encryption Key
```bash
# Using OpenSSL (recommended)
openssl rand -hex 32

# Using Node.js
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"

# Using Python
python3 -c "import secrets; print(secrets.token_hex(32))"
```

### Key Requirements
- **Minimum Length**: 32 characters
- **Recommended Length**: 64+ characters
- **Character Set**: Use alphanumeric characters and symbols
- **Randomness**: Must be cryptographically random
- **Uniqueness**: Each environment should have a unique key

## Key Rotation Process

### Step 1: Generate New Key
```bash
# Generate new encryption key
NEW_KEY=$(openssl rand -hex 32)
echo "New key: $NEW_KEY"
```

### Step 2: Update Environment Variables
```bash
# Set new key as current
ENCRYPTION_MASTER_KEY=$NEW_KEY
ENCRYPTION_KEY_VERSION=2

# Keep old key for decryption
ENCRYPTION_KEY_V1=your-old-encryption-key-here
```

### Step 3: Deploy and Verify
1. Deploy with new environment variables
2. Verify encryption/decryption works
3. Monitor for any decryption errors
4. After successful migration, old keys can be removed

## Security Best Practices

### Key Management
1. **Never commit keys to version control**
2. **Use different keys for different environments**
3. **Rotate keys regularly (every 6-12 months)**
4. **Store keys securely (password manager, vault)**
5. **Limit access to production keys**

### Environment Security
1. **Use HTTPS for all communications**
2. **Restrict environment variable access**
3. **Monitor for unauthorized access**
4. **Regular security audits**
5. **Backup encryption keys securely**

### Key Storage Options
1. **Development**: `.env.local` file (not committed)
2. **Staging/Production**: Convex Dashboard environment variables
3. **Enterprise**: Consider using AWS Secrets Manager, Azure Key Vault, or HashiCorp Vault

## Validation and Testing

### Environment Validation
The application automatically validates encryption configuration on startup:
- Checks if ENCRYPTION_MASTER_KEY is set
- Validates key length (minimum 32 characters)
- Tests encryption/decryption functionality
- Reports any configuration issues

### Testing Encryption
```bash
# The application includes built-in encryption testing
# Check logs for validation results on startup
```

## Troubleshooting

### Common Issues

#### "ENCRYPTION_MASTER_KEY environment variable is required"
- **Cause**: Missing encryption key
- **Solution**: Set ENCRYPTION_MASTER_KEY environment variable

#### "ENCRYPTION_MASTER_KEY must be at least 32 characters long"
- **Cause**: Key too short
- **Solution**: Generate a longer key using the methods above

#### "Failed to decrypt credential"
- **Cause**: Key mismatch or corrupted data
- **Solution**: Check key version and historical keys

#### "Encryption configuration error"
- **Cause**: Invalid key format or configuration
- **Solution**: Regenerate key and verify format

### Emergency Recovery

#### Lost Encryption Key
1. **If you have backups**: Restore from secure backup
2. **If no backups**: You'll need to:
   - Generate new encryption key
   - Re-enter all encrypted credentials (Twilio settings)
   - Update environment variables

#### Key Rotation Issues
1. **Keep old keys available** during transition
2. **Monitor application logs** for decryption errors
3. **Have rollback plan** ready

## Security Monitoring

### What to Monitor
1. **Failed decryption attempts**
2. **Encryption configuration errors**
3. **Unauthorized environment variable access**
4. **Key rotation events**

### Log Examples
```
INFO: Encryption configuration validated successfully
WARN: Using encryption key version 1, consider rotation
ERROR: Failed to decrypt credential - check key configuration
```

## Compliance Notes

### Data Protection
- Encryption keys are considered sensitive data
- Follow your organization's data protection policies
- Consider regulatory requirements (GDPR, HIPAA, etc.)

### Audit Trail
- All encryption/decryption operations are logged
- Key rotation events are recorded
- Failed attempts are monitored

## Support

For security-related issues:
1. Check application logs for specific error messages
2. Verify environment variable configuration
3. Test encryption functionality
4. Contact your security team if needed

**Remember**: Never share encryption keys through insecure channels (email, chat, etc.)

# Convex Environment Variables Setup for Resend Email

Add these environment variables to your Convex dashboard at: https://dashboard.convex.dev

## Required Variables

### 1. CONVEX_RESEND_API_KEY
- **Description**: Your Resend API key
- **Value**: Your API key from Resend dashboard (starts with `re_`)
- **Example**: `re_123456789abcdef...`
- **Required**: ✅ YES

## Optional Variables (for Custom Domain)

### 2. FROM_EMAIL
- **Description**: Your verified sending email address
- **Value**: Email address from your verified domain in Resend
- **Example**: `<EMAIL>` or `<EMAIL>`
- **Default**: `<EMAIL>` (if not set)
- **Required**: ❌ Optional (but recommended for professional emails)

### 3. FROM_NAME
- **Description**: The sender name that appears in emails
- **Value**: Your business/company name
- **Example**: `BH HVAC Services` or `Your Company Name`
- **Default**: Uses your company name from settings
- **Required**: ❌ Optional

### 4. RESEND_FROM_EMAIL
- **Description**: Alternative name for FROM_EMAIL (fallback)
- **Value**: Same as FROM_EMAIL
- **Required**: ❌ Optional (only if you prefer this name)

## How to Add Variables in Convex:

1. Go to https://dashboard.convex.dev
2. Select your project
3. Go to "Settings" → "Environment Variables"
4. Click "Add Environment Variable"
5. Add each variable with its value

## Example Configuration:

```
CONVEX_RESEND_API_KEY=re_your_actual_api_key_here
FROM_EMAIL=<EMAIL>
FROM_NAME=BH HVAC Services
```

## Verification Steps:

1. **Domain Verification**: Make sure your domain is verified in Resend dashboard
2. **DNS Records**: Ensure SPF, DKIM, and DMARC records are set up
3. **Test Email**: Send a test invoice to verify everything works

## Email Format Result:

With these settings, your invoice emails will be sent as:
- **From**: `BH HVAC Services <<EMAIL>>`
- **Reply-To**: Your company contact email from settings
- **Subject**: `Invoice INV-********** from BH HVAC Services`

## Troubleshooting:

- If you get "Invalid RESEND_API_TOKEN": Check your API key format
- If emails don't send: Verify your domain in Resend dashboard
- If emails go to spam: Set up proper DNS records (SPF, DKIM, DMARC)
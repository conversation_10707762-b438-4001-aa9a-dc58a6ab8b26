# Convex Codebase: Structure and Patterns

## Overview

The codebase uses Convex, a backend platform that combines a database with serverless functions in a unified system. This document outlines the key patterns, conventions, and functionality of the codebase.

## Core Concepts

### 1. Functions Structure

Convex functions are organized into different types:

- **Queries** (`query`): Read-only operations that fetch data
- **Mutations** (`mutation`): Operations that modify data
- **Internal Queries** (`internalQuery`): Private read operations used by other backend functions
- **Internal Mutations** (`internalMutation`): Private write operations used by other backend functions

### 2. Authentication

Authentication is handled through `@convex-dev/auth/server`:

```typescript
import { getAuthUserId } from "@convex-dev/auth/server";

async function getCurrentUser(ctx: any) {
  const userId = await getAuthUserId(ctx);
  if (!userId) throw new Error("Not authenticated");
  return userId;
}
```

This function is used to validate that a user is logged in and to retrieve their ID for permission checks.

### 3. Data Models

Data is organized into tables (collections) like:

- `customers`
- `invoices`

Each table has specific fields and indexing strategies for efficient querying.

### 4. Field Validation

Field validation uses Convex's validation system:

```typescript
import { v } from "convex/values";

// Example field validation
const args = {
  name: v.string(),
  email: v.string(),
  phone: v.string(),
  optional_field: v.optional(v.string()),
}
```

### 5. Common Patterns

#### CRUD Operations

Each data type typically has a complete set of CRUD operations:

- **Create**: Insert new records with validation
- **Read**: Fetch records with permissions checks 
- **Update**: Modify existing records after ownership verification
- **Delete**: Remove records after permissions verification

#### Indexing & Queries

Queries often use indexes for efficient filtering:

```typescript
await ctx.db
  .query("invoices")
  .withIndex("by_created_by", (q) => q.eq("createdBy", userId))
  .order("desc")
  .collect();
```

#### Permissions Model

The system uses an ownership model where users can only access their own data:

```typescript
const invoice = await ctx.db.get(args.id);
if (!invoice || invoice.createdBy !== userId) {
  throw new Error("Invoice not found");
}
```

### 6. Business Logic

#### Calculation Patterns

Financial calculations for invoices follow a consistent pattern:

```typescript
// Calculate subtotal from line items
const subtotal = items.reduce(
  (sum, item) => sum + item.quantity * item.unitPrice, 
  0
);

// Apply tax and discount
const taxAmount = tax ? subtotal * (tax / 100) : 0;
const discountAmount = discount ? subtotal * (discount / 100) : 0;
const total = subtotal + taxAmount - discountAmount;
```

#### Timestamps

Records are timestamped for creation and updates:

```typescript
const now = Date.now();
// For new records
createdAt: now,
updatedAt: now,
// For updates
updatedAt: Date.now(),
```

### 7. Bulk Operations

The system supports bulk operations for data import with validation and error handling:

```typescript
// Example: Bulk import function structure
export const bulkImport = mutation({
  args: {
    items: v.array(/* validation schema */),
  },
  handler: async (ctx, args) => {
    const results = {
      imported: 0,
      skipped: 0,
      errors: [],
    };
    
    for (const item of args.items) {
      try {
        // Process and insert item
        results.imported++;
      } catch (error) {
        results.errors.push(`Error: ${error}`);
        results.skipped++;
      }
    }
    
    return results;
  },
});
```

## Databases & Tables

### Customers Table
- Primary identifier: Convex-generated ID
- Key fields: name, email, phone, address details
- Indexed by: createdBy (for permission filtering)
- Status tracking: "active" by default

### Invoices Table
- Primary identifier: Convex-generated ID
- Key fields: invoiceNumber, customerId, issueDate, dueDate
- Financial fields: items array, subtotal, tax, discount, total
- Status tracking: "draft" by default
- Indexed by: createdBy (for permission filtering)

## Function Categories

### Public API Functions
- Standard CRUD operations for each data type
- Search functionality
- Specialized business operations

### Internal Functions
- Used for cross-table operations or complex internal logic
- Not exposed to client applications

## Security Patterns

1. **Authentication Required**: All operations require authentication
2. **Row-Level Security**: Users can only access their own data
3. **Data Validation**: All inputs are validated before processing
4. **Error Handling**: Errors are caught and reported appropriately

## Conventions

1. **Timestamps**: Created and updated timestamps on all records
2. **User Association**: All data is associated with a creator
3. **Status Tracking**: Records have status fields for workflow state
4. **Soft Validation**: Some validation happens in the backend

## Technical Implementation Notes

1. **Indexes**: Used for efficient querying by owner and other frequently filtered fields
2. **Error Messages**: Generic error messages to prevent information leakage
3. **Batch Operations**: Support for bulk operations with detailed result reporting
4. **Default Values**: Sensible defaults provided for optional fields

This document provides a high-level overview of the system design patterns and conventions used in the Convex backend codebase.
# Header Search Functionality

## Overview

The header search functionality provides a global search capability across the HVAC CRM application, allowing users to quickly find customers, invoices, jobs, and products from anywhere in the application.

## Features

### Desktop Search
- **Location**: Visible search input field in the header (center position)
- **Placeholder**: "Search customers, invoices, jobs..."
- **Real-time suggestions**: Shows results as you type (after 2+ characters)
- **Keyboard navigation**: Use arrow keys to navigate, Enter to select, Escape to close
- **Debounced queries**: 300ms delay to optimize performance

### Mobile Search
- **Location**: Search icon button in the header (visible only on mobile)
- **Overlay interface**: Full-screen search overlay when icon is tapped
- **Touch-optimized**: Larger touch targets and mobile-friendly interface
- **Auto-focus**: Search input automatically focuses when overlay opens

### Search Capabilities
- **Customers**: Search by name, email, company, or phone number
- **Invoices**: Search by invoice number or customer name
- **Jobs**: Search by job title, description, or customer name
- **Products**: Search by name, description, SKU, or category

## Technical Implementation

### Enhanced Navigation System
- **Custom Events**: Enhanced `changeView` events that support both simple navigation and item-specific navigation
- **Auto-Selection**: Each page component (Customers, Invoices, Jobs, Products) automatically selects items when `selectedItemId` is provided
- **State Management**: Proper state management to handle item selection from search navigation

### Convex Queries
- `api.search.quickSearch`: Fast autocomplete suggestions (8 results)
- `api.search.globalSearch`: Comprehensive search across all data types (20 results)

### Performance Optimizations
- **Debouncing**: 300ms delay to reduce API calls
- **Loading states**: Visual feedback during search operations
- **Error handling**: Graceful fallbacks for failed searches
- **Responsive design**: Optimized for both desktop and mobile

### Components
- `HeaderSearch.tsx`: Desktop search component with enhanced navigation
- `MobileSearchOverlay.tsx`: Mobile search overlay with enhanced navigation
- `convex/search.ts`: Backend search queries
- `App.tsx`: Enhanced navigation event handling
- Page components: Auto-selection logic for specific items

## Usage

### Desktop
1. Click on the search input in the header
2. Type at least 2 characters to see suggestions
3. Use arrow keys to navigate through results
4. Press Enter or click on a result to navigate to that item
5. Press Escape to close the search dropdown

### Mobile
1. Tap the search icon (🔍) in the header
2. Type in the search overlay that appears
3. Tap on any result to navigate to that item
4. Tap the back arrow or backdrop to close the search

## Enhanced Navigation Behavior

When a search result is selected, the application automatically navigates to the appropriate view AND selects the specific item:

### Automatic Item Selection
- **Customer results** → Customers page with the specific customer pre-selected and displayed in the detail panel
- **Invoice results** → Invoices page with the specific invoice pre-selected and displayed in the detail view
- **Job results** → Jobs page with the specific job pre-selected and displayed in the detail panel
- **Product results** → Products page with the specific product pre-selected and displayed in the detail view

### Navigation Flow
1. User performs search using header search (desktop) or mobile search overlay
2. User clicks/taps on a search result
3. Application navigates to the appropriate page (customers, invoices, jobs, or products)
4. The specific item is automatically selected and highlighted in the list
5. The item's detail view is immediately displayed in the right panel
6. User can immediately view and interact with the specific item they searched for

## Error Handling

The search functionality includes comprehensive error handling:
- Network errors are caught and logged
- Failed searches return empty results gracefully
- Loading states provide user feedback
- Debouncing prevents excessive API calls

## Accessibility

- **Keyboard navigation**: Full keyboard support for desktop search
- **ARIA labels**: Proper labeling for screen readers
- **Touch targets**: Minimum 44px touch targets on mobile
- **Focus management**: Proper focus handling in overlays

## Testing the Enhanced Navigation

### Desktop Testing
1. **Open the application** at `http://localhost:5173/`
2. **Add some test data** (customers, invoices, jobs, products) if not already present
3. **Test search functionality**:
   - Click on the search input in the header
   - Type a customer name, invoice number, job title, or product name
   - Observe real-time suggestions appearing
   - Click on any search result
4. **Verify navigation**:
   - Application should navigate to the appropriate page
   - The specific item should be automatically selected and highlighted
   - The detail view should be immediately visible in the right panel

### Mobile Testing
1. **Resize browser** to mobile width (< 768px) or use mobile device
2. **Tap the search icon** (🔍) in the header
3. **Test mobile search**:
   - Search overlay should open with auto-focused input
   - Type search terms and observe suggestions
   - Tap on any search result
4. **Verify navigation**:
   - Search overlay should close
   - Application should navigate to the appropriate page
   - The specific item should be automatically selected and displayed

### Test Scenarios
- **Customer Search**: Search for customer name/email → Should open Customers page with customer selected
- **Invoice Search**: Search for invoice number → Should open Invoices page with invoice selected
- **Job Search**: Search for job title → Should open Jobs page with job selected
- **Product Search**: Search for product name/SKU → Should open Products page with product selected

## Future Enhancements

Potential improvements for future versions:
- Search history and recent searches
- Advanced filtering options
- Search result highlighting
- Voice search capability
- Search analytics and insights
- Breadcrumb navigation showing search origin

# Address Autocomplete Setup Guide

## Overview

This guide explains how to set up and configure the address autocomplete functionality in the HVAC CRM application. The system uses Mapbox's Geocoding API to provide real-time address suggestions and automatic population of city, state, and ZIP code fields.

## Prerequisites

1. **Mapbox Account**: Create a free account at [mapbox.com](https://www.mapbox.com/)
2. **API Key**: Generate an API key from your Mapbox account dashboard

## Setup Instructions

### 1. Get Mapbox API Key

1. Go to [Mapbox Account Dashboard](https://account.mapbox.com/access-tokens/)
2. Click "Create a token"
3. Configure the token:
   - **Name**: "HVAC CRM Address Autocomplete"
   - **Scopes**: Select "Geocoding API" (required)
   - **URL restrictions**: Add your domain(s) for production
4. Copy the generated token

### 2. Configure Environment Variables

1. Copy `.env.example` to `.env`:
   ```bash
   cp .env.example .env
   ```

2. Add your Mapbox API key to `.env`:
   ```env
   VITE_MAPBOX_API_KEY=pk.eyJ1IjoieW91ci11c2VybmFtZSIsImEiOiJjbGV4YW1wbGUifQ.example
   ```

### 3. Install Dependencies

The required dependencies are already included in `package.json`:
- `@mapbox/search-js-core`: Mapbox search functionality

If you need to install manually:
```bash
npm install @mapbox/search-js-core
```

## Usage

### Basic Usage

```tsx
import { AddressAutocomplete } from '../components/common/AddressAutocomplete';

function MyForm() {
  const [formData, setFormData] = useState({
    address: '',
    city: '',
    state: '',
    zipCode: ''
  });

  const handleAddressChange = (address: string) => {
    setFormData(prev => ({ ...prev, address }));
  };

  const handleFormDataChange = (addressData: Partial<AddressFormData>) => {
    setFormData(prev => ({ ...prev, ...addressData }));
  };

  return (
    <AddressAutocomplete
      value={formData.address}
      onChange={handleAddressChange}
      onFormDataChange={handleFormDataChange}
      label="Street Address"
      placeholder="Enter street address..."
      required
    />
  );
}
```

### Advanced Usage with Custom Handling

```tsx
import { AddressAutocomplete, ParsedAddress } from '../components/common/AddressAutocomplete';

function AdvancedForm() {
  const handleAddressSelect = (address: ParsedAddress) => {
    console.log('Selected address:', address);
    // Custom logic for handling the selected address
    // address.coordinates contains [longitude, latitude]
  };

  return (
    <AddressAutocomplete
      value={formData.address}
      onChange={handleAddressChange}
      onAddressSelect={handleAddressSelect}
      country="US"
      proximity={[-74.006, 40.7128]} // [longitude, latitude] for NYC
      className="custom-input-class"
    />
  );
}
```

## API Configuration

### Rate Limiting

The service includes built-in rate limiting:
- **Limit**: 100 requests per minute
- **Caching**: 5-minute cache for repeated queries
- **Retry Logic**: Exponential backoff for failed requests

### Error Handling

The component handles various error scenarios:
- **Network errors**: Displays user-friendly messages
- **API errors**: Shows specific error information
- **Rate limiting**: Notifies users when limits are exceeded
- **Invalid responses**: Graceful fallback behavior

### Customization Options

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `value` | `string` | - | Current input value |
| `onChange` | `(value: string) => void` | - | Input change handler |
| `onAddressSelect` | `(address: ParsedAddress) => void` | - | Address selection handler |
| `onFormDataChange` | `(data: Partial<AddressFormData>) => void` | - | Form data update handler |
| `placeholder` | `string` | "Enter street address..." | Input placeholder |
| `disabled` | `boolean` | `false` | Disable the input |
| `className` | `string` | "" | Additional CSS classes |
| `label` | `string` | - | Input label |
| `required` | `boolean` | `false` | Mark as required field |
| `error` | `string` | - | Error message to display |
| `country` | `string` | "US" | Country code for search |
| `proximity` | `[number, number]` | - | [longitude, latitude] for location bias |

## Pricing Information

### Mapbox Pricing (as of 2024)

- **Free Tier**: 100,000 requests per month
- **Paid Tier**: $0.50 per 1,000 requests
- **Session-based billing**: More cost-effective for autocomplete

### Cost Estimation

For a typical HVAC CRM with 50 users:
- **Light usage**: ~5,000 requests/month (well within free tier)
- **Heavy usage**: ~20,000 requests/month (still within free tier)
- **Enterprise usage**: 100,000+ requests/month (~$50/month)

## Security Best Practices

1. **API Key Protection**:
   - Never commit API keys to version control
   - Use environment variables for all configurations
   - Restrict API key usage to specific domains in production

2. **URL Restrictions**:
   - Configure URL restrictions in Mapbox dashboard
   - Limit to your production domains only

3. **Rate Limiting**:
   - Built-in rate limiting prevents abuse
   - Monitor usage in Mapbox dashboard

## Troubleshooting

### Common Issues

1. **"API key not configured" error**:
   - Ensure `VITE_MAPBOX_API_KEY` is set in `.env`
   - Restart the development server after adding the key

2. **No suggestions appearing**:
   - Check browser console for API errors
   - Verify API key has correct permissions
   - Ensure you're typing at least 2 characters

3. **Rate limit exceeded**:
   - Wait for the rate limit to reset (1 minute)
   - Consider implementing user-specific rate limiting

4. **Network errors**:
   - Check internet connection
   - Verify Mapbox service status
   - Check for firewall/proxy issues

### Debug Mode

Enable debug logging by setting:
```env
NODE_ENV=development
```

This will log API requests and responses to the browser console.

## Testing

### Manual Testing Scenarios

1. **Basic functionality**:
   - Type "123 Main St" and verify suggestions appear
   - Select a suggestion and verify fields populate

2. **Error handling**:
   - Disconnect internet and verify error message
   - Use invalid API key and verify error handling

3. **Edge cases**:
   - Test with PO Box addresses
   - Test with international addresses
   - Test with very long address strings

### Automated Testing

```bash
# Run component tests
npm test AddressAutocomplete

# Run integration tests
npm test address-integration
```

## Implementation Details

### Components Updated

The following forms now include address autocomplete functionality:

1. **Customer Form** (`src/components/Customers.tsx`)
   - Address autocomplete for customer addresses
   - Auto-population of city, state, and ZIP fields
   - Maintains existing form validation and styling

2. **Branding Settings Form** (`src/components/BrandingSettings.tsx`)
   - Address autocomplete for business address
   - Auto-population of city, state, and ZIP fields
   - Maintains existing form validation and styling

### Files Added

- `src/services/addressService.ts` - Address service with Mapbox integration
- `src/components/common/AddressAutocomplete.tsx` - Reusable autocomplete component
- `src/components/AddressAutocompleteTest.tsx` - Test component for development
- `docs/ADDRESS_AUTOCOMPLETE_SETUP.md` - This documentation

### Testing

A test component is available for development and testing:

1. **Access the test page**: Navigate to `/address-test` (admin only)
2. **Test scenarios**: Use the built-in test scenarios for different address types
3. **Monitor behavior**: Watch the test log to see component responses
4. **Verify auto-population**: Ensure city, state, and ZIP are correctly filled

### Performance Considerations

- **Debouncing**: 300ms delay reduces API calls
- **Caching**: 5-minute cache for repeated queries
- **Rate limiting**: 100 requests per minute protection
- **Error handling**: Graceful fallbacks for API failures

## Support

For issues related to:
- **Mapbox API**: Check [Mapbox Support](https://docs.mapbox.com/help/)
- **Component bugs**: Create an issue in the project repository
- **Configuration help**: Refer to this documentation or ask the development team

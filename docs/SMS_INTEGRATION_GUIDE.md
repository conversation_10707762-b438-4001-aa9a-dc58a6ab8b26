# SMS Integration Guide for HVAC CRM

This guide covers the complete SMS functionality integration using Twilio through Convex components.

## Overview

The SMS integration provides:
- **Admin-only Twilio configuration** with secure credential management
- **SMS templates** for common HVAC communications
- **SMS history and logging** for audit trails
- **Customer workflow integration** with jobs and invoices
- **Security and error handling** with comprehensive validation

## Table of Contents

1. [Initial Setup](#initial-setup)
2. [Admin Configuration](#admin-configuration)
3. [SMS Templates](#sms-templates)
4. [Sending SMS Messages](#sending-sms-messages)
5. [Security Features](#security-features)
6. [Error Handling](#error-handling)
7. [API Reference](#api-reference)
8. [Troubleshooting](#troubleshooting)

## Initial Setup

### 1. Twilio Account Setup

1. **Create a Twilio Account**
   - Sign up at [twilio.com](https://www.twilio.com)
   - Verify your account and complete setup

2. **Get Your Credentials**
   - Account SID (starts with `AC`, 34 characters)
   - Auth Token (32 characters)
   - Phone Number (E.164 format, e.g., +**********)

3. **Purchase a Phone Number**
   - Go to Phone Numbers → Manage → Buy a number
   - Choose a number that supports SMS
   - Note the number in E.164 format

### 2. Environment Variables (Optional)

You can set environment variables in the Convex dashboard for initial setup:

```
TWILIO_ACCOUNT_SID=ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
TWILIO_AUTH_TOKEN=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
TWILIO_PHONE_NUMBER=+**********
```

**Note:** The application will migrate these to database storage for better management.

## Admin Configuration

### Accessing SMS Settings

1. **Admin Access Required**
   - Only users with `role: "admin"` can configure SMS settings
   - Navigate to SMS → Settings (gear icon)

2. **Configuration Options**
   - **Account SID**: Your Twilio Account SID
   - **Auth Token**: Your Twilio Auth Token (stored securely)
   - **Phone Number**: Your Twilio phone number
   - **Test Mode**: Enable for development/testing

### Configuration Steps

1. **Navigate to SMS Dashboard**
   ```
   Main Menu → SMS → Settings Button
   ```

2. **Enter Credentials**
   - Fill in all required fields
   - Use the "Test Configuration" button to verify

3. **Save Configuration**
   - Click "Save Configuration"
   - Configuration is stored securely in the database

### Migration from Environment Variables

If you have environment variables set, the system will offer to migrate them:

1. **Automatic Detection**
   - System detects existing environment variables
   - Shows migration prompt to admin users

2. **One-Click Migration**
   - Click "Migrate Configuration"
   - Credentials are moved to database storage
   - Environment variables can be removed after migration

## SMS Templates

### Default Templates

The system includes pre-configured templates:

1. **Appointment Reminder**
   ```
   Hi {customerName}, this is a reminder that your HVAC service appointment 
   is scheduled for {appointmentDate} at {appointmentTime}. Our technician 
   {technicianName} will be there. Please call {companyPhone} if you need to reschedule.
   ```

2. **Service Complete**
   ```
   Hello {customerName}, your HVAC service at {customerAddress} has been completed. 
   Thank you for choosing our services! If you have any questions, please call {companyPhone}.
   ```

3. **Invoice Sent**
   ```
   Hi {customerName}, your invoice #{invoiceNumber} for ${invoiceAmount} has been 
   sent to {customerEmail}. Payment is due by {dueDate}. Thank you for your business!
   ```

### Available Variables

- `{customerName}` - Customer's name
- `{companyName}` - Customer's company
- `{customerPhone}` - Customer's phone number
- `{customerEmail}` - Customer's email
- `{customerAddress}` - Full customer address
- `{appointmentDate}` - Scheduled appointment date
- `{appointmentTime}` - Scheduled appointment time
- `{jobTitle}` - Job title/description
- `{technicianName}` - Assigned technician name
- `{invoiceNumber}` - Invoice number
- `{invoiceAmount}` - Invoice total amount
- `{dueDate}` - Invoice due date
- `{companyPhone}` - Company phone number
- `{companyEmail}` - Company email

### Creating Custom Templates

1. **Admin Access Required**
   - Navigate to SMS → Templates
   - Click "Create Template"

2. **Template Configuration**
   - **Name**: Descriptive template name
   - **Category**: appointment, service, invoice, or general
   - **Content**: Message content with variables
   - **Description**: Optional description

3. **Variable Insertion**
   - Click on available variables to insert them
   - Variables are automatically detected and validated

## Sending SMS Messages

### From Customer View

1. **SMS Button Integration**
   - SMS button appears next to customer phone numbers
   - Only visible if SMS is configured and customer has phone

2. **Quick Send**
   - Click SMS button → Opens composer
   - Select template or write custom message
   - Send immediately

### From Job/Invoice Views

1. **Contextual SMS**
   - SMS buttons in job and invoice detail views
   - Automatically includes job/invoice context
   - Template variables are pre-populated

2. **Template Selection**
   - Choose from relevant templates
   - Variables are automatically filled from context
   - Preview message before sending

### SMS Composer Features

1. **Template Integration**
   - Browse and select from available templates
   - Variables are automatically replaced
   - Preview final message

2. **Message Validation**
   - Character count (1600 max)
   - SMS segment estimation
   - Security content validation

3. **Context Information**
   - Shows related job/invoice details
   - Customer information display
   - Send history for context

## Security Features

### Content Security

1. **Suspicious Pattern Detection**
   - URLs and links monitoring
   - Financial terms detection
   - Urgency/pressure tactics identification
   - Suspicious request patterns

2. **Risk Levels**
   - **Low**: Normal business content
   - **Medium**: Contains some flagged patterns
   - **High**: Multiple suspicious patterns (blocked)

### Phone Number Validation

1. **Format Validation**
   - E.164 format enforcement
   - Country code validation
   - Length validation (7-15 digits)

2. **Security Checks**
   - Repeated digit detection
   - Sequential number detection
   - Premium rate number blocking
   - Test number identification

### Credential Security

1. **Secure Storage**
   - Credentials stored in database (encrypted in production)
   - Environment variable migration
   - Admin-only access

2. **Access Control**
   - Role-based permissions
   - Admin verification for sensitive operations
   - Audit logging for all actions

## Error Handling

### Error Categories

1. **Validation Errors**
   - Invalid phone numbers
   - Empty messages
   - Content too long
   - Suspicious content

2. **Authentication Errors**
   - Invalid Twilio credentials
   - Unauthorized access
   - Admin privileges required

3. **Service Errors**
   - Twilio API errors
   - Network connectivity issues
   - Service unavailable

4. **Rate Limiting**
   - Too many messages per minute/hour/day
   - Twilio rate limits
   - Account limits exceeded

### Error Recovery

1. **Automatic Retry**
   - Retryable errors are automatically retried
   - Exponential backoff strategy
   - Maximum retry attempts

2. **User Guidance**
   - Clear error messages
   - Suggested actions
   - Escalation guidance when needed

3. **Admin Notifications**
   - Critical errors notify administrators
   - Configuration issues highlighted
   - Service status monitoring

## API Reference

### Core Functions

```typescript
// Send SMS to customer
api.sms.sendToCustomer({
  customerId: "customer_id",
  message: "Your message",
  jobId?: "job_id",
  invoiceId?: "invoice_id"
})

// Send with template
api.sms.sendWithTemplate({
  customerId: "customer_id",
  templateId: "template_id",
  variables: { customerName: "John Doe" },
  jobId?: "job_id",
  invoiceId?: "invoice_id"
})

// Get SMS history
api.sms.getCustomerHistory({ customerId: "customer_id" })
api.sms.getMyHistory({ limit: 50 })
api.sms.getAllHistory({ limit: 100 }) // Admin only
```

### Template Management

```typescript
// List templates
api.smsTemplates.list({ category?: "appointment", activeOnly?: true })

// Create template (Admin only)
api.smsTemplates.create({
  name: "Template Name",
  content: "Message with {variables}",
  category: "appointment",
  description: "Optional description"
})

// Update template (Admin only)
api.smsTemplates.update({
  id: "template_id",
  name?: "New Name",
  content?: "New content",
  isActive?: true
})
```

### Configuration Management

```typescript
// Get configuration status
api.twilioConfig.getConfigStatus()

// Save configuration (Admin only)
api.twilioConfig.saveConfig({
  accountSid: "AC...",
  authToken: "...",
  phoneNumber: "+**********",
  testMode?: false
})

// Test configuration (Admin only)
api.twilioConfig.testConfig({
  accountSid: "AC...",
  authToken: "...",
  phoneNumber: "+**********"
})
```

## Troubleshooting

### Common Issues

1. **SMS Not Configured**
   - **Symptom**: No SMS buttons visible
   - **Solution**: Admin must configure Twilio credentials

2. **Invalid Phone Number**
   - **Symptom**: "Invalid phone number" error
   - **Solution**: Ensure E.164 format (+**********)

3. **Authentication Failed**
   - **Symptom**: "Invalid credentials" error
   - **Solution**: Verify Twilio Account SID and Auth Token

4. **Message Not Delivered**
   - **Symptom**: SMS shows "sent" but not "delivered"
   - **Solution**: Check recipient phone number and carrier

5. **Rate Limit Exceeded**
   - **Symptom**: "Too many messages" error
   - **Solution**: Wait before sending more messages

### Debug Steps

1. **Check Configuration**
   ```
   SMS Dashboard → Settings → Configuration Status
   ```

2. **Test Credentials**
   ```
   SMS Settings → Test Configuration
   ```

3. **Review Error Logs**
   ```
   SMS Dashboard → History → Filter by "failed"
   ```

4. **Verify Phone Numbers**
   ```
   Customer Details → Phone Number Format
   ```

### Support

For additional support:
1. Check SMS history for error details
2. Review Twilio console for delivery status
3. Contact system administrator for configuration issues
4. Refer to Twilio documentation for service-specific issues

## Quick Start Checklist

### For Administrators

- [ ] Create Twilio account and verify
- [ ] Purchase SMS-capable phone number
- [ ] Note Account SID, Auth Token, and Phone Number
- [ ] Access HVAC CRM as admin user
- [ ] Navigate to SMS → Settings
- [ ] Enter Twilio credentials
- [ ] Test configuration
- [ ] Save configuration
- [ ] Initialize default templates
- [ ] Test sending SMS to a known number
- [ ] Train team on SMS features

### For Users

- [ ] Verify SMS is configured (SMS menu item visible)
- [ ] Find customer with phone number
- [ ] Click SMS button next to customer
- [ ] Select template or write custom message
- [ ] Send test message
- [ ] Check SMS history for delivery status
- [ ] Report any issues to administrator

## Security Considerations

1. **Credential Management**
   - Store credentials securely in database
   - Limit admin access to configuration
   - Regularly rotate Twilio Auth Tokens

2. **Content Monitoring**
   - Review SMS content for compliance
   - Monitor for suspicious patterns
   - Implement approval workflows if needed

3. **Rate Limiting**
   - Monitor daily/monthly SMS usage
   - Set up Twilio usage alerts
   - Implement business rules for SMS frequency

4. **Audit Trail**
   - All SMS messages are logged
   - Admin actions are tracked
   - Regular audit reviews recommended

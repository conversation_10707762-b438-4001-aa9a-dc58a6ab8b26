# Schema Update Required

The schema.ts file has syntax errors (missing commas). Here's what needs to be fixed:

## Current Issues:
- Missing commas in the invoices table definition
- Missing commas in index definitions

## Required Changes:

Add these fields to the invoices table:
```typescript
pdfStorageId: v.optional(v.string()), // Storage ID for generated PDF
htmlStorageId: v.optional(v.string()), // Storage ID for HTML version (legacy)
```

## Manual Fix Required:
Please manually add commas to fix the syntax errors in schema.ts, then add the PDF storage fields.

The PDF email system is ready to use once the schema is fixed!
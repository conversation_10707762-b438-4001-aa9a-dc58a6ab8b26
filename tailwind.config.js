module.exports = {
  mode: "jit",
  content: ["./index.html", "./src/**/*.{vue,js,ts,jsx,tsx}"],
  darkMode: 'class',
  theme: {
    extend: {
      fontFamily: {
        sans: ["Inter", "ui-sans-serif", "system-ui", "-apple-system", "BlinkMacSystemFont", "Segoe UI", "Roboto", "Helvetica Neue", "Arial", "Noto Sans", "sans-serif"],
      },
      borderRadius: {
        DEFAULT: "8px",
        secondary: "4px",
        container: "12px",
      },
      boxShadow: {
        DEFAULT: "0 1px 4px rgba(0, 0, 0, 0.1)",
        hover: "0 2px 8px rgba(0, 0, 0, 0.12)",
        'dark': "0 1px 4px rgba(0, 0, 0, 0.3)",
        'dark-hover': "0 2px 8px rgba(0, 0, 0, 0.4)",
      },
      colors: {
        // Theme-aware colors using CSS variables
        bg: {
          DEFAULT: "rgb(var(--color-bg) / <alpha-value>)",
          surface: "rgb(var(--color-bg-surface) / <alpha-value>)",
        },
        text: {
          DEFAULT: "rgb(var(--color-text) / <alpha-value>)",
          muted: "rgb(var(--color-text-muted) / <alpha-value>)",
        },
        border: "rgb(var(--color-border) / <alpha-value>)",
        primary: {
          DEFAULT: "rgb(var(--color-primary) / <alpha-value>)",
          hover: "rgb(var(--color-primary-hover) / <alpha-value>)",
        },
        secondary: {
          DEFAULT: "#6B7280",
          hover: "#4B5563",
        },
        accent: {
          DEFAULT: "rgb(var(--color-accent) / <alpha-value>)",
          hover: "rgb(var(--color-accent) / 0.9)",
        },
        charcoal: "rgb(var(--color-charcoal) / <alpha-value>)",
        neutral: "rgb(var(--color-neutral) / <alpha-value>)",
        
        // Enhanced dark theme colors
        dark: {
          bg: "#1a1a1a",
          surface: "#2d2d2d",
          text: "#ffffff",
          muted: "#a0a0a0",
          border: "#3d3d3d",
          primary: {
            DEFAULT: "#4f9cf5",
            hover: "#3a8ae6",
          },
          accent: {
            DEFAULT: "#9c6bf5",
            hover: "#8a5ae6",
          },
        },
      },
      spacing: {
        "form-field": "16px",
        section: "32px",
        "18": "4.5rem", // 72px for larger logo sizes
      },
    },
  },
  variants: {
    extend: {
      boxShadow: ["hover", "active", "dark"],
    },
  },
};

import { Authenticated, Unauthenticated, useQuery } from "convex/react";
import { api } from "../convex/_generated/api";
import { SignInForm } from "./SignInForm";
import { Toaster } from "sonner";
import { Navigation } from "./components/Navigation";
import { Header } from "./components/Header";
import { Dashboard } from "./components/Dashboard";
import { Customers } from "./components/Customers";
import { Jobs } from "./components/Jobs";
import { Products } from "./components/Products";
import { Invoices } from "./components/Invoices";
import { UserProfile } from "./components/UserProfile";
import { Settings } from "./components/Settings/Settings";
import { PDFTestComponent } from "./components/PDFTestComponent";
import { SMSDashboard } from "./components/SMS/SMSDashboard";
import { Conversations } from "./components/Conversations/Conversations";
import { UserManagement } from "./components/UserManagement/UserManagement";
import { AdminApprovalDashboard } from "./components/AdminApproval/AdminApprovalDashboard";
import { AddressAutocompleteTest } from "./components/AddressAutocompleteTest";
import { UserOnboardingModal } from "./components/UserOnboarding/UserOnboardingModal";
import { OnboardingTest } from "./components/UserOnboarding/OnboardingTest";
import { useState, useEffect } from "react";

// Main application component with navigation and routing
export default function App() {
  return (
    <div className="min-h-screen flex flex-col bg-neutral transition-colors duration-300">
      <main className="flex-1 flex flex-col">
        <Content />
      </main>
      <Toaster position="top-center" />
    </div>
  );
}

// Content component that handles authentication state
function Content() {
  const loggedInUser = useQuery(api.auth.loggedInUser);
  const profileStatus = useQuery(api.users.isProfileComplete);
  const [currentPage, setCurrentPage] = useState("dashboard");
  const [selectedItemId, setSelectedItemId] = useState<string | null>(null);
  const [showOnboarding, setShowOnboarding] = useState(false);

  // Add event listener for navigation events
  useEffect(() => {
    const handleViewChange = (event: CustomEvent) => {
      if (event.detail) {
        if (typeof event.detail === 'string') {
          // Simple navigation without item selection
          setCurrentPage(event.detail);
          setSelectedItemId(null);
        } else if (event.detail.page && event.detail.itemId) {
          // Enhanced navigation with item selection
          setCurrentPage(event.detail.page);
          setSelectedItemId(event.detail.itemId);
        }
      }
    };

    // Add event listener
    window.addEventListener('changeView', handleViewChange as EventListener);

    // Clean up
    return () => {
      window.removeEventListener('changeView', handleViewChange as EventListener);
    };
  }, []);

  // Check if onboarding should be shown
  useEffect(() => {
    if (loggedInUser && profileStatus && !profileStatus.isComplete) {
      setShowOnboarding(true);
    }
  }, [loggedInUser, profileStatus]);

  // Loading state while checking authentication and profile
  if (loggedInUser === undefined || profileStatus === undefined) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <div className="spinner h-8 w-8"></div>
      </div>
    );
  }

  const handleOnboardingComplete = () => {
    setShowOnboarding(false);
  };

  return (
    <div className="flex flex-col h-full min-h-screen">
      <Authenticated>
        <div className="flex flex-col h-full min-h-screen">
          {/* Header */}
          <Header />

          {/* Main Layout Container - Fixed height and proper flex distribution */}
          <div className="flex flex-1 h-full overflow-hidden">
            {/* Sidebar Navigation - Fixed, never scrolls */}
            <div className="w-64 lg:w-72 xl:w-80 h-full overflow-hidden">
              <Navigation
                activeView={currentPage}
                onViewChange={setCurrentPage}
              />
            </div>

            {/* Main Content Area with proper scrolling */}
            <div className="flex-1 overflow-y-auto overflow-x-hidden bg-neutral h-full">
              <div className="p-4 md:p-6">
                <PageContent currentPage={currentPage} selectedItemId={selectedItemId} />
              </div>
            </div>
          </div>
        </div>

        {/* User Onboarding Modal */}
        {showOnboarding && loggedInUser && profileStatus && (
          <UserOnboardingModal
            user={profileStatus.user}
            onComplete={handleOnboardingComplete}
          />
        )}
      </Authenticated>

      <Unauthenticated>
        <div className="flex items-center justify-center min-h-screen px-4">
          <div className="w-full max-w-md mx-auto p-6 md:p-8">
            <div className="text-center mb-6 md:mb-8">
              <img src="images/logo.png" alt="Logo" className="h-25 mb-4" />
              <h1 className="text-3xl md:text-4xl font-bold text-primary mb-4">Bernie's Heating CRM</h1>
              <p className="text-gray-500">Please sign in to access</p>
            </div>
            <SignInForm />
          </div>
        </div>
      </Unauthenticated>
    </div>
  );
}

// Page content router component
function PageContent({ currentPage, selectedItemId }: { currentPage: string; selectedItemId: string | null }) {
  const user = useQuery(api.auth.loggedInUser);
  const isAdmin = user?.role === 'admin' || user?.role === 'master';
  const isMaster = user?.role === 'master';

  switch (currentPage) {
    case "dashboard":
      return <Dashboard />;
    case "customers":
      return <Customers selectedItemId={selectedItemId} />;
    case "jobs":
      return <Jobs selectedItemId={selectedItemId} />;
    case "products":
      return <Products selectedItemId={selectedItemId} />;
    case "invoices":
      return <Invoices selectedItemId={selectedItemId} />;
    case "conversations":
      // Conversations are available to all authenticated users
      return <Conversations />;
    case "sms":
      // Only show SMS dashboard for admin users
      return isAdmin ? <SMSDashboard /> : <Dashboard />;
    case "user-management":
      // Only show user management for master users
      return isMaster ? <UserManagement /> : <Dashboard />;
    case "admin-approvals":
      // Only show admin approvals for master users
      return isMaster ? <AdminApprovalDashboard /> : <Dashboard />;
    case "profile":
      return <UserProfile />;
    case "settings":
      // Only show settings for admin users
      return isAdmin ? <Settings /> : <Dashboard />;
    case "pdf-test":
      // PDF testing page for development (admin only)
      return isAdmin ? <PDFTestComponent /> : <Dashboard />;
    case "address-test":
      // Address autocomplete testing page for development (admin only)
      return isAdmin ? <AddressAutocompleteTest /> : <Dashboard />;
    case "onboarding-test":
      // Onboarding testing page for development (admin only)
      return isAdmin ? <OnboardingTest /> : <Dashboard />;
    default:
      return <Dashboard />;
  }
}
import { api } from "../../convex/_generated/api";
import { useMutation, useQuery, useConvex } from "convex/react";

// Parse CSV string into array of objects
export function parseCSV(csvString: string): any[] {
  // Handle possible BOM character at the start of the file
  const cleanedCSV = csvString.replace(/^\uFEFF/, '');

  // Split the CSV into lines, but handle quoted newlines properly
  const lines: string[] = [];
  let currentLine = '';
    let inQuotes = false;
    
  for (let i = 0; i < cleanedCSV.length; i++) {
    const char = cleanedCSV[i];
    const nextChar = cleanedCSV[i + 1];
      
      if (char === '"') {
      // Toggle quote state
        inQuotes = !inQuotes;
      currentLine += char;
    } else if ((char === '\r' && nextChar === '\n') || char === '\n') {
      // Handle newline characters
      if (!inQuotes) {
        // End of line outside quotes - push the current line
        lines.push(currentLine);
        currentLine = '';
        if (char === '\r') {
          i++; // Skip the \n in \r\n
      }
      } else {
        // Newline inside quotes - preserve it
        currentLine += char;
      }
    } else {
      currentLine += char;
  }
}

  // Don't forget the last line
  if (currentLine) {
    lines.push(currentLine);
  }

  if (lines.length < 2) return [];

  // Get headers (first line)
  const headers = parseCSVLine(lines[0]);

  const results = [];

  // Process each data row
  for (let i = 1; i < lines.length; i++) {
    if (!lines[i].trim()) continue; // Skip empty lines

    const values = parseCSVLine(lines[i]);

    // Skip rows with incorrect number of fields
    if (values.length === 0) continue;

    // Create an object from the headers and values
    const obj: Record<string, string> = {};
    headers.forEach((header, index) => {
      if (index < values.length) {
        obj[header] = values[index];
      } else {
        obj[header] = '';
      }
    });
    
    results.push(obj);
  }
  
  return results;
}

// Helper function to parse a single CSV line into values
function parseCSVLine(line: string): string[] {
  const values: string[] = [];
  let currentValue = '';
  let inQuotes = false;

  for (let i = 0; i < line.length; i++) {
    const char = line[i];

    if (char === '"') {
      if (!inQuotes) {
        // Starting a quoted field
        inQuotes = true;
      } else if (i + 1 < line.length && line[i + 1] === '"') {
        // Escaped quote inside a quoted field
        currentValue += '"';
        i++; // Skip the next quote
      } else {
        // Ending a quoted field
        inQuotes = false;
      }
    } else if (char === ',' && !inQuotes) {
      // End of value
      values.push(currentValue);
      currentValue = '';
    } else {
      currentValue += char;
    }
  }

  // Add the last value
  values.push(currentValue);

  // Clean up values - remove surrounding quotes
  return values.map(value => {
    if (value.startsWith('"') && value.endsWith('"')) {
      return value.substring(1, value.length - 1);
    }
    return value;
  });
}

// Format parsed CSV data for the bulkImport mutation
export function formatCustomersForImport(parsedCustomers: any[]): any[] {
  return parsedCustomers.map(customer => {
    // Extract known fields
    const knownFields = {
      id: customer.id || undefined,
      name: customer.name || 'Unknown',
      email: customer.email || '', // Allow empty email
      phone: customer.phone || '',
      address: customer.address || '',
      city: customer.city || '',
      state: customer.state || '',
      zipCode: customer.zip_code || customer.zipCode || '',
    };

    // Extract all other fields into an additionalFields object
    const additionalFields: Record<string, any> = {};
    const knownFieldNames = [
      'id', 'name', 'email', 'phone', 'address', 'city', 'state', 'zip_code', 'zipCode'
    ];

    // Store all other columns as additional fields
    Object.keys(customer).forEach(key => {
      if (!knownFieldNames.includes(key) && customer[key]) {
        additionalFields[key] = customer[key];
      }
    });

    // Create notes by combining existing notes with JSON representation of additional fields
    let notesContent = customer.notes || '';

    // Only add additional fields section if there are any
    if (Object.keys(additionalFields).length > 0) {
      notesContent += '\n\n--- Additional Fields ---\n';
      for (const [key, value] of Object.entries(additionalFields)) {
        notesContent += `${key}: ${value}\n`;
      }
    }
    return {
      ...knownFields,
      notes: notesContent.trim(),
      // Include original additional fields for use in UI
      additionalFields
    };
  });
}

// Helper function to extract additional fields from notes
export function extractAdditionalFields(notes: string): Record<string, string> {
  const additionalFields: Record<string, string> = {};

  if (!notes) return additionalFields;

  // Look for the marker that indicates additional fields
  const markerIndex = notes.indexOf('--- Additional Fields ---');
  if (markerIndex === -1) return additionalFields;

  // Extract the section after the marker
  const additionalFieldsSection = notes.substring(markerIndex + '--- Additional Fields ---'.length).trim();

  // Split into lines and parse each key-value pair
  additionalFieldsSection.split('\n').forEach(line => {
    const colonIndex = line.indexOf(':');
    if (colonIndex !== -1) {
      const key = line.substring(0, colonIndex).trim();
      const value = line.substring(colonIndex + 1).trim();
      if (key && value) {
        additionalFields[key] = value;
      }
    }
  });

  return additionalFields;
}

// React hook to use the CSV import functionality
export function useCustomerImport() {
  const bulkImport = useMutation(api.customers.bulkImport);
  const importCustomers = async (csvString: string) => {
    try {
      // Parse CSV
      const parsedCustomers = parseCSV(csvString);
      if (parsedCustomers.length === 0) {
        throw new Error("No customers found in CSV");
      }
      
      console.log(`Importing ${parsedCustomers.length} customers from CSV`);

      // Format data for import
      const formattedCustomers = formatCustomersForImport(parsedCustomers);
      
      // Log import data for debugging
      console.log(`Formatted ${formattedCustomers.length} customers for import`);

      // Perform the import
      const importResult = await bulkImport({ customers: formattedCustomers });
      console.log("Import result:", importResult);
      return {
          success: true,
          ...importResult,
          totalProcessed: formattedCustomers.length,
        };
    } catch (error) {
      console.error("Import error:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error occurred",
      };
    }
  };
  
  return { importCustomers };
}

// React hook to check import status
export function useCustomerImportCheck() {
  // Create a function to manually call the checkImport query
  // We'll use a one-time query (not a subscription)
  const convex = useConvex();
  const checkCustomersImport = async (csvString: string) => {
    try {
      // Parse CSV
      const parsedCustomers = parseCSV(csvString);
      if (parsedCustomers.length === 0) {
        throw new Error("No customers found in CSV");
      }

      // Format data for import
      const formattedCustomers = formatCustomersForImport(parsedCustomers);

      // Call the query directly using the Convex client
      const result = await convex.query(api.customers.checkImport, {
        customers: formattedCustomers.map(c => ({
          id: c.id,
          name: c.name,
          email: c.email
        }))
      });
      return {
        success: true,
        ...result
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error occurred",
      };
    }
  };

  return { checkCustomersImport };
}
import jsPDF from 'jspdf';
import { Doc } from "../../convex/_generated/dataModel";

// Use Convex generated types for better type safety
type Invoice = Doc<"invoices">;
type Customer = Doc<"customers">;
type CompanySettings = Doc<"settings">;

// Enhanced invoice interface that includes customer data
interface InvoiceWithCustomer extends Invoice {
  customer?: Customer;
}

// Legacy interfaces for backward compatibility
interface InvoiceItem {
  description: string;
  quantity: number;
  unitPrice: number;
  total?: number; // Optional for backward compatibility
}

interface LayoutConfig {
  margin: number;
  baseSpacing: number;
  itemCount: number;
  hasNotes: boolean;
  pageWidth: number;
  pageHeight: number;
}

interface FontSizes {
  header: number;
  detail: number;
  billTo: number;
  customer: number;
  tableHeader: number;
  table: number;
  totals: number;
  notes: number;
}

// Helper function to add text with dynamic sizing and wrapping
function addText(
  doc: jsPDF,
  text: string,
  x: number,
  y: number,
  config: LayoutConfig,
  options?: any
): number {
  const maxWidth = options?.maxWidth || config.pageWidth - 2 * config.margin;
  const lines = doc.splitTextToSize(text, maxWidth);
  doc.text(lines, x, y, options);
  const lineHeight = options?.lineHeight || (config.itemCount > 8 ? 4 : 5);
  return y + (lines.length * lineHeight);
}

function calculateLayoutConfig(invoice: InvoiceWithCustomer, pageWidth: number, pageHeight: number): LayoutConfig {
  const itemCount = invoice.items?.length || 0;
  const hasNotes = Boolean(invoice.notes && invoice.notes.trim());
  const margin = itemCount > 8 ? 12 : 15;
  const baseSpacing = itemCount > 10 ? 2 : 3;

  return {
    margin,
    baseSpacing,
    itemCount,
    hasNotes,
    pageWidth,
    pageHeight
  };
}

function calculateFontSizes(itemCount: number): FontSizes {
  return {
    header: itemCount > 8 ? 16 : 18,
    detail: itemCount > 8 ? 9 : 10,
    billTo: itemCount > 8 ? 10 : 12,
    customer: itemCount > 8 ? 8 : 9,
    tableHeader: itemCount > 8 ? 9 : 10,
    table: itemCount > 10 ? 8 : itemCount > 6 ? 9 : 10,
    totals: itemCount > 8 ? 8 : 9,
    notes: itemCount > 8 ? 7 : 8
  };
}

function calculateReservedSpace(config: LayoutConfig, companyInfo?: any) {
  const reservedSpace = {
    header: 45,
    customer: 35,
    totals: 35,
    notes: config.hasNotes ? 25 : 0,
    footer: companyInfo?.invoiceFooter ? 20 : 10,
    margins: config.margin * 2
  };

  return {
    ...reservedSpace,
    total: Object.values(reservedSpace).reduce((sum, space) => sum + space, 0)
  };
}

function renderHeader(
  doc: jsPDF,
  invoice: InvoiceWithCustomer,
  companyInfo: CompanySettings | undefined,
  config: LayoutConfig,
  fontSizes: FontSizes,
  yPosition: number
): number {
  // Company Info (Left Side)
  doc.setFontSize(fontSizes.header);
  doc.setFont('helvetica', 'bold');
  yPosition = addText(doc, companyInfo?.companyName || 'Your Company Name', config.margin, yPosition, config, { lineHeight: 6 });

  doc.setFontSize(fontSizes.detail);
  doc.setFont('helvetica', 'normal');

  // Compact company details
  const companyDetails = [];
  if (companyInfo?.address) companyDetails.push(companyInfo.address);
  if (companyInfo?.city && companyInfo?.state && companyInfo?.zipCode) {
    companyDetails.push(`${companyInfo.city}, ${companyInfo.state} ${companyInfo.zipCode}`);
  }
  if (companyInfo?.contactPhone) companyDetails.push(`Phone: ${companyInfo.contactPhone}`);
  if (companyInfo?.contactEmail) companyDetails.push(`Email: ${companyInfo.contactEmail}`);

  companyDetails.forEach(detail => {
    yPosition = addText(doc, detail, config.margin, yPosition + config.baseSpacing, config, { lineHeight: 4 });
  });

  // Invoice Details (Right Side)
  let rightYPosition = config.margin;

  doc.setFontSize(20);
  doc.setFont('helvetica', 'bold');
  doc.text('INVOICE', config.pageWidth - config.margin, rightYPosition, { align: 'right' });

  doc.setFontSize(fontSizes.detail);
  doc.setFont('helvetica', 'normal');
  rightYPosition += 15;
  doc.text(`#${invoice.invoiceNumber}`, config.pageWidth - config.margin, rightYPosition, { align: 'right' });
  rightYPosition += 10;
  doc.text(`Date: ${new Date(invoice.issueDate).toLocaleDateString()}`, config.pageWidth - config.margin, rightYPosition, { align: 'right' });
  rightYPosition += 10;
  doc.text(`Due: ${new Date(invoice.dueDate).toLocaleDateString()}`, config.pageWidth - config.margin, rightYPosition, { align: 'right' });

  // Status
  const statusColor = getStatusColor(invoice.status);
  doc.setTextColor(statusColor.r, statusColor.g, statusColor.b);
  doc.setFont('helvetica', 'bold');
  rightYPosition += 10;
  doc.text(invoice.status.toUpperCase(), config.pageWidth - config.margin, rightYPosition, { align: 'right' });
  doc.setTextColor(0, 0, 0);

  return Math.max(yPosition + 8, rightYPosition + 8);
}

function renderCustomerSection(
  doc: jsPDF,
  invoice: InvoiceWithCustomer,
  config: LayoutConfig,
  fontSizes: FontSizes,
  yPosition: number
): number {
  doc.setFontSize(fontSizes.billTo);
  doc.setFont('helvetica', 'bold');
  yPosition = addText(doc, 'Bill To:', config.margin, yPosition + 8, config, { lineHeight: 5 });

  doc.setFontSize(fontSizes.customer);
  doc.setFont('helvetica', 'normal');

  if (invoice.customer) {
    yPosition = addText(doc, invoice.customer.name || 'Unknown Customer', config.margin, yPosition + 3, config, { lineHeight: 4 });

    const customerDetails = [];
    if (invoice.customer.email) customerDetails.push(invoice.customer.email);
    if (invoice.customer.phone) customerDetails.push(invoice.customer.phone);
    if (invoice.customer.address) customerDetails.push(invoice.customer.address);
    if (invoice.customer.city && invoice.customer.state && invoice.customer.zipCode) {
      customerDetails.push(`${invoice.customer.city}, ${invoice.customer.state} ${invoice.customer.zipCode}`);
    }

    customerDetails.forEach(detail => {
      yPosition = addText(doc, detail, config.margin, yPosition + 2, config, { lineHeight: 3 });
    });
  }

  return yPosition + (config.itemCount > 8 ? 8 : 12);
}

interface TableColumns {
  descriptionX: number;
  descriptionWidth: number;
  quantityX: number;
  priceX: number;
  totalX: number;
}

function calculateTableColumns(config: LayoutConfig): TableColumns {
  return {
    descriptionX: config.margin,
    descriptionWidth: config.pageWidth - 110,
    quantityX: config.pageWidth - 85,
    priceX: config.pageWidth - 60,
    totalX: config.pageWidth - config.margin
  };
}

function renderTableHeader(doc: jsPDF, config: LayoutConfig, fontSizes: FontSizes, columns: TableColumns, yPosition: number): number {
  doc.setFontSize(fontSizes.tableHeader);
  doc.setFont('helvetica', 'bold');

  doc.text('Description', columns.descriptionX, yPosition);
  doc.text('Qty', columns.quantityX, yPosition, { align: 'center' });
  doc.text('Price', columns.priceX, yPosition, { align: 'center' });
  doc.text('Total', columns.totalX, yPosition, { align: 'right' });

  doc.setLineWidth(0.5);
  doc.line(config.margin, yPosition + 2, config.pageWidth - config.margin, yPosition + 2);

  return yPosition + 6;
}

function calculateItemSpacing(itemCount: number): number {
  if (itemCount > 10) return 1;
  if (itemCount > 6) return 2;
  return 3;
}

function truncateDescription(description: string, maxDescriptionWidth: number, maxItemHeight: number, doc: jsPDF): string[] {
  let descriptionLines = doc.splitTextToSize(description, maxDescriptionWidth);
  const maxLines = Math.floor(maxItemHeight / 4);

  if (descriptionLines.length > maxLines && maxLines > 0) {
    const truncatedText = description.substring(0, Math.floor(description.length * (maxLines / descriptionLines.length))) + '...';
    descriptionLines = doc.splitTextToSize(truncatedText, maxDescriptionWidth);
    if (descriptionLines.length > maxLines) {
      descriptionLines = descriptionLines.slice(0, maxLines);
    }
  }

  return descriptionLines;
}

function renderItemDescription(
  doc: jsPDF,
  item: InvoiceItem,
  config: LayoutConfig,
  columns: TableColumns,
  maxItemHeight: number,
  reservedSpace: any,
  yPosition: number,
  itemSpacing: number
): void {
  const maxDescriptionWidth = columns.descriptionWidth - 5;
  const descriptionLines = truncateDescription(item.description, maxDescriptionWidth, maxItemHeight, doc);

  const lineHeight = config.itemCount > 10 ? 3 : 4;
  const itemHeight = Math.max(lineHeight, descriptionLines.length * lineHeight);

  const remainingSpace = config.pageHeight - yPosition - reservedSpace.totals - reservedSpace.notes - reservedSpace.footer;
  if (remainingSpace < itemHeight + itemSpacing) {
    const singleLineDesc = item.description.length > 40 ? item.description.substring(0, 37) + '...' : item.description;
    doc.text(singleLineDesc, columns.descriptionX, yPosition);
  } else {
    doc.text(descriptionLines, columns.descriptionX, yPosition);
  }
}

function renderSingleItem(
  doc: jsPDF,
  item: InvoiceItem,
  config: LayoutConfig,
  columns: TableColumns,
  maxItemHeight: number,
  reservedSpace: any,
  yPosition: number
): number {
  const itemSpacing = calculateItemSpacing(config.itemCount);

  renderItemDescription(doc, item, config, columns, maxItemHeight, reservedSpace, yPosition, itemSpacing);

  doc.text(item.quantity.toString(), columns.quantityX, yPosition, { align: 'center' });
  doc.text(`$${item.unitPrice.toFixed(2)}`, columns.priceX, yPosition, { align: 'center' });
  doc.text(`$${(item.quantity * item.unitPrice).toFixed(2)}`, columns.totalX, yPosition, { align: 'right' });

  const lineHeight = config.itemCount > 10 ? 3 : 4;
  const maxDescriptionWidth = columns.descriptionWidth - 5;
  const descriptionLines = truncateDescription(item.description, maxDescriptionWidth, maxItemHeight, doc);
  const itemHeight = Math.max(lineHeight, descriptionLines.length * lineHeight);

  return yPosition + Math.min(itemHeight, maxItemHeight) + itemSpacing;
}

function renderItemsTable(
  doc: jsPDF,
  invoice: InvoiceWithCustomer,
  config: LayoutConfig,
  fontSizes: FontSizes,
  reservedSpace: any,
  maxItemHeight: number,
  yPosition: number
): number {
  const columns = calculateTableColumns(config);
  yPosition = renderTableHeader(doc, config, fontSizes, columns, yPosition);

  doc.setFontSize(fontSizes.table);
  doc.setFont('helvetica', 'normal');

  if (invoice.items && invoice.items.length > 0) {
    invoice.items.forEach((item) => {
      yPosition = renderSingleItem(doc, item, config, columns, maxItemHeight, reservedSpace, yPosition);
    });
  }

  return yPosition;
}

function renderTotalsSection(
  doc: jsPDF,
  invoice: InvoiceWithCustomer,
  config: LayoutConfig,
  fontSizes: FontSizes,
  yPosition: number
): number {
  yPosition += config.itemCount > 8 ? 4 : 6;
  doc.setLineWidth(0.5);
  doc.line(config.pageWidth - 100, yPosition - 2, config.pageWidth - config.margin, yPosition - 2);

  const totalsX = config.pageWidth - 70;
  const valuesX = config.pageWidth - config.margin;
  const totalsSpacing = config.itemCount > 8 ? 5 : 6;

  doc.setFontSize(fontSizes.totals);
  doc.setFont('helvetica', 'normal');

  doc.text('Subtotal:', totalsX, yPosition);
  doc.text(`$${invoice.subtotal.toFixed(2)}`, valuesX, yPosition, { align: 'right' });

  if (invoice.taxAmount > 0) {
    yPosition += totalsSpacing;
    doc.text(`Tax (${invoice.taxRate}%):`, totalsX, yPosition);
    doc.text(`$${invoice.taxAmount.toFixed(2)}`, valuesX, yPosition, { align: 'right' });
  }

  yPosition += totalsSpacing;
  doc.setFont('helvetica', 'bold');
  doc.text('Total:', totalsX, yPosition);
  doc.text(`$${invoice.total.toFixed(2)}`, valuesX, yPosition, { align: 'right' });

  return yPosition;
}

function renderNotesSection(
  doc: jsPDF,
  invoice: InvoiceWithCustomer,
  config: LayoutConfig,
  fontSizes: FontSizes,
  companyInfo: CompanySettings | undefined,
  yPosition: number
): number {
  if (!invoice.notes || !invoice.notes.trim()) {
    return yPosition;
  }

  yPosition += config.itemCount > 8 ? 8 : 12;
  doc.setFontSize(fontSizes.notes);
  doc.setFont('helvetica', 'bold');
  yPosition = addText(doc, 'Notes:', config.margin, yPosition, config, { lineHeight: 3 });
  doc.setFont('helvetica', 'normal');

  let notes = invoice.notes;
  const remainingSpace = config.pageHeight - yPosition - (companyInfo?.invoiceFooter ? 15 : 5);
  const maxNotesLines = Math.floor(remainingSpace / 3);

  if (maxNotesLines > 0) {
    const notesLines = doc.splitTextToSize(notes, config.pageWidth - 2 * config.margin);
    if (notesLines.length > maxNotesLines) {
      const truncatedNotes = notesLines.slice(0, maxNotesLines - 1).join(' ') + '...';
      notes = truncatedNotes;
    }
    yPosition = addText(doc, notes, config.margin, yPosition + 2, config, {
      maxWidth: config.pageWidth - 2 * config.margin,
      lineHeight: 3
    });
  }

  return yPosition;
}

function renderFooter(doc: jsPDF, companyInfo: CompanySettings | undefined, config: LayoutConfig): void {
  if (!companyInfo?.invoiceFooter) {
    return;
  }

  const footerY = config.pageHeight - 12;
  doc.setFontSize(7);
  doc.setFont('helvetica', 'normal');
  doc.setTextColor(100, 100, 100);

  let footer = companyInfo.invoiceFooter;
  const footerLines = doc.splitTextToSize(footer, config.pageWidth - 2 * config.margin);
  if (footerLines.length > 2) {
    footer = footerLines.slice(0, 2).join(' ');
  }

  doc.text(footer, config.pageWidth / 2, footerY, { align: 'center' });
}

// Generate PDF and return as Blob for storage
export function generateInvoicePDFBlob(invoice: InvoiceWithCustomer, companyInfo?: CompanySettings): Blob {
  const doc = new jsPDF();
  const pageWidth = doc.internal.pageSize.width;
  const pageHeight = doc.internal.pageSize.height;

  const config = calculateLayoutConfig(invoice, pageWidth, pageHeight);
  const fontSizes = calculateFontSizes(config.itemCount);

  let yPosition = config.margin;

  const reservedSpace = calculateReservedSpace(config, companyInfo);
  const availableItemSpace = config.pageHeight - reservedSpace.total;
  const maxItemHeight = config.itemCount > 0 ? Math.max(8, availableItemSpace / config.itemCount) : 15;

  yPosition = renderHeader(doc, invoice, companyInfo, config, fontSizes, yPosition);
  yPosition = renderCustomerSection(doc, invoice, config, fontSizes, yPosition);
  yPosition = renderItemsTable(doc, invoice, config, fontSizes, reservedSpace, maxItemHeight, yPosition);
  yPosition = renderTotalsSection(doc, invoice, config, fontSizes, yPosition);
  yPosition = renderNotesSection(doc, invoice, config, fontSizes, companyInfo, yPosition);
  renderFooter(doc, companyInfo, config);

  // Return PDF as Blob instead of downloading
  const pdfOutput = doc.output('blob');
  return pdfOutput;
}

// Legacy function for backward compatibility - downloads PDF directly
export function generateInvoicePDF(invoice: InvoiceWithCustomer, companyInfo?: CompanySettings): void {
  const doc = new jsPDF();
  const pageWidth = doc.internal.pageSize.width;
  const pageHeight = doc.internal.pageSize.height;

  const config = calculateLayoutConfig(invoice, pageWidth, pageHeight);
  const fontSizes = calculateFontSizes(config.itemCount);

  let yPosition = config.margin;

  const reservedSpace = calculateReservedSpace(config, companyInfo);
  const availableItemSpace = config.pageHeight - reservedSpace.total;
  const maxItemHeight = config.itemCount > 0 ? Math.max(8, availableItemSpace / config.itemCount) : 15;

  yPosition = renderHeader(doc, invoice, companyInfo, config, fontSizes, yPosition);
  yPosition = renderCustomerSection(doc, invoice, config, fontSizes, yPosition);
  yPosition = renderItemsTable(doc, invoice, config, fontSizes, reservedSpace, maxItemHeight, yPosition);
  yPosition = renderTotalsSection(doc, invoice, config, fontSizes, yPosition);
  yPosition = renderNotesSection(doc, invoice, config, fontSizes, companyInfo, yPosition);
  renderFooter(doc, companyInfo, config);

  doc.save(`Invoice-${invoice.invoiceNumber}.pdf`);
}

function getStatusColor(status: string): { r: number; g: number; b: number } {
  switch (status.toLowerCase()) {
    case 'paid':
      return { r: 34, g: 197, b: 94 }; // Green
    case 'sent':
      return { r: 59, g: 130, b: 246 }; // Blue
    case 'overdue':
      return { r: 239, g: 68, b: 68 }; // Red
    case 'cancelled':
      return { r: 239, g: 68, b: 68 }; // Red
    default:
      return { r: 107, g: 114, b: 128 }; // Gray
  }
}
// Address autocomplete service using Mapbox Search Box API
// Provides address suggestions and automatic population of city/state/zip fields

export interface AddressSuggestion {
  id: string;
  text: string;
  place_name: string;
  center: [number, number]; // [longitude, latitude]
  properties: {
    address?: string;
    category?: string;
    maki?: string;
  };
  context: Array<{
    id: string;
    text: string;
    short_code?: string;
  }>;
}

export interface ParsedAddress {
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  coordinates?: [number, number];
}

export interface AddressServiceConfig {
  apiKey: string;
  baseUrl?: string;
  timeout?: number;
  maxRetries?: number;
}

export class AddressServiceError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode?: number
  ) {
    super(message);
    this.name = 'AddressServiceError';
  }
}

class AddressService {
  private config: AddressServiceConfig;
  private cache = new Map<string, AddressSuggestion[]>();
  private requestCount = 0;
  private lastResetTime = Date.now();
  private readonly RATE_LIMIT = 100; // requests per minute
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  constructor(config: AddressServiceConfig) {
    this.config = {
      baseUrl: 'https://api.mapbox.com/geocoding/v5/mapbox.places',
      timeout: 5000,
      maxRetries: 3,
      ...config,
    };
  }

  /**
   * Search for address suggestions based on user input
   */
  async searchAddresses(query: string, options: {
    limit?: number;
    country?: string;
    proximity?: [number, number];
    types?: string[];
  } = {}): Promise<AddressSuggestion[]> {
    if (!query || query.length < 2) {
      return [];
    }

    // Check rate limiting
    this.checkRateLimit();

    // Check cache first
    const cacheKey = this.getCacheKey(query, options);
    const cached = this.cache.get(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const suggestions = await this.fetchAddressSuggestions(query, options);
      
      // Cache the results
      this.cache.set(cacheKey, suggestions);
      setTimeout(() => this.cache.delete(cacheKey), this.CACHE_TTL);
      
      return suggestions;
    } catch (error) {
      console.error('Address search failed:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Parse a selected address suggestion into structured components
   */
  parseAddress(suggestion: AddressSuggestion): ParsedAddress {
    const placeName = suggestion.place_name;
    const context = suggestion.context || [];
    
    // Extract components from context
    let street = '';
    let city = '';
    let state = '';
    let zipCode = '';
    let country = '';

    // The main text is usually the street address
    if (suggestion.properties?.address) {
      street = suggestion.properties.address;
    } else {
      // Extract street from place_name (first part before first comma)
      const parts = placeName.split(',');
      street = parts[0]?.trim() || '';
    }

    // Parse context for city, state, zip, country
    context.forEach(item => {
      const id = item.id.toLowerCase();
      
      if (id.includes('place')) {
        city = item.text;
      } else if (id.includes('region')) {
        state = item.short_code || item.text;
      } else if (id.includes('postcode')) {
        zipCode = item.text;
      } else if (id.includes('country')) {
        country = item.short_code || item.text;
      }
    });

    // Fallback: try to extract from place_name if context parsing failed
    if (!city || !state) {
      const parts = placeName.split(',').map(p => p.trim());
      if (parts.length >= 3) {
        if (!city) city = parts[1] || '';
        if (!state) {
          const stateZip = parts[2] || '';
          const stateZipMatch = stateZip.match(/^([A-Z]{2})\s*(\d{5}(?:-\d{4})?)?/);
          if (stateZipMatch) {
            state = stateZipMatch[1];
            if (!zipCode && stateZipMatch[2]) {
              zipCode = stateZipMatch[2];
            }
          }
        }
      }
    }

    return {
      street: street || '',
      city: city || '',
      state: state || '',
      zipCode: zipCode || '',
      country: country || 'US',
      coordinates: suggestion.center,
    };
  }

  private async fetchAddressSuggestions(
    query: string,
    options: {
      limit?: number;
      country?: string;
      proximity?: [number, number];
      types?: string[];
    }
  ): Promise<AddressSuggestion[]> {
    const params = new URLSearchParams({
      access_token: this.config.apiKey,
      autocomplete: 'true',
      limit: String(options.limit || 5),
      country: options.country || 'US',
      types: options.types?.join(',') || 'address,poi',
    });

    if (options.proximity) {
      params.set('proximity', options.proximity.join(','));
    }

    const url = `${this.config.baseUrl}/${encodeURIComponent(query)}.json?${params}`;

    const response = await this.fetchWithRetry(url);
    
    if (!response.ok) {
      throw new AddressServiceError(
        `API request failed: ${response.statusText}`,
        'API_ERROR',
        response.status
      );
    }

    const data = await response.json();
    
    if (!data.features) {
      throw new AddressServiceError(
        'Invalid API response format',
        'INVALID_RESPONSE'
      );
    }

    return data.features;
  }

  private async fetchWithRetry(url: string, retries = 0): Promise<Response> {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

      const response = await fetch(url, {
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
        },
      });

      clearTimeout(timeoutId);
      this.requestCount++;

      return response;
    } catch (error) {
      if (retries < this.config.maxRetries! && this.isRetryableError(error)) {
        await this.delay(Math.pow(2, retries) * 1000); // Exponential backoff
        return this.fetchWithRetry(url, retries + 1);
      }
      throw error;
    }
  }

  private isRetryableError(error: any): boolean {
    return (
      error.name === 'AbortError' ||
      error.name === 'TypeError' ||
      (error.statusCode && error.statusCode >= 500)
    );
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private checkRateLimit(): void {
    const now = Date.now();
    const timeSinceReset = now - this.lastResetTime;
    
    // Reset counter every minute
    if (timeSinceReset >= 60000) {
      this.requestCount = 0;
      this.lastResetTime = now;
    }
    
    if (this.requestCount >= this.RATE_LIMIT) {
      throw new AddressServiceError(
        'Rate limit exceeded. Please try again later.',
        'RATE_LIMIT_EXCEEDED'
      );
    }
  }

  private getCacheKey(query: string, options: any): string {
    return `${query.toLowerCase()}_${JSON.stringify(options)}`;
  }

  private handleError(error: any): AddressServiceError {
    if (error instanceof AddressServiceError) {
      return error;
    }

    if (error.name === 'AbortError') {
      return new AddressServiceError(
        'Request timeout. Please try again.',
        'TIMEOUT'
      );
    }

    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      return new AddressServiceError(
        'Network error. Please check your connection.',
        'NETWORK_ERROR'
      );
    }

    return new AddressServiceError(
      'An unexpected error occurred.',
      'UNKNOWN_ERROR'
    );
  }

  /**
   * Clear the cache (useful for testing or memory management)
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * Get current cache size
   */
  getCacheSize(): number {
    return this.cache.size;
  }
}

// Singleton instance
let addressServiceInstance: AddressService | null = null;

/**
 * Get the address service instance
 */
export function getAddressService(): AddressService {
  if (!addressServiceInstance) {
    const apiKey = import.meta.env.VITE_MAPBOX_API_KEY;

    if (!apiKey) {
      throw new AddressServiceError(
        'Mapbox API key not configured. Please set VITE_MAPBOX_API_KEY environment variable.',
        'MISSING_API_KEY'
      );
    }

    addressServiceInstance = new AddressService({ apiKey });
  }

  return addressServiceInstance;
}

/**
 * Initialize the address service with custom configuration
 */
export function initializeAddressService(config: AddressServiceConfig): void {
  addressServiceInstance = new AddressService(config);
}

export default AddressService;

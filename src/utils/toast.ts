import { toast as sonnerToast, ToastT } from 'sonner';

// Variable to track the currently active toast ID
let activeToastId: string | number | null = null;

// Custom toast utility with single toast functionality
const toast = {
  /**
   * Show a success toast, dismissing any existing toast
   * @param message - Message to display
   */
  success: (message: string) => {
    if (activeToastId !== null) {
      sonnerToast.dismiss(activeToastId);
    }
    activeToastId = sonnerToast.success(message, {
      duration: 3000,
      onDismiss: () => {
        activeToastId = null;
      }
    });
    return activeToastId;
  },

  /**
   * Show an error toast, dismissing any existing toast
   * @param message - Message to display
   */
  error: (message: string) => {
    if (activeToastId !== null) {
      sonnerToast.dismiss(activeToastId);
    }
    activeToastId = sonnerToast.error(message, {
      duration: 4000,
      onDismiss: () => {
        activeToastId = null;
      }
    });
    return activeToastId;
  },

  /**
   * Show an info toast, dismissing any existing toast
   * @param message - Message to display
   */
  info: (message: string) => {
    if (activeToastId !== null) {
      sonnerToast.dismiss(activeToastId);
    }
    activeToastId = sonnerToast.info(message, {
      duration: 3000,
      onDismiss: () => {
        activeToastId = null;
      }
    });
    return activeToastId;
  },

  /**
   * Show a warning toast, dismissing any existing toast
   * @param message - Message to display
   */
  warning: (message: string) => {
    if (activeToastId !== null) {
      sonnerToast.dismiss(activeToastId);
    }
    activeToastId = sonnerToast.warning(message, {
      duration: 4000,
      onDismiss: () => {
        activeToastId = null;
      }
    });
    return activeToastId;
  },

  /**
   * Manually dismiss a toast
   * @param toastId - ID of the toast to dismiss (optional, dismisses active toast if not provided)
   */
  dismiss: (toastId?: string | number) => {
    const idToDismiss = toastId || activeToastId;
    if (idToDismiss !== null) {
      sonnerToast.dismiss(idToDismiss);
      if (idToDismiss === activeToastId) {
        activeToastId = null;
      }
    }
  },

  /**
   * Create a custom toast with options, dismissing any existing toast
   * @param message - Message to display
   * @param options - Toast options
   */
  custom: (message: string, options: Partial<ToastT> = {}) => {
    if (activeToastId !== null) {
      sonnerToast.dismiss(activeToastId);
    }
    activeToastId = sonnerToast(message, {
      duration: 700,
      ...options,
      onDismiss: (toast) => {
        activeToastId = null as any;
        options.onDismiss?.(toast);
      }
    });
    return activeToastId;
  }
};

export { toast };
/* Layout fixes for dashboard */

/* Fix html and body scrolling */
html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden !important; /* Prevent double scrollbars */
}

#root {
  height: 100%;
  min-height: 100vh;
  overflow: hidden !important; /* Contain all scrolling to child elements */
}

/* Fix sidebar height and content */
.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

.main-layout {
  display: flex;
  flex: 1;
  overflow: hidden;
  height: calc(100vh - var(--header-height, 64px));
}

.sidebar-container {
  height: 100%;
  overflow-y: auto;
}

.main-content {
  flex: 1;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  padding-bottom: 80px; /* Extra padding to account for mobile nav */
}

/* Fix dark mode sidebar */
.dark .sidebar-container {
  background-color: #1f2937;
}

/* Add extra mobile bottom padding */
@media (max-width: 768px) {
  .main-content {
    padding-bottom: 120px;
  }
}
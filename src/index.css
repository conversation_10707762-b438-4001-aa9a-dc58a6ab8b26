@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Light theme */
  --color-bg: 255 255 255;
  --color-bg-surface: 249 250 251;
  --color-text: 17 24 39;
  --color-text-muted: 75 85 99;
  --color-border: 229 231 235;
  --color-primary: 59 130 246;
  --color-primary-hover: 37 99 235;
}

.dark {
  /* Dark theme */
  --color-bg: 17 24 39;
  --color-bg-surface: 31 41 55;
  --color-text: 255 255 255;
  --color-text-muted: 156 163 175;
  --color-border: 55 65 81;
  --color-primary: 59 130 246;
  --color-primary-hover: 37 99 235;
}

@layer base {
  :root {
    --color-primary: 26 62 114;
    --color-accent: 26 188 156;
    --color-charcoal: 43 43 43;
    --color-neutral: 245 247 250;
    --color-text-light: 224 224 224;
    --color-bg-dark: 18 18 18;
    --color-card-dark: 31 31 31;
    --color-sidebar-dark: 18 18 18;
    --color-sidebar-active: 30 30 30;
    --color-sidebar-hover: 51 51 51;
    --color-mobile-nav: 31 41 55; /* Fixed mobile nav color #1f2937 */
  }

  .dark {
    --color-primary: 59 130 246;
    --color-accent: 26 188 156;
    --color-charcoal: 229 231 235;
    --color-neutral: 18 18 18;
    --color-text-light: 229 231 235;
    --color-bg-dark: 18 18 18;
    --color-card-dark: 31 31 31;
    --color-sidebar-dark: 31 41 55;
    --color-sidebar-active: 55 65 81;
    --color-sidebar-hover: 75 85 99;
    --color-mobile-nav: 31 41 55; /* Fixed mobile nav color #1f2937 */
  }

  /* Ensure full height layout */
  html, body {
    height: 100%;
    margin: 0;
    padding: 0;
  }

  #root {
    height: 100%;
    min-height: 100vh;
  }

  body {
    font-family: 'Inter', sans-serif;
    background-color: #F5F7FA;
    color: #2B2B2B;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  .dark body {
    background-color: rgb(var(--color-bg-dark));
    color: rgb(var(--color-text-light));
  }
  
  /* Improve touch targets on mobile */
  @media (max-width: 768px) {
    button, input, select, textarea {
      min-height: 44px;
    }
  }
}

@layer utilities {
  .text-charcoal {
    color: rgb(var(--color-charcoal));
  }
  
  .bg-primary {
    background-color: rgb(var(--color-primary));
  }
  
  .bg-accent {
    background-color: rgb(var(--color-accent));
  }
  
  .bg-neutral {
    background-color: rgb(var(--color-neutral));
  }
  
  .text-primary {
    color: rgb(var(--color-primary));
  }
  
  .text-accent {
    color: rgb(var(--color-accent));
  }
  
  .border-primary {
    border-color: rgb(var(--color-primary));
  }
  
  .border-accent {
    border-color: rgb(var(--color-accent));
  }
  
  .hover\:bg-primary\/90:hover {
    background-color: rgb(var(--color-primary) / 0.9);
  }
  
  .hover\:bg-primary\/10:hover {
    background-color: rgb(var(--color-primary) / 0.1);
  }
  
  .focus\:ring-primary:focus {
    --tw-ring-color: rgb(var(--color-primary));
  }
  
  .focus\:ring-primary\/20:focus {
    --tw-ring-color: rgb(var(--color-primary) / 0.2);
  }

  /* Sidebar specific utilities */
  .sidebar-bg {
    background-color: white;
  }
  
  .dark .sidebar-bg {
    background-color: rgb(var(--color-sidebar-dark));
  }
  
  .sidebar-item-active {
    background-color: rgb(var(--color-primary) / 0.1);
    color: rgb(var(--color-primary));
    border: 1px solid rgb(var(--color-primary) / 0.2);
  }
  
  .dark .sidebar-item-active {
    background-color: rgb(var(--color-sidebar-active));
    color: rgb(229 231 235);
    border: 1px solid rgb(var(--color-sidebar-hover));
  }
  
  .sidebar-item-inactive {
    color: #6b7280;
  }
  
  .dark .sidebar-item-inactive {
    color: rgb(229 231 235);
  }
  
  .sidebar-item-hover:hover {
    background-color: #f9fafb;
    color: #111827;
  }
  
  .dark .sidebar-item-hover:hover {
    background-color: rgb(var(--color-sidebar-hover));
    color: rgb(229 231 235);
  }

  /* Mobile navigation specific utilities - Fixed background */
  .mobile-nav-bg {
    background-color: white !important;
  }
  
  .dark .mobile-nav-bg {
    background-color: #1f2937 !important; /* Fixed color without CSS variables */
  }
  
  .mobile-nav-overlay-bg {
    background-color: white !important;
  }
  
  .dark .mobile-nav-overlay-bg {
    background-color: #1f2937 !important; /* Fixed color without CSS variables */
  }

  /* Full height utilities */
  .full-height-layout {
    height: 100vh;
    min-height: 100vh;
  }

  .sidebar-responsive-height {
    max-height: calc(100vh - var(--header-height, 0px));
    min-height: fit-content;
    overflow-y: auto;
  }

  /* Full height sidebar utilities */
  .sidebar-full-height {
    height: 100vh;
    min-height: 100vh;
    max-height: 100vh;
  }

  .sidebar-content-height {
    height: calc(100vh - var(--header-height, 64px));
    min-height: calc(100vh - var(--header-height, 64px));
    max-height: calc(100vh - var(--header-height, 64px));
  }

  /* Touch-friendly utilities */
  .touch-manipulation {
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }
}

/* Custom scrollbar - Hidden but functional */
::-webkit-scrollbar {
  width: 0px;
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: transparent;
}

/* Firefox scrollbar hiding */
html {
  scrollbar-width: none;
}

/* For all elements with scroll */
* {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

*::-webkit-scrollbar {
  width: 0px;
  background: transparent;
}

/* Smooth transitions - Exclude mobile nav background */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

/* Override transitions for mobile nav to prevent flickering */
.mobile-nav-bg,
.mobile-nav-overlay-bg {
  transition: none !important;
}

/* Focus styles - Enhanced for mobile */
button:focus,
input:focus,
select:focus,
textarea:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgb(var(--color-primary) / 0.1);
}

/* Active states for mobile */
@media (max-width: 768px) {
  button:active {
    transform: scale(0.98);
  }
  
  /* Ensure content doesn't get hidden behind mobile navigation */
  .mobile-content {
    padding-bottom: calc(80px + env(safe-area-inset-bottom, 0px));
  }
  
  /* Mobile navigation specific styles */
  .mobile-nav-item {
    -webkit-tap-highlight-color: transparent;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
  }
  
  .mobile-nav-item:active {
    transform: scale(0.95);
  }
}

/* Hover animations - Disabled on touch devices */
@media (hover: hover) {
  .hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .dark .hover-lift:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
  }
}

/* Enhanced Quick Action Button Animations */
.quick-action-button {
  position: relative;
  overflow: hidden;
}

.quick-action-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(var(--color-primary), 0.1) 0%, transparent 70%);
  transition: width 0.6s, height 0.6s;
  transform: translate(-50%, -50%);
  border-radius: 50%;
}

.quick-action-button:active::before {
  width: 300px;
  height: 300px;
}

/* Enhanced button press animation */
@keyframes buttonPress {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.95);
  }
  100% {
    transform: scale(1);
  }
}

.quick-action-pressed {
  animation: buttonPress 0.15s ease-in-out;
}

/* Success pulse animation for feedback */
@keyframes successPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(var(--color-accent), 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(var(--color-accent), 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(var(--color-accent), 0);
  }
}

.success-feedback {
  animation: successPulse 0.6s ease-out;
}

/* Card styles */
.card {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 transition-all duration-300;
}

.card-hover {
  @apply card hover:shadow-md dark:hover:shadow-lg transition-all duration-300;
}

/* Enhanced dark mode card */
.dark-card {
  @apply bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 transition-all duration-300;
}

/* Button styles - Enhanced for mobile and dark mode */
.btn-primary {
  @apply bg-primary text-white px-4 py-3 rounded-lg hover:bg-primary/90 transition-all duration-300 font-medium text-center shadow-sm hover:shadow-md;
}

.btn-secondary {
  @apply bg-gray-100 dark:bg-gray-700 text-charcoal dark:text-gray-200 px-4 py-3 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-all duration-300 font-medium text-center shadow-sm hover:shadow-md;
}

.btn-accent {
  @apply bg-accent text-white px-4 py-3 rounded-lg hover:bg-accent/90 transition-all duration-300 font-medium text-center shadow-sm hover:shadow-md;
}

/* Form styles - Enhanced for mobile and dark mode */
.form-input {
  @apply w-full px-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent text-base bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 transition-all duration-300;
}

.form-label {
  @apply block text-sm font-medium text-charcoal dark:text-gray-200 mb-2 transition-colors duration-300;
}

/* Auth form styles */
.auth-input-field {
  @apply w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent text-base bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 transition-all duration-300;
}

.auth-button {
  @apply w-full bg-primary text-white px-4 py-3 rounded-lg hover:bg-primary/90 transition-all duration-300 font-medium text-center disabled:opacity-50 disabled:cursor-not-allowed shadow-sm hover:shadow-md;
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Loading spinner */
.spinner {
  @apply animate-spin rounded-full border-b-2 border-primary;
}

/* Status badges */
.status-badge {
  @apply inline-flex items-center px-2 py-1 rounded-full text-xs font-medium transition-all duration-300;
}

.status-active {
  @apply bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300;
}

.status-inactive {
  @apply bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-300;
}

.status-pending {
  @apply bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300;
}

.status-error {
  @apply bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300;
}

/* Mobile-specific utilities */
@media (max-width: 768px) {
  .mobile-stack {
    @apply flex-col space-y-4 space-x-0;
  }
  
  .mobile-full {
    @apply w-full;
  }
  
  /* Ensure proper spacing for mobile bottom navigation */
  .mobile-bottom-padding {
    padding-bottom: calc(80px + env(safe-area-inset-bottom, 0px));
  }
  
  /* Mobile-friendly modal sizing */
  .mobile-modal {
    @apply max-h-[90vh] overflow-y-auto;
  }
  
  /* Touch-friendly table alternatives */
  .mobile-table-card {
    @apply block md:hidden;
  }
  
  .desktop-table {
    @apply hidden md:block;
  }
}

/* Responsive utilities */
@media (max-width: 640px) {
  .sm-stack {
    @apply flex-col space-y-2 space-x-0;
  }
}

/* Dark mode specific styles */
.dark {
  color-scheme: dark;
}

.dark .bg-neutral {
  background-color: rgb(var(--color-bg-dark));
}

.dark input::placeholder,
.dark textarea::placeholder {
  color: rgb(156 163 175);
}

.dark select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23374151' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.5rem center;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
  -webkit-print-color-adjust: exact;
  print-color-adjust: exact;
  appearance: none;
}

.dark select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
}

/* Enhanced modal styles for dark mode */
.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 dark:bg-opacity-70 flex items-center justify-center z-50 p-4 transition-all duration-300;
}

/* Enhanced category colors for dark mode */
.category-equipment {
  @apply bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 border border-blue-200 dark:border-blue-700;
}

.category-service {
  @apply bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 border border-green-200 dark:border-green-700;
}

.category-parts {
  @apply bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300 border border-purple-200 dark:border-purple-700;
}

/* Accessibility improvements */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .high-contrast {
    @apply border-2 border-black dark:border-white;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Safe area support for mobile devices with notches */
@supports (padding: max(0px)) {
  .safe-area-top {
    padding-top: max(1rem, env(safe-area-inset-top));
  }
  
  .safe-area-bottom {
    padding-bottom: max(1rem, env(safe-area-inset-bottom));
  }
  
  .safe-area-left {
    padding-left: max(1rem, env(safe-area-inset-left));
  }
  
  .safe-area-right {
    padding-right: max(1rem, env(safe-area-inset-right));
  }
}

/* Improved focus indicators for keyboard navigation */
@media (prefers-reduced-motion: no-preference) {
  .focus-ring:focus {
    @apply ring-2 ring-primary ring-offset-2 ring-offset-white dark:ring-offset-gray-800;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break {
    page-break-before: always;
  }
  
  body {
    background: white !important;
    color: black !important;
  }
}

/* Dark mode transitions */
.dark-transition {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Custom dark mode utilities */
.dark-text {
  @apply text-gray-900 dark:text-gray-100 transition-colors duration-300;
}

.dark-text-muted {
  @apply text-gray-600 dark:text-gray-400 transition-colors duration-300;
}

.dark-border {
  @apply border-gray-200 dark:border-gray-700 transition-colors duration-300;
}

.dark-hover {
  @apply hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-300;
}

/* Enhanced delete button for dark mode */
.btn-delete {
  @apply bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 py-2 px-3 rounded-lg hover:bg-red-100 dark:hover:bg-red-900/30 transition-all duration-300 text-sm font-medium border border-red-200 dark:border-red-800;
}

/* Enhanced filter buttons */
.filter-button {
  @apply px-3 md:px-4 py-2 rounded-lg capitalize transition-all duration-300 text-sm md:text-base touch-manipulation border;
}

.filter-button-active {
  @apply bg-accent text-white border-accent shadow-md;
}

.filter-button-inactive {
  @apply bg-neutral dark:bg-gray-700 text-charcoal dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 border-gray-300 dark:border-gray-600;
}

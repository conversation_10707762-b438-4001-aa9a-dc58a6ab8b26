import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { useState, useEffect, useRef } from "react";
import { toast } from "sonner";
import { useStorageUrl } from "../hooks/useStorageUrl";
import { AddressAutocomplete, AddressFormData } from "./common/AddressAutocomplete";

type BrandingFormData = {
  companyName: string;
  logoUrl: string;
  contactEmail: string;
  contactPhone: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  termsUrl: string;
  privacyPolicyUrl: string;
  invoiceFooter: string;
  taxId: string;
  paymentTerms: string;
};

const DEFAULT_FORM_DATA: BrandingFormData = {
  companyName: "",
  logoUrl: "",
  contactEmail: "",
  contactPhone: "",
  address: "",
  city: "",
  state: "",
  zipCode: "",
  termsUrl: "",
  privacyPolicyUrl: "",
  invoiceFooter: "",
  taxId: "",
  paymentTerms: "Net 30",
};

export function BrandingSettings() {
  const user = useQuery(api.auth.loggedInUser);
  const settings = useQuery(api.settings.get);
  const updateSettings = useMutation(api.settings.update);
  const uploadLogo = useMutation(api.settings.uploadLogo);
  const history = useQuery(api.settings.getHistory);
  
  const [formData, setFormData] = useState<BrandingFormData>(DEFAULT_FORM_DATA);
  const [loading, setLoading] = useState(false);
  const [showHistory, setShowHistory] = useState(false);
  const [formTouched, setFormTouched] = useState(false);
  const [previewLogo, setPreviewLogo] = useState<string | null>(null);
  
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // Get the actual URL for the logo if it's a storage ID
  const logoUrl = useStorageUrl(formData.logoUrl);
  
  // Initialize form with settings data when available
  useEffect(() => {
    if (settings) {
      setFormData({
        companyName: settings.companyName || "",
        logoUrl: settings.logoUrl || "",
        contactEmail: settings.contactEmail || "",
        contactPhone: settings.contactPhone || "",
        address: settings.address || "",
        city: settings.city || "",
        state: settings.state || "",
        zipCode: settings.zipCode || "",
        termsUrl: settings.termsUrl || "",
        privacyPolicyUrl: settings.privacyPolicyUrl || "",
        invoiceFooter: settings.invoiceFooter || "",
        taxId: settings.taxId || "",
        paymentTerms: settings.paymentTerms || "Net 30",
      });
    }
  }, [settings]);
  
  // If user is not an admin, show unauthorized message
  if (user?.role !== 'admin') {
    return (
      <div className="card p-6">
        <h2 className="text-xl font-bold mb-4">Unauthorized</h2>
        <p>You must be an administrator to access branding settings.</p>
      </div>
    );
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    setFormTouched(true);
  };

  const handleAddressChange = (address: string) => {
    setFormData(prev => ({ ...prev, address }));
    setFormTouched(true);
  };

  const handleAddressFormDataChange = (addressData: Partial<AddressFormData>) => {
    setFormData(prev => ({
      ...prev,
      address: addressData.address || prev.address,
      city: addressData.city || prev.city,
      state: addressData.state || prev.state,
      zipCode: addressData.zipCode || prev.zipCode,
    }));
    setFormTouched(true);
  };

  const handleLogoUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    // Validate file
    if (file.size > 2 * 1024 * 1024) {
      toast.error('Logo must be less than 2MB');
      return;
    }
    
    // Create preview for immediate feedback
    setPreviewLogo(URL.createObjectURL(file));
    setFormTouched(true);
    
    try {
      setLoading(true);
      
      // Get upload URL from Convex
      const uploadUrl = await uploadLogo({});
      if (!uploadUrl) throw new Error('Failed to get upload URL');
      
      // Upload the file to the generated URL
      const result = await fetch(uploadUrl, {
        method: "POST",
        headers: { "Content-Type": file.type },
        body: file,
      });

      if (!result.ok) {
        throw new Error('Failed to upload file');
      }

      // Get the storage ID from the response
      const { storageId } = await result.json();

      // Use the storage ID as the logo URL in settings
      setFormData(prev => ({...prev, logoUrl: storageId}));
      toast.success('Logo uploaded successfully');
    } catch (error) {
      console.error('Upload error:', error);
      toast.error('Failed to upload logo');
      // Reset preview on error
      setPreviewLogo(null);
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveLogo = () => {
    setFormData(prev => ({...prev, logoUrl: ""}));
    setPreviewLogo(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
    setFormTouched(true);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setLoading(true);
      await updateSettings(formData);
      toast.success("Branding settings updated successfully");
      setFormTouched(false);
    } catch (error: any) {
      if (error.message?.includes('Unauthorized')) {
        toast.error("You don't have permission to update branding settings");
      } else {
        toast.error("Failed to update branding settings");
        console.error("Update error:", error);
      }
    } finally {
      setLoading(false);
    }
  };
  
  const handleReset = () => {
    if (settings) {
      setFormData({
        companyName: settings.companyName || "",
        logoUrl: settings.logoUrl || "",
        contactEmail: settings.contactEmail || "",
        contactPhone: settings.contactPhone || "",
        address: settings.address || "",
        city: settings.city || "",
        state: settings.state || "",
        zipCode: settings.zipCode || "",
        termsUrl: settings.termsUrl || "",
        privacyPolicyUrl: settings.privacyPolicyUrl || "",
        invoiceFooter: settings.invoiceFooter || "",
        taxId: settings.taxId || "",
        paymentTerms: settings.paymentTerms || "Net 30",
      });
      setPreviewLogo(null);
      setFormTouched(false);
    }
  };

  const displayLogo = previewLogo || logoUrl;

  return (
    <div className="space-y-6">
      <div className="card p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold">Company Branding Settings</h2>
          <div>
            <button
              type="button"
              className="text-blue-600 hover:text-blue-800 text-sm mr-4"
              onClick={() => setShowHistory(!showHistory)}
            >
              {showHistory ? "Hide History" : "View History"}
            </button>
          </div>
        </div>
        
        <div className="text-sm text-gray-600 mb-6">
          <p>These settings will appear on invoices and other customer-facing documents.</p>
        </div>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Company Identity Section */}
          <div className="border-b pb-6">
            <h3 className="font-medium text-lg mb-4">Company Identity</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium mb-1">Company Name *</label>
                <input
                  type="text"
                  name="companyName"
                  value={formData.companyName}
                  onChange={handleChange}
                  className="form-input w-full"
                  placeholder="Your Company Name"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Company Logo</label>
                <div className="flex items-center gap-4">
                  {displayLogo && (
                    <div className="relative w-16 h-16 border rounded-md overflow-hidden bg-gray-50 flex items-center justify-center">
                      <img
                        src={displayLogo}
                        alt="Company logo"
                        className="object-contain w-full h-full"
                        onError={() => {
                          setPreviewLogo(null);
                          if (previewLogo) URL.revokeObjectURL(previewLogo);
                        }}
                      />
                      <button
                        type="button"
                        onClick={handleRemoveLogo}
                        className="absolute top-0 right-0 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs"
                        title="Remove logo"
                      >
                        ×
                      </button>
                    </div>
                  )}
                  <div className="flex-1">
                    <input
                      type="file"
                      accept="image/png, image/jpeg, image/svg+xml"
                      onChange={handleLogoUpload}
                      className="hidden"
                      id="logo-upload"
                      ref={fileInputRef}
                    />
                    <label
                      htmlFor="logo-upload"
                      className={`btn-secondary cursor-pointer block text-center ${loading ? 'opacity-70 cursor-not-allowed' : ''}`}
                    >
                      {displayLogo ? 'Change Logo' : 'Upload Logo'}
                    </label>
                    <p className="text-xs text-gray-500 mt-1">
                      PNG, JPG or SVG. Max 2MB.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Contact Information Section */}
          <div className="border-b pb-6">
            <h3 className="font-medium text-lg mb-4">Contact Information</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium mb-1">Contact Email *</label>
                <input
                  type="email"
                  name="contactEmail"
                  value={formData.contactEmail}
                  onChange={handleChange}
                  className="form-input w-full"
                  placeholder="<EMAIL>"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Contact Phone *</label>
                <input
                  type="tel"
                  name="contactPhone"
                  value={formData.contactPhone}
                  onChange={handleChange}
                  className="form-input w-full"
                  placeholder="(*************"
                  required
                />
              </div>
            </div>
          </div>
          
          {/* Address Section */}
          <div className="border-b pb-6">
            <h3 className="font-medium text-lg mb-4">Business Address</h3>

            <div className="grid grid-cols-1 gap-6">
              {/* Address Autocomplete */}
              <AddressAutocomplete
                value={formData.address}
                onChange={handleAddressChange}
                onFormDataChange={handleAddressFormDataChange}
                label="Street Address"
                placeholder="Enter business address..."
                required
                className="form-input w-full"
              />

              {/* City, State, ZIP fields - now auto-populated but still editable */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label className="block text-sm font-medium mb-1">City *</label>
                  <input
                    type="text"
                    name="city"
                    value={formData.city}
                    onChange={handleChange}
                    className="form-input w-full"
                    placeholder="City (auto-filled from address)"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">State/Province *</label>
                  <input
                    type="text"
                    name="state"
                    value={formData.state}
                    onChange={handleChange}
                    className="form-input w-full"
                    placeholder="State (auto-filled)"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">ZIP/Postal Code *</label>
                  <input
                    type="text"
                    name="zipCode"
                    value={formData.zipCode}
                    onChange={handleChange}
                    className="form-input w-full"
                    placeholder="ZIP (auto-filled)"
                    required
                  />
                </div>
              </div>
            </div>
          </div>
          
          {/* Business Details Section */}
          <div className="border-b pb-6">
            <h3 className="font-medium text-lg mb-4">Business Details</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium mb-1">Tax ID</label>
                <input
                  type="text"
                  name="taxId"
                  value={formData.taxId}
                  onChange={handleChange}
                  className="form-input w-full"
                  placeholder="Tax ID or EIN"
                />
                <p className="text-xs text-gray-500 mt-1">Will appear on invoices</p>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Default Payment Terms</label>
                <select
                  name="paymentTerms"
                  value={formData.paymentTerms}
                  onChange={handleChange}
                  className="form-select w-full"
                >
                  <option value="Due on Receipt">Due on Receipt</option>
                  <option value="Net 15">Net 15</option>
                  <option value="Net 30">Net 30</option>
                  <option value="Net 45">Net 45</option>
                  <option value="Net 60">Net 60</option>
                  <option value="Net 90">Net 90</option>
                </select>
                <p className="text-xs text-gray-500 mt-1">Default for new invoices</p>
              </div>
            </div>
          </div>
          
          {/* Legal Documents Section */}
          <div className="border-b pb-6">
            <h3 className="font-medium text-lg mb-4">Legal Documents</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium mb-1">Terms & Conditions URL</label>
                <input
                  type="url"
                  name="termsUrl"
                  value={formData.termsUrl}
                  onChange={handleChange}
                  className="form-input w-full"
                  placeholder="https://yourcompany.com/terms"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Privacy Policy URL</label>
                <input
                  type="url"
                  name="privacyPolicyUrl"
                  value={formData.privacyPolicyUrl}
                  onChange={handleChange}
                  className="form-input w-full"
                  placeholder="https://yourcompany.com/privacy"
                />
              </div>
            </div>
          </div>
          
          {/* Invoice Customization Section */}
          <div>
            <h3 className="font-medium text-lg mb-4">Invoice Customization</h3>
            
            <div>
              <label className="block text-sm font-medium mb-1">Invoice Footer</label>
              <textarea
                name="invoiceFooter"
                value={formData.invoiceFooter}
                onChange={handleChange}
                className="form-textarea w-full min-h-[100px]"
                placeholder="Thank you for your business. Payment can be made via bank transfer or check."
              />
              <p className="text-xs text-gray-500 mt-1">This text will appear at the bottom of all invoices</p>
            </div>
          </div>

          <div className="flex justify-between pt-4">
            <button
              type="button"
              onClick={handleReset}
              className="btn-secondary"
              disabled={!formTouched || loading}
            >
              Reset Changes
            </button>
            
            <button
              type="submit"
              className={`btn-primary ${loading ? 'opacity-70 cursor-not-allowed' : ''}`}
              disabled={loading || !formTouched}
            >
              {loading ? 'Saving...' : 'Save Branding Settings'}
            </button>
          </div>
        </form>
      </div>

      {/* Version History Panel */}
      {showHistory && history && history.length > 0 && (
        <div className="card p-6">
          <h2 className="text-xl font-bold mb-4">Version History</h2>
          <div className="space-y-4 max-h-[500px] overflow-y-auto">
            {history.map((record, index) => (
              <div key={record._id} className="border rounded-lg p-4">
                <div className="flex justify-between items-center">
                  <div>
                    <span className="font-medium">
                      {new Date(record.changedAt).toLocaleDateString()} at {new Date(record.changedAt).toLocaleTimeString()}
                    </span>
                    {index === 0 && <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">Current</span>}
                  </div>
                  <span className="text-sm text-gray-500">Changed by {record.changedBy}</span>
                </div>
                <div className="mt-3">
                  <div className="bg-gray-50 rounded-md p-3 text-sm">
                    <details>
                      <summary className="cursor-pointer">View details</summary>
                      <pre className="text-xs mt-2 overflow-x-auto">
                        {JSON.stringify(record.data, null, 2)}
                      </pre>
                    </details>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
import React, { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { toast } from "sonner";
import { RoleBadge } from "../UserProfile/RoleBadge";
import { RoleChangeModal } from "./RoleChangeModal";
import { Id } from "../../../convex/_generated/dataModel";

interface User {
  _id: Id<"users">;
  name: string;
  email: string;
  role: string;
  department: string;
  phone: string;
  bio: string;
  _creationTime: number;
}

export function UserManagement() {
  const [activeTab, setActiveTab] = useState("users");
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [showRoleChangeModal, setShowRoleChangeModal] = useState(false);

  const user = useQuery(api.auth.loggedInUser);
  const allUsers = useQuery(api.roleManagement.listAllUsers);
  const pendingRequests = useQuery(api.roleManagement.listPendingRoleRequests);
  const roleStats = useQuery(api.roleManagement.getRoleStatistics);

  const isMaster = user?.role === "master";

  // Check if user has master role
  if (user && !isMaster) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="text-center py-12">
          <div className="text-6xl mb-4">🔒</div>
          <h2 className="text-2xl font-semibold text-charcoal dark:text-gray-100 mb-4">
            Access Restricted
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto">
            User management is only available to Master users. Please contact your system administrator if you need access.
          </p>
        </div>
      </div>
    );
  }

  if (!allUsers || !pendingRequests || !roleStats) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  const tabs = [
    { id: "users", label: "All Users", icon: "👥" },
    { id: "requests", label: "Role Requests", icon: "📋", badge: pendingRequests.length },
    { id: "statistics", label: "Statistics", icon: "📊" },
  ];

  return (
    <div className="max-w-7xl mx-auto p-6">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl md:text-3xl font-bold text-charcoal dark:text-gray-100 mb-2">
          User Management
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Manage user roles and permissions across the system
        </p>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700 mb-6">
        <nav className="flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center gap-2 ${
                activeTab === tab.id
                  ? "border-primary text-primary"
                  : "border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
              }`}
            >
              <span>{tab.icon}</span>
              {tab.label}
              {tab.badge && tab.badge > 0 && (
                <span className="bg-red-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] h-5 flex items-center justify-center">
                  {tab.badge}
                </span>
              )}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === "users" && (
        <UsersTab 
          users={allUsers} 
          onUserSelect={setSelectedUser}
          onRoleChange={() => setShowRoleChangeModal(true)}
        />
      )}
      
      {activeTab === "requests" && (
        <RoleRequestsTab requests={pendingRequests} />
      )}
      
      {activeTab === "statistics" && (
        <StatisticsTab stats={roleStats} />
      )}

      {/* Role Change Modal */}
      {showRoleChangeModal && selectedUser && (
        <RoleChangeModal
          user={selectedUser}
          onClose={() => {
            setShowRoleChangeModal(false);
            setSelectedUser(null);
          }}
          onSuccess={() => {
            setShowRoleChangeModal(false);
            setSelectedUser(null);
            toast.success("Role updated successfully");
          }}
        />
      )}
    </div>
  );
}

// Users Tab Component
function UsersTab({ 
  users, 
  onUserSelect, 
  onRoleChange 
}: { 
  users: User[]; 
  onUserSelect: (user: User) => void;
  onRoleChange: () => void;
}) {
  const [searchTerm, setSearchTerm] = useState("");
  const [roleFilter, setRoleFilter] = useState("all");

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesRole = roleFilter === "all" || user.role === roleFilter;
    return matchesSearch && matchesRole;
  });

  return (
    <div className="space-y-4">
      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <input
            type="text"
            placeholder="Search users..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="form-input w-full"
          />
        </div>
        <div>
          <select
            value={roleFilter}
            onChange={(e) => setRoleFilter(e.target.value)}
            className="form-input"
          >
            <option value="all">All Roles</option>
            <option value="master">Master</option>
            <option value="admin">Admin</option>
            <option value="staff">Staff</option>
          </select>
        </div>
      </div>

      {/* Users Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredUsers.map((user) => (
          <div
            key={user._id}
            className="card p-4 hover:shadow-md transition-shadow cursor-pointer"
            onClick={() => {
              onUserSelect(user);
              onRoleChange();
            }}
          >
            <div className="flex items-start justify-between mb-3">
              <div className="flex-1">
                <h3 className="font-semibold text-charcoal dark:text-gray-100 mb-1">
                  {user.name || "Unknown User"}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                  {user.email}
                </p>
              </div>
              <RoleBadge role={user.role} />
            </div>
            
            {user.department && (
              <p className="text-xs text-gray-500 dark:text-gray-400 mb-2">
                {user.department}
              </p>
            )}
            
            <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
              <span>Joined {new Date(user._creationTime).toLocaleDateString()}</span>
              <button className="text-primary hover:text-primary/80">
                Manage →
              </button>
            </div>
          </div>
        ))}
      </div>

      {filteredUsers.length === 0 && (
        <div className="text-center py-8">
          <p className="text-gray-500 dark:text-gray-400">No users found matching your criteria.</p>
        </div>
      )}
    </div>
  );
}

// Role Requests Tab Component
function RoleRequestsTab({ requests }: { requests: any[] }) {
  const reviewRequest = useMutation(api.roleManagement.reviewRoleRequest);
  const [reviewingId, setReviewingId] = useState<string | null>(null);

  const handleReview = async (requestId: string, action: "approve" | "reject", notes?: string) => {
    try {
      setReviewingId(requestId);
      await reviewRequest({ requestId: requestId as Id<"roleRequests">, action, reviewNotes: notes });
      toast.success(`Role request ${action}d successfully`);
    } catch (error) {
      toast.error(`Failed to ${action} role request`);
    } finally {
      setReviewingId(null);
    }
  };

  if (requests.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-4xl mb-4">✅</div>
        <h3 className="text-lg font-semibold text-charcoal dark:text-gray-100 mb-2">
          No Pending Requests
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          All role requests have been reviewed.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {requests.map((request) => (
        <div key={request._id} className="card p-6">
          <div className="flex items-start justify-between mb-4">
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-2">
                <h3 className="font-semibold text-charcoal dark:text-gray-100">
                  {request.user.name}
                </h3>
                <span className="text-sm text-gray-500">→</span>
                <RoleBadge role={request.requestedRole} />
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                {request.user.email}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Current role: <span className="capitalize">{request.currentRole}</span>
              </p>
            </div>
            <div className="text-right">
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Requested by: {request.requestedByUser.name}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {new Date(request.createdAt).toLocaleDateString()}
              </p>
            </div>
          </div>

          {request.reason && (
            <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <p className="text-sm text-gray-700 dark:text-gray-300">
                <strong>Reason:</strong> {request.reason}
              </p>
            </div>
          )}

          <div className="flex gap-3">
            <button
              onClick={() => handleReview(request._id, "approve")}
              disabled={reviewingId === request._id}
              className="btn-primary flex-1"
            >
              {reviewingId === request._id ? "Processing..." : "Approve"}
            </button>
            <button
              onClick={() => handleReview(request._id, "reject")}
              disabled={reviewingId === request._id}
              className="btn-secondary flex-1"
            >
              {reviewingId === request._id ? "Processing..." : "Reject"}
            </button>
          </div>
        </div>
      ))}
    </div>
  );
}

// Statistics Tab Component
function StatisticsTab({ stats }: { stats: any }) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {/* Total Users */}
      <div className="card p-6">
        <div className="flex items-center gap-4">
          <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
            <span className="text-2xl">👥</span>
          </div>
          <div>
            <h3 className="text-2xl font-bold text-charcoal dark:text-gray-100">
              {stats.totalUsers}
            </h3>
            <p className="text-gray-600 dark:text-gray-400">Total Users</p>
          </div>
        </div>
      </div>

      {/* Pending Requests */}
      <div className="card p-6">
        <div className="flex items-center gap-4">
          <div className="w-12 h-12 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg flex items-center justify-center">
            <span className="text-2xl">⏳</span>
          </div>
          <div>
            <h3 className="text-2xl font-bold text-charcoal dark:text-gray-100">
              {stats.pendingRequests}
            </h3>
            <p className="text-gray-600 dark:text-gray-400">Pending Requests</p>
          </div>
        </div>
      </div>

      {/* Role Distribution */}
      <div className="card p-6 md:col-span-2 lg:col-span-1">
        <h3 className="font-semibold text-charcoal dark:text-gray-100 mb-4">Role Distribution</h3>
        <div className="space-y-3">
          {Object.entries(stats.roleDistribution).map(([role, count]) => (
            <div key={role} className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <RoleBadge role={role} />
              </div>
              <span className="font-semibold text-charcoal dark:text-gray-100">
                {count as number}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}



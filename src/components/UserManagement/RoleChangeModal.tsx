import React, { useState } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { toast } from "sonner";
import { RoleBadge } from "../UserProfile/RoleBadge";
import { Id } from "../../../convex/_generated/dataModel";

interface User {
  _id: Id<"users">;
  name: string;
  email: string;
  role: string;
  department: string;
  phone: string;
  bio: string;
  _creationTime: number;
}

interface RoleChangeModalProps {
  user: User;
  onClose: () => void;
  onSuccess: () => void;
}

export function RoleChangeModal({ user, onClose, onSuccess }: RoleChangeModalProps) {
  const [selectedRole, setSelectedRole] = useState(user.role);
  const [reason, setReason] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showHistory, setShowHistory] = useState(false);

  const requestRoleChange = useMutation(api.roleManagement.requestRoleChange);
  const roleHistory = useQuery(
    api.roleManagement.getUserRoleHistory,
    showHistory ? { userId: user._id } : "skip"
  );

  const roles = [
    { value: "staff", label: "Staff Member", description: "Basic access to system features" },
    { value: "admin", label: "Administrator", description: "Advanced access with management capabilities" },
    { value: "master", label: "Master", description: "Full system access with role management" },
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (selectedRole === user.role) {
      toast.error("Please select a different role");
      return;
    }

    if (!reason.trim()) {
      toast.error("Please provide a reason for the role change");
      return;
    }

    try {
      setIsSubmitting(true);
      const result = await requestRoleChange({
        targetUserId: user._id,
        requestedRole: selectedRole,
        reason: reason.trim(),
      });

      if (result.immediate) {
        toast.success("Role updated successfully");
      } else {
        toast.success("Role change request submitted for approval");
      }
      
      onSuccess();
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to update role");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-charcoal dark:text-gray-100">
              Change User Role
            </h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              ✕
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* User Info */}
          <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <div>
                <h4 className="font-semibold text-charcoal dark:text-gray-100">
                  {user.name}
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {user.email}
                </p>
              </div>
              <div className="text-right">
                <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Current Role</p>
                <RoleBadge role={user.role} />
              </div>
            </div>
            {user.department && (
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Department: {user.department}
              </p>
            )}
          </div>

          {/* Tabs */}
          <div className="border-b border-gray-200 dark:border-gray-700 mb-6">
            <nav className="flex space-x-8">
              <button
                onClick={() => setShowHistory(false)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  !showHistory
                    ? "border-primary text-primary"
                    : "border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                }`}
              >
                Change Role
              </button>
              <button
                onClick={() => setShowHistory(true)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  showHistory
                    ? "border-primary text-primary"
                    : "border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                }`}
              >
                Role History
              </button>
            </nav>
          </div>

          {/* Tab Content */}
          {!showHistory ? (
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Role Selection */}
              <div>
                <label className="form-label mb-4">Select New Role</label>
                <div className="space-y-3">
                  {roles.map((role) => (
                    <label
                      key={role.value}
                      className={`flex items-start gap-3 p-4 border rounded-lg cursor-pointer transition-colors ${
                        selectedRole === role.value
                          ? "border-primary bg-primary/5 dark:bg-primary/10"
                          : "border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"
                      }`}
                    >
                      <input
                        type="radio"
                        name="role"
                        value={role.value}
                        checked={selectedRole === role.value}
                        onChange={(e) => setSelectedRole(e.target.value)}
                        className="mt-1"
                      />
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <RoleBadge role={role.value} />
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {role.description}
                        </p>
                      </div>
                    </label>
                  ))}
                </div>
              </div>

              {/* Reason */}
              <div>
                <label className="form-label">
                  Reason for Role Change <span className="text-red-500">*</span>
                </label>
                <textarea
                  value={reason}
                  onChange={(e) => setReason(e.target.value)}
                  placeholder="Explain why this role change is necessary..."
                  className="form-input h-24 resize-none"
                  required
                />
              </div>

              {/* Actions */}
              <div className="flex gap-3 pt-4">
                <button
                  type="button"
                  onClick={onClose}
                  className="btn-secondary flex-1"
                  disabled={isSubmitting}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="btn-primary flex-1"
                  disabled={isSubmitting || selectedRole === user.role}
                >
                  {isSubmitting ? "Processing..." : "Update Role"}
                </button>
              </div>
            </form>
          ) : (
            <RoleHistoryTab history={roleHistory} />
          )}
        </div>
      </div>
    </div>
  );
}

// Role History Tab Component
function RoleHistoryTab({ history }: { history: any[] | undefined }) {
  if (!history) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (history.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="text-4xl mb-4">📋</div>
        <h4 className="text-lg font-semibold text-charcoal dark:text-gray-100 mb-2">
          No Role Changes
        </h4>
        <p className="text-gray-600 dark:text-gray-400">
          This user has not had any role changes yet.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h4 className="font-semibold text-charcoal dark:text-gray-100 mb-4">
        Role Change History
      </h4>
      
      <div className="space-y-3">
        {history.map((entry, index) => (
          <div key={entry._id} className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-3">
                <RoleBadge role={entry.previousRole} />
                <span className="text-gray-400">→</span>
                <RoleBadge role={entry.newRole} />
              </div>
              <span className="text-xs text-gray-500 dark:text-gray-400">
                {new Date(entry.createdAt).toLocaleDateString()}
              </span>
            </div>
            
            <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">
              Changed by: <span className="font-medium">{entry.changedByUser.name}</span>
            </div>
            
            {entry.reason && (
              <div className="text-sm text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-700 p-2 rounded">
                <strong>Reason:</strong> {entry.reason}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}

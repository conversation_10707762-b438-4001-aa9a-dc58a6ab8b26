import React, { useState } from "react";
import { useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { BrandingSettings } from "../BrandingSettings";
import { EmailTemplates } from "../EmailTemplates/EmailTemplates";

export function Settings() {
  const [activeTab, setActiveTab] = useState("branding");
  const user = useQuery(api.auth.loggedInUser);

  const isAdmin = user?.role === "admin" || user?.role === "master";

  if (!isAdmin) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <div className="text-center">
          <div className="text-gray-400 dark:text-gray-500 text-6xl mb-4">🔒</div>
          <h2 className="text-xl font-semibold text-charcoal dark:text-gray-100 mb-2">
            Access Restricted
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            You need admin privileges to access settings.
          </p>
        </div>
      </div>
    );
  }

  const tabs = [
    { id: "branding", label: "Company Branding", icon: "🏢" },
    { id: "email-templates", label: "Email Templates", icon: "📧" },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl md:text-3xl font-bold text-charcoal dark:text-gray-100">
          Settings
        </h1>
        <p className="text-gray-600 dark:text-gray-400 text-sm md:text-base">
          Manage your company settings and configurations
        </p>
      </div>

      {/* Tabs */}
      <div className="card">
        <div className="border-b border-gray-100 dark:border-gray-700">
          <nav className="flex overflow-x-auto px-4 md:px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center gap-2 py-3 md:py-4 px-3 md:px-4 border-b-2 font-medium text-sm whitespace-nowrap transition-colors touch-manipulation ${
                  activeTab === tab.id
                    ? "border-primary text-primary"
                    : "border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
                }`}
              >
                <span>{tab.icon}</span>
                <span className="hidden sm:inline">{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>

        <div className="p-4 md:p-6">
          <TabContent activeTab={activeTab} />
        </div>
      </div>
    </div>
  );
}

// Tab content component
function TabContent({ activeTab }: { activeTab: string }) {
  switch (activeTab) {
    case "branding":
      return <BrandingSettingsTab />;
    case "email-templates":
      return <EmailTemplatesTab />;
    default:
      return <BrandingSettingsTab />;
  }
}

// Branding settings tab wrapper
function BrandingSettingsTab() {
  return (
    <div>
      <BrandingSettings />
    </div>
  );
}

// Email templates tab wrapper
function EmailTemplatesTab() {
  return (
    <div>
      <EmailTemplates />
    </div>
  );
}

import React from "react";
import { useTheme } from "../../hooks/useTheme";
import { InvoiceLineItemsProps } from "./types";

export function InvoiceLineItems({ 
  items, 
  mode = 'preview', 
  products, 
  onItemChange, 
  onAddItem, 
  onRemoveItem 
}: InvoiceLineItemsProps) {
  const { isDark } = useTheme();

  if (mode === 'form') {
    return (
      <div className={`border ${isDark ? 'border-gray-600' : 'border-gray-300'} rounded-md p-4`}>
        <div className="flex justify-between items-center mb-3">
          <h3 className={`text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>Line Items</h3>
          <button
            type="button"
            onClick={onAddItem}
            className="text-sm text-blue-600 hover:text-blue-700 font-medium"
          >
            + Add Item
          </button>
        </div>
        
        <div className="space-y-3">
          {items.map((item, index) => (
            <div key={index} className="grid grid-cols-12 gap-2 items-start">
              {/* Product Selection */}
              <div className="col-span-5">
                <select
                  value={item.productId || ""}
                  onChange={(e) => onItemChange?.(index, 'productId', e.target.value)}
                  className={`w-full px-2 py-1 text-sm border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    isDark ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'
                  }`}
                >
                  <option value="">Select product</option>
                  {products?.map((product) => (
                    <option key={product._id} value={product._id}>
                      {product.name} - ${product.price}
                    </option>
                  ))}
                </select>
              </div>
              
              {/* Description */}
              <div className="col-span-3">
                <input
                  type="text"
                  value={item.description}
                  onChange={(e) => onItemChange?.(index, 'description', e.target.value)}
                  placeholder="Description"
                  className={`w-full px-2 py-1 text-sm border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    isDark ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900'
                  }`}
                />
              </div>
              
              {/* Quantity */}
              <div className="col-span-1">
                <input
                  type="number"
                  min="1"
                  value={item.quantity}
                  onChange={(e) => onItemChange?.(index, 'quantity', parseInt(e.target.value) || 1)}
                  className={`w-full px-2 py-1 text-sm border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    isDark ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'
                  }`}
                />
              </div>
              
              {/* Unit Price */}
              <div className="col-span-2">
                <input
                  type="number"
                  step="0.01"
                  value={item.unitPrice}
                  onChange={(e) => onItemChange?.(index, 'unitPrice', parseFloat(e.target.value) || 0)}
                  placeholder="Price"
                  className={`w-full px-2 py-1 text-sm border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    isDark ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900'
                  }`}
                />
              </div>
              
              {/* Remove Button */}
              <div className="col-span-1 flex justify-center">
                {items.length > 1 && (
                  <button
                    type="button"
                    onClick={() => onRemoveItem?.(index)}
                    className="text-red-600 hover:text-red-700 text-sm"
                  >
                    ✕
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Preview mode
  if (!items || items.length === 0) {
    return null;
  }

  return (
    <div>
      <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">Line Items</h3>
      <div className="border border-gray-200 dark:border-gray-600 rounded-md overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full text-xs">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="text-left px-2 py-2 text-gray-700 dark:text-gray-300 font-medium">Description</th>
                <th className="text-center px-2 py-2 text-gray-700 dark:text-gray-300 font-medium">Qty</th>
                <th className="text-right px-2 py-2 text-gray-700 dark:text-gray-300 font-medium">Price</th>
                <th className="text-right px-2 py-2 text-gray-700 dark:text-gray-300 font-medium">Total</th>
              </tr>
            </thead>
            <tbody>
              {items.map((item, index) => (
                <tr key={index} className="border-t border-gray-200 dark:border-gray-600">
                  <td className="px-2 py-2 text-charcoal dark:text-white">{item.description}</td>
                  <td className="text-center px-2 py-2 text-charcoal dark:text-white">{item.quantity}</td>
                  <td className="text-right px-2 py-2 text-charcoal dark:text-white">${item.unitPrice.toFixed(2)}</td>
                  <td className="text-right px-2 py-2 text-charcoal dark:text-white font-medium">
                    ${(item.quantity * item.unitPrice).toFixed(2)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

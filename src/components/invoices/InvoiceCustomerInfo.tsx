import React from "react";
import { InvoiceCustomerInfoProps } from "./types";

export function InvoiceCustomerInfo({ customer }: InvoiceCustomerInfoProps) {
  return (
    <div>
      <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">Customer Information</h3>
      <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg border-l-4 border-blue-500 dark:border-blue-400">
        <p className="text-charcoal dark:text-white font-semibold">{customer?.name || "N/A"}</p>
        {customer?.email && (
          <p className="text-gray-600 dark:text-gray-300 text-sm mt-1">{customer.email}</p>
        )}
        {customer?.phone && (
          <p className="text-gray-600 dark:text-gray-300 text-sm">{customer.phone}</p>
        )}
        {customer?.address && (
          <div className="text-gray-600 dark:text-gray-300 text-sm mt-1">
            <p>{customer.address}</p>
            {(customer.city || customer.state || customer.zipCode) && (
              <p>
                {customer.city && customer.city}
                {customer.city && customer.state && ", "}
                {customer.state && customer.state}
                {customer.zipCode && ` ${customer.zipCode}`}
              </p>
            )}
          </div>
        )}
        {customer?.company && (
          <p className="text-gray-600 dark:text-gray-300 text-sm mt-1">Company: {customer.company}</p>
        )}
      </div>
    </div>
  );
}

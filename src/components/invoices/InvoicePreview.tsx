import { useState } from "react";
import { useQuery, useMutation, useAction } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { toast } from "sonner";
import { generateInvoicePDF } from "../../utilities/pdfGenerator";
import { usePDFOperations } from "../../hooks/usePDFOperations";
import { StatusDropdown } from "../common/StatusDropdown";
import { InvoiceActions } from "./InvoiceActions";
import { InvoiceCustomerInfo } from "./InvoiceCustomerInfo";
import { InvoiceLineItems } from "./InvoiceLineItems";
import { InvoiceTotals } from "./InvoiceTotals";
import { InvoicePreviewProps } from "./types";

export function InvoicePreview({ invoice, onEdit }: InvoicePreviewProps) {
  const settings = useQuery(api.settings.get);
  const sendInvoiceEmail = useAction(api.invoiceEmail.sendInvoiceEmail);
  const updateInvoiceStatus = useMutation(api.invoices.updateStatus);
  const [isEmailSending, setIsEmailSending] = useState(false);

  // PDF operations hook
  const {
    hasPDF,
    pdfUrl,
    generatePDF,
    downloadPDF: downloadStoredPDF,
    downloadPDFDirect,
    removePDF,
    sendPDFEmail,
    generateAndSendPDF,
  } = usePDFOperations(invoice._id);

  // State for PDF email operations
  const [isPDFEmailSending, setIsPDFEmailSending] = useState(false);
  
  // Legacy PDF download function (generates PDF on client-side)
  const downloadPDFLegacy = () => {
    // Use the optimized PDF generator for single-page invoices
    if (!invoice || !settings) {
      toast.error("Invoice or company settings not available");
      return;
    }

    try {
      // Transform invoice data to match PDF generator interface
      const pdfInvoice = {
        ...invoice,
        items: invoice.items || [],
        customer: invoice.customer || undefined
      };

      // Use company settings as companyInfo
      generateInvoicePDF(pdfInvoice, settings);
      toast.success("PDF downloaded successfully!");
    } catch (error) {
      console.error("PDF generation error:", error);
      toast.error("Failed to generate PDF");
      // Fallback to browser print
      fallbackToBrowserPrint();
    }
  };

  // Enhanced PDF download function that uses stored PDF or generates new one
  const downloadPDF = async () => {
    if (hasPDF && pdfUrl) {
      // Use stored PDF if available
      await downloadStoredPDF();
    } else {
      // Generate and store PDF, then download
      const result = await generatePDF();
      if (result.success) {
        // After generation, download the stored PDF
        setTimeout(() => void downloadPDFDirect(), 1000);
      } else {
        // Fallback to legacy client-side generation
        downloadPDFLegacy();
      }
    }
  };

  const handleSendEmail = async () => {
    if (!invoice.customer?.email) {
      toast.error("Customer email not found. Please update the customer's email address before sending.");
      return;
    }

    setIsEmailSending(true);
    try {
      const result = await sendInvoiceEmail({ invoiceId: invoice._id });
      toast.success(`Invoice sent successfully to ${result.customerEmail}! Status updated to 'Sent'.`);
    } catch (error: any) {
      console.error("Email sending error:", error);
      toast.error(error.message || "Failed to send invoice email. Please try again.");
    } finally {
      setIsEmailSending(false);
    }
  };

  const handleSendPDFEmail = async () => {
    if (!invoice.customer?.email) {
      toast.error("Customer email not found. Please update the customer's email address before sending.");
      return;
    }

    setIsPDFEmailSending(true);
    try {
      let result;

      if (hasPDF) {
        // Send existing PDF
        result = await sendPDFEmail();
      } else {
        // Generate PDF and send
        result = await generateAndSendPDF();
      }

      if (result.success) {
        // Update invoice status if needed
        if (invoice.status === "draft") {
          await updateInvoiceStatus({
            id: invoice._id,
            status: "sent"
          });
        }
      }
    } catch (error: any) {
      console.error("PDF email sending error:", error);
      toast.error(error.message || "Failed to send PDF email. Please try again.");
    } finally {
      setIsPDFEmailSending(false);
    }
  };

  const handleStatusChange = async (newStatus: string) => {
    try {
      const result = await updateInvoiceStatus({
        id: invoice._id,
        status: newStatus
      });
      toast.success(`Invoice status updated from '${result.previousStatus}' to '${result.newStatus}'`);
    } catch (error: any) {
      console.error("Status update error:", error);
      toast.error(error.message || "Failed to update invoice status. Please try again.");
      throw error; // Re-throw to let StatusDropdown handle the error state
    }
  };

  const fallbackToBrowserPrint = () => {
    // Fallback: Create a new window with invoice content for browser printing
    const printWindow = window.open('', '_blank');
    if (!printWindow) return;
    
    // Build the invoice HTML
    const invoiceHTML = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Invoice ${invoice.invoiceNumber}</title>
        <style>
          @media print {
            body { margin: 0; }
            .no-print { display: none; }
          }
          body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
          }
          .header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
          }
          .company-info h1 {
            margin: 0;
            color: #2563eb;
          }
          .invoice-details {
            text-align: right;
          }
          .invoice-details h2 {
            margin: 0;
            color: #333;
          }
          .section {
            margin-bottom: 20px;
          }
          .section h3 {
            color: #666;
            font-size: 14px;
            margin-bottom: 5px;
          }
          .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
          }
          .total-section {
            text-align: right;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #eee;
          }
          .total-amount {
            font-size: 24px;
            font-weight: bold;
            color: #2563eb;
          }
          .status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            ${invoice.status === 'paid' ? 'background: #d1fae5; color: #065f46;' :
              invoice.status === 'sent' ? 'background: #dbeafe; color: #1e40af;' :
              invoice.status === 'overdue' ? 'background: #fee2e2; color: #991b1b;' :
              invoice.status === 'cancelled' ? 'background: #f3f4f6; color: #6b7280;' :
              'background: #fef3c7; color: #92400e;'}
          }
          .footer {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            text-align: center;
            color: #666;
            font-size: 12px;
          }
        </style>
      </head>
      <body>
        <div class="header">
          <div class="company-info">
            <h1>${settings?.companyName || 'HVAC Company'}</h1>
            ${settings?.address ? `<p>${settings.address}<br>${settings.city}, ${settings.state} ${settings.zipCode}</p>` : ''}
            ${settings?.contactPhone ? `<p>Phone: ${settings.contactPhone}</p>` : ''}
            ${settings?.contactEmail ? `<p>Email: ${settings.contactEmail}</p>` : ''}
            ${settings?.taxId ? `<p>Tax ID: ${settings.taxId}</p>` : ''}
          </div>
          <div class="invoice-details">
            <h2>INVOICE</h2>
            <p><strong>#${invoice.invoiceNumber}</strong></p>
            <p>Date: ${new Date(invoice.issueDate).toLocaleDateString()}</p>
            <p>Due: ${new Date(invoice.dueDate).toLocaleDateString()}</p>
            <p class="status">${invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}</p>
          </div>
        </div>
        
        <div class="grid">
          <div class="section">
            <h3>Bill To:</h3>
            <p><strong>${invoice.customer?.name || 'N/A'}</strong></p>
            ${invoice.customer?.address ? `<p>${invoice.customer.address}<br>${invoice.customer.city}, ${invoice.customer.state} ${invoice.customer.zipCode}</p>` : ''}
            ${invoice.customer?.email ? `<p>${invoice.customer.email}</p>` : ''}
            ${invoice.customer?.phone ? `<p>${invoice.customer.phone}</p>` : ''}
          </div>
        </div>
        
        ${invoice.items && invoice.items.length > 0 ? `
        <div class="section">
          <h3>Line Items</h3>
          <table style="width: 100%; border-collapse: collapse; margin-top: 10px;">
            <thead>
              <tr style="background: #f3f4f6;">
                <th style="text-align: left; padding: 8px; border: 1px solid #e5e7eb;">Description</th>
                <th style="text-align: center; padding: 8px; border: 1px solid #e5e7eb;">Qty</th>
                <th style="text-align: right; padding: 8px; border: 1px solid #e5e7eb;">Price</th>
                <th style="text-align: right; padding: 8px; border: 1px solid #e5e7eb;">Total</th>
              </tr>
            </thead>
            <tbody>
              ${invoice.items.map((item: any) => `
                <tr>
                  <td style="padding: 8px; border: 1px solid #e5e7eb;">${item.description}</td>
                  <td style="text-align: center; padding: 8px; border: 1px solid #e5e7eb;">${item.quantity}</td>
                  <td style="text-align: right; padding: 8px; border: 1px solid #e5e7eb;">${item.unitPrice.toFixed(2)}</td>
                  <td style="text-align: right; padding: 8px; border: 1px solid #e5e7eb;">${(item.quantity * item.unitPrice).toFixed(2)}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </div>
        ` : ''}
        
        ${invoice.notes ? `
        <div class="section">
          <h3>Notes:</h3>
          <p>${invoice.notes.replace(/\n/g, '<br>')}</p>
        </div>
        ` : ''}
        
        <div class="total-section">
          <p>Subtotal: ${(invoice.subtotal || invoice.total).toFixed(2)}</p>
          ${invoice.taxAmount ? `<p>Tax (${invoice.taxRate}%): ${invoice.taxAmount.toFixed(2)}</p>` : ''}
          ${invoice.discountAmount ? `<p>Discount: -${invoice.discountAmount.toFixed(2)}</p>` : ''}
          <p class="total-amount">Total: ${invoice.total.toFixed(2)}</p>
        </div>
        
        ${settings?.invoiceFooter ? `
        <div class="footer">
          <p>${settings.invoiceFooter}</p>
        </div>
        ` : ''}
        
        <div class="no-print" style="margin-top: 30px; text-align: center;">
          <button onclick="window.print()" style="padding: 10px 20px; background: #2563eb; color: white; border: none; border-radius: 4px; cursor: pointer;">Print / Save as PDF</button>
          <button onclick="window.close()" style="margin-left: 10px; padding: 10px 20px; background: #6b7280; color: white; border: none; border-radius: 4px; cursor: pointer;">Close</button>
        </div>
      </body>
      </html>
    `;
    
    printWindow.document.write(invoiceHTML);
    printWindow.document.close();
    
    // Auto-trigger print dialog after a short delay
    setTimeout(() => {
      printWindow.print();
    }, 250);
  };
  
  return (
    <div className="flex flex-col h-full">
      {/* Header with actions */}
      <div className="flex flex-col gap-3 pb-4 border-b border-gray-200 dark:border-gray-700">
        <h2 className="text-xl font-semibold text-charcoal dark:text-white">
          Invoice #{invoice.invoiceNumber}
        </h2>
        <InvoiceActions
          invoice={invoice}
          settings={settings}
          onEdit={onEdit}
          isEmailSending={isEmailSending}
          onSendEmail={handleSendEmail}
          onDownloadPDF={() => void downloadPDF()}
          onPrint={fallbackToBrowserPrint}
          hasPDF={hasPDF}
          onGeneratePDF={async () => { await generatePDF(); }}
          onDeletePDF={async () => { await removePDF(); }}
          showPDFManagement={true}
          isPDFEmailSending={isPDFEmailSending}
          onSendPDFEmail={handleSendPDFEmail}
          showPDFEmailDelivery={true}
        />
      </div>

      {/* Scrollable content */}
      <div className="flex-1 overflow-y-auto space-y-4 pt-4">
        <InvoiceCustomerInfo customer={invoice.customer} />

        {/* Job Information */}
        {invoice.job && (
          <div>
            <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">Related Job</h3>
            <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                <div>
                  <span className="font-medium text-charcoal dark:text-white">{invoice.job.title}</span>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">Status: </span>
                  <span className="text-charcoal dark:text-white capitalize">{invoice.job.status}</span>
                </div>
                <div className="md:col-span-2">
                  <span className="text-gray-600 dark:text-gray-400">Description: </span>
                  <span className="text-charcoal dark:text-white">{invoice.job.description}</span>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="grid grid-cols-2 gap-4">
          <div>
            <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">Date</h3>
            <p className="text-charcoal dark:text-white text-sm">{new Date(invoice.issueDate).toLocaleDateString()}</p>
          </div>

          <div>
            <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">Due Date</h3>
            <p className="text-charcoal dark:text-white text-sm">
              {new Date(invoice.dueDate).toLocaleDateString()}
            </p>
          </div>
        </div>

        <div>
          <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">Status</h3>
          <StatusDropdown
            currentStatus={invoice.status}
            onStatusChange={(status) => void handleStatusChange(status)}
            size="md"
          />
        </div>

        <InvoiceLineItems items={invoice.items} mode="preview" />

        <InvoiceTotals 
          items={invoice.items} 
          taxRate={invoice.taxRate} 
          mode="preview" 
          invoice={invoice} 
        />

        {invoice.notes && (
          <div>
            <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">Notes</h3>
            <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
              <p className="text-charcoal dark:text-white text-sm whitespace-pre-line">{invoice.notes}</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

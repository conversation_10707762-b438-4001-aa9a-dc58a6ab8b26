// Use Convex generated types for better type safety and consistency
import { Doc, Id } from "../../../convex/_generated/dataModel";

// Re-export Convex types for convenience
export type Invoice = Doc<"invoices">;
export type Customer = Doc<"customers">;
export type Job = Doc<"jobs">;
export type Product = Doc<"products">;
export type CompanySettings = Doc<"settings">;
export type InvoiceItem = Invoice["items"][0]; // Extract item type from invoice items array

// Enhanced types for components that include related data
export interface InvoiceWithCustomer extends Invoice {
  customer: Customer | null;
  job?: Job | null;
}

// Form-specific types that don't need all database fields
export interface InvoiceFormData {
  customerId: Id<"customers">;
  jobId?: Id<"jobs"> | "";
  dueDate: string; // Form uses string, converted to number for database
  taxRate: number;
  notes: string;
  items: Array<{
    productId?: Id<"products">;
    description: string;
    quantity: number;
    unitPrice: number;
  }>;
}

// Component prop interfaces
export interface InvoicePreviewProps {
  invoice: InvoiceWithCustomer;
  onEdit: () => void;
}

export interface InvoiceFormProps {
  invoice?: InvoiceWithCustomer | null;
  currentUserId: Id<"users">;
  onClose: () => void;
  onSuccess: () => void;
}

// PDF-related types
export interface PDFGenerationResult {
  success: boolean;
  storageId?: string;
  message: string;
}

export interface PDFUrlResponse {
  hasPdf: boolean;
  pdfUrl?: string;
  invoiceNumber?: string;
  message?: string;
}

export interface PDFActionResult {
  success: boolean;
  message: string;
}

// PDF Email delivery types
export interface PDFEmailDeliveryResult {
  success: boolean;
  message: string;
  emailId?: string;
  pdfStorageId?: string;
  customerEmail?: string;
  invoiceNumber?: string;
  downloadUrl?: string;
}

export interface PDFEmailDeliveryOptions {
  generatePDF: boolean; // Whether to generate a new PDF or use existing
  includeAttachment: boolean; // Whether to include PDF as email attachment
  includeDownloadLink: boolean; // Whether to include download link in email
  cleanupOldPDF: boolean; // Whether to cleanup old PDF before generating new one
}

export interface InvoiceActionsProps {
  invoice: InvoiceWithCustomer;
  settings?: CompanySettings;
  onEdit: () => void;
  isEmailSending?: boolean;
  onSendEmail: () => Promise<void>;
  onDownloadPDF: () => void;
  onPrint: () => void;
  // PDF management options
  hasPDF?: boolean;
  onGeneratePDF?: () => Promise<void>;
  onDeletePDF?: () => Promise<void>;
  showPDFManagement?: boolean;
  // PDF Email delivery options
  isPDFEmailSending?: boolean;
  onSendPDFEmail?: () => Promise<void>;
  showPDFEmailDelivery?: boolean;
}

export interface InvoiceCustomerInfoProps {
  customer?: Customer | null;
}

export interface InvoiceLineItemsProps {
  items: InvoiceItem[];
  mode?: 'preview' | 'form';
  products?: Product[];
  onItemChange?: (index: number, field: string, value: any) => void;
  onAddItem?: () => void;
  onRemoveItem?: (index: number) => void;
}

export interface InvoiceTotalsProps {
  items: InvoiceItem[];
  taxRate: number;
  mode?: 'preview' | 'form';
  invoice?: Invoice;
}

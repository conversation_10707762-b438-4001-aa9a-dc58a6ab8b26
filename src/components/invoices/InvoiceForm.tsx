import React, { useState, useCallback } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { toast } from "sonner";
import { useTheme } from "../../hooks/useTheme";
import { InvoiceLineItems } from "./InvoiceLineItems";
import { InvoiceTotals } from "./InvoiceTotals";
import { Id } from "../../../convex/_generated/dataModel";
import { InvoiceFormData, InvoiceFormProps } from "./types";

// Local types for form-specific line items
interface LineItem {
  productId?: Id<"products">;
  description: string;
  quantity: number;
  unitPrice: number;
}

// Utility functions
const generateInvoiceNumber = (): string => {
  const date = new Date().toISOString().slice(0, 7).replace("-", "");
  const random = Math.floor(Math.random() * 1000)
    .toString()
    .padStart(3, "0");
  return `INV-${date}-${random}`;
};

const formatDateForInput = (date: string | number): string => {
  return new Date(date).toISOString().split("T")[0];
};

const createEmptyLineItem = (): LineItem => ({
  productId: undefined,
  description: "",
  quantity: 1,
  unitPrice: 0,
});

export function InvoiceForm({
  invoice,
  onClose,
  onSuccess,
}: InvoiceFormProps) {
  // Mutations and queries
  const createInvoice = useMutation(api.invoices.create);
  const updateInvoice = useMutation(api.invoices.update);
  const customers = useQuery(api.customers.list);
  const products = useQuery(api.products.list);
  const { isDark } = useTheme();

  // Form state - using local interface for form data
  const [formData, setFormData] = useState(() => ({
    customerId: invoice?.customerId || ("" as Id<"customers"> | ""),
    jobId: invoice?.jobId || ("" as Id<"jobs"> | ""),
    dueDate: invoice?.dueDate ? formatDateForInput(invoice.dueDate) : "",
    taxRate: invoice?.taxRate || 0,
    notes: invoice?.notes || "",
    items: invoice?.items?.length ? invoice.items : [createEmptyLineItem()],
  }));

  // Get jobs for the selected customer
  const customerJobs = useQuery(
    api.jobs.getByCustomer,
    formData.customerId ? { customerId: formData.customerId as Id<"customers"> } : "skip"
  );

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Validation
  const validateForm = useCallback((): Record<string, string> => {
    const newErrors: Record<string, string> = {};

    if (!formData.customerId) {
      newErrors.customerId = "Customer is required";
    }

    if (formData.items.length === 0) {
      newErrors.items = "At least one line item is required";
    }

    formData.items.forEach((item, index) => {
      if (!item.description.trim()) {
        newErrors[`item-${index}-description`] = "Description is required";
      }
      if (item.quantity <= 0) {
        newErrors[`item-${index}-quantity`] = "Quantity must be positive";
      }
      if (item.unitPrice < 0) {
        newErrors[`item-${index}-unitPrice`] = "Unit price cannot be negative";
      }
    });

    if (formData.taxRate < 0 || formData.taxRate > 100) {
      newErrors.taxRate = "Tax rate must be between 0 and 100";
    }

    return newErrors;
  }, [formData]);

  // Form field handlers
  const updateFormField = useCallback(
    <K extends keyof InvoiceFormData>(field: K, value: InvoiceFormData[K]) => {
      setFormData((prev) => {
        // If customer changes, clear the job selection
        if (field === "customerId") {
          return { ...prev, [field]: value, jobId: "" };
        }
        return { ...prev, [field]: value };
      });
      // Clear error when field is updated
      if (errors[field]) {
        setErrors((prev) => {
          const newErrors = { ...prev };
          delete newErrors[field];
          return newErrors;
        });
      }
    },
    [errors]
  );

  // Line item handlers
  const addLineItem = useCallback(() => {
    setFormData((prev) => ({
      ...prev,
      items: [...prev.items, createEmptyLineItem()],
    }));
  }, []);

  const removeLineItem = useCallback((index: number) => {
    setFormData((prev) => ({
      ...prev,
      items: prev.items.filter((_, i) => i !== index),
    }));
    // Clear related errors
    setErrors((prev) => {
      const newErrors = { ...prev };
      Object.keys(newErrors).forEach((key) => {
        if (key.startsWith(`item-${index}-`)) {
          delete newErrors[key];
        }
      });
      return newErrors;
    });
  }, []);

  const updateLineItem = useCallback(
    (index: number, field: string, value: any) => {
      setFormData((prev) => {
        const updatedItems = [...prev.items];
        const updatedItem = { ...updatedItems[index] } as LineItem;

        if (field === "productId") {
          if (value && products) {
            const product = products.find((p) => p._id === value);
            if (product) {
              updatedItem.productId = value as Id<"products">;
              updatedItem.description = product.name;
              updatedItem.unitPrice = product.price;
            }
          } else {
            updatedItem.productId = undefined;
          }
        } else {
          (updatedItem as any)[field] = value;
        }

        updatedItems[index] = updatedItem;
        return { ...prev, items: updatedItems };
      });

      // Clear related errors
      const errorKey = `item-${index}-${field}`;
      if (errors[errorKey]) {
        setErrors((prev) => {
          const newErrors = { ...prev };
          delete newErrors[errorKey];
          return newErrors;
        });
      }
    },
    [products, errors]
  );

  // Form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const validationErrors = validateForm();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      toast.error("Please fix the errors before submitting");
      return;
    }

    setIsSubmitting(true);
    setErrors({});

    try {
      const invoiceData = {
        invoiceNumber: invoice?.invoiceNumber || generateInvoiceNumber(),
        customerId: formData.customerId as Id<"customers">,
        jobId: formData.jobId && formData.jobId !== "" ? (formData.jobId as Id<"jobs">) : undefined,
        issueDate: invoice?.issueDate
          ? formatDateForInput(invoice.issueDate)
          : formatDateForInput(Date.now()),
        dueDate: formData.dueDate || formatDateForInput(Date.now()),
        items: formData.items.map((item) => ({
          description: item.description.trim(),
          quantity: item.quantity,
          unitPrice: item.unitPrice,
        })),
        notes: formData.notes.trim(),
        tax: formData.taxRate,
      };

      if (invoice) {
        await updateInvoice({
          id: invoice._id,
          ...invoiceData,
        });
        toast.success("Invoice updated successfully");
      } else {
        await createInvoice(invoiceData);
        toast.success("Invoice created successfully");
      }

      onSuccess();
    } catch (error) {
      console.error("Error saving invoice:", error);
      toast.error(
        `Failed to ${invoice ? "update" : "create"} invoice. Please try again.`
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  // Input styling
  const inputClassName = `w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors ${
    isDark
      ? "bg-gray-700 border-gray-600 text-white placeholder-gray-400"
      : "bg-white border-gray-300 text-gray-900 placeholder-gray-500"
  }`;

  const errorInputClassName = `w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-red-500 focus:border-transparent transition-colors ${
    isDark
      ? "bg-gray-700 border-red-500 text-white placeholder-gray-400"
      : "bg-white border-red-500 text-gray-900 placeholder-gray-500"
  }`;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div
        className={`${
          isDark ? "bg-gray-800 text-white" : "bg-white text-gray-900"
        } rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto shadow-xl`}
      >
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold">
            {invoice ? "Edit Invoice" : "Create New Invoice"}
          </h2>
          <button
            onClick={onClose}
            disabled={isSubmitting}
            className={`p-2 rounded-full hover:bg-opacity-20 transition-colors ${
              isDark ? "hover:bg-white" : "hover:bg-gray-900"
            }`}
          >
            <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
              <path
                fillRule="evenodd"
                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Customer Selection */}
            <div>
              <label className="block text-sm font-medium mb-2">
                Customer <span className="text-red-500">*</span>
              </label>
              <select
                value={formData.customerId}
                onChange={(e) =>
                  updateFormField("customerId", e.target.value as any)
                }
                className={
                  errors.customerId ? errorInputClassName : inputClassName
                }
                disabled={isSubmitting}
              >
                <option value="">Select a customer</option>
                {customers?.map((customer) => (
                  <option key={customer._id} value={customer._id}>
                    {customer.name}
                  </option>
                ))}
              </select>
              {errors.customerId && (
                <p className="text-red-500 text-sm mt-1">{errors.customerId}</p>
              )}
            </div>

            {/* Job Selection */}
            <div>
              <label className="block text-sm font-medium mb-2">
                Related Job (Optional)
              </label>
              <select
                value={formData.jobId || ""}
                onChange={(e) =>
                  updateFormField("jobId", e.target.value as any)
                }
                className={inputClassName}
                disabled={isSubmitting || !formData.customerId}
              >
                <option value="">No job selected</option>
                {customerJobs?.map((job) => (
                  <option key={job._id} value={job._id}>
                    {job.title} - {job.status}
                  </option>
                ))}
              </select>
              {!formData.customerId && (
                <p className="text-gray-500 text-sm mt-1">
                  Select a customer first to see available jobs
                </p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Due Date */}
            <div>
              <label className="block text-sm font-medium mb-2">Due Date</label>
              <input
                type="date"
                value={formData.dueDate}
                onChange={(e) => updateFormField("dueDate", e.target.value)}
                className={inputClassName}
                disabled={isSubmitting}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Tax Rate */}
            <div>
              <label className="block text-sm font-medium mb-2">
                Tax Rate (%)
              </label>
              <input
                type="number"
                step="0.01"
                min="0"
                max="100"
                value={formData.taxRate}
                onChange={(e) =>
                  updateFormField("taxRate", parseFloat(e.target.value) || 0)
                }
                className={errors.taxRate ? errorInputClassName : inputClassName}
                disabled={isSubmitting}
                placeholder="0.00"
              />
              {errors.taxRate && (
                <p className="text-red-500 text-sm mt-1">{errors.taxRate}</p>
              )}
            </div>
          </div>

          {/* Notes */}
          <div>
            <label className="block text-sm font-medium mb-2">Notes</label>
            <textarea
              value={formData.notes}
              onChange={(e) => updateFormField("notes", e.target.value)}
              rows={3}
              className={inputClassName}
              disabled={isSubmitting}
              placeholder="Optional notes for this invoice..."
            />
          </div>

          {/* Line Items */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Line Items</h3>
            <InvoiceLineItems
              items={formData.items}
              mode="form"
              products={products}
              onItemChange={updateLineItem}
              onAddItem={addLineItem}
              onRemoveItem={removeLineItem}
            />
            {errors.items && (
              <p className="text-red-500 text-sm mt-2">{errors.items}</p>
            )}
          </div>

          {/* Totals */}
          <div className="border-t pt-4">
            <InvoiceTotals
              items={formData.items}
              taxRate={formData.taxRate}
              mode="form"
            />
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 pt-6 border-t">
            <button
              type="submit"
              disabled={isSubmitting}
              className="flex-1 bg-blue-600 text-white py-3 px-6 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
            >
              {isSubmitting ? (
                <span className="flex items-center justify-center">
                  <svg
                    className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  {invoice ? "Updating..." : "Creating..."}
                </span>
              ) : (
                `${invoice ? "Update" : "Create"} Invoice`
              )}
            </button>
            <button
              type="button"
              onClick={onClose}
              disabled={isSubmitting}
              className={`flex-1 py-3 px-6 rounded-md transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed ${
                isDark
                  ? "bg-gray-600 text-white hover:bg-gray-700"
                  : "bg-gray-300 text-gray-700 hover:bg-gray-400"
              }`}
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
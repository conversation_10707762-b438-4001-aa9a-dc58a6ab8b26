import React from "react";
import { InvoiceTotalsProps } from "./types";

export function InvoiceTotals({ items, taxRate, mode = 'preview', invoice }: InvoiceTotalsProps) {
  const subtotal = items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);
  const taxAmount = subtotal * (taxRate / 100);
  const total = subtotal + taxAmount;

  if (mode === 'form') {
    return (
      <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-600">
        <div className="space-y-1 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-400">Subtotal:</span>
            <span className="font-medium">${subtotal.toFixed(2)}</span>
          </div>
          {taxRate > 0 && (
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Tax ({taxRate}%):</span>
              <span className="font-medium">${taxAmount.toFixed(2)}</span>
            </div>
          )}
          <div className="flex justify-between font-semibold">
            <span>Total:</span>
            <span>${total.toFixed(2)}</span>
          </div>
        </div>
      </div>
    );
  }

  // Preview mode
  return (
    <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
      <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">Total Amount</h3>
      <p className="text-charcoal dark:text-white text-xl font-bold">
        ${(invoice?.total || total).toFixed(2)}
      </p>
    </div>
  );
}

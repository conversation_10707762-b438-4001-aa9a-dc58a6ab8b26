import { InvoiceActionsProps } from "./types";

export function InvoiceActions({
  invoice,
  onEdit,
  isEmailSending = false,
  onSendEmail,
  onDownloadPDF,
  onPrint,
  hasPDF = false,
  onGeneratePDF,
  onDeletePDF,
  showPDFManagement = false,
  isPDFEmailSending = false,
  onSendPDFEmail,
  showPDFEmailDelivery = true
}: InvoiceActionsProps) {
  return (
    <div className="space-y-2">
      {/* Main action buttons */}
      <div className="flex flex-wrap gap-2">
        <button
          onClick={onDownloadPDF}
          className={`text-xs px-3 py-2 flex items-center gap-1 flex-1 min-w-0 transition-all duration-300 rounded-lg font-medium ${
            hasPDF
              ? 'btn-primary'
              : 'btn-secondary border-dashed border-2'
          }`}
          title={hasPDF ? 'Download stored PDF' : 'Generate and download PDF'}
        >
          <span>{hasPDF ? '📄' : '📝'}</span>
          <span className="truncate">{hasPDF ? 'PDF' : 'Generate PDF'}</span>
        </button>
        <button
          onClick={onPrint}
          className="btn-secondary text-xs px-3 py-2 flex items-center gap-1 flex-1 min-w-0"
        >
          <span>🖨️</span>
          <span className="truncate">Print</span>
        </button>
        <button
          onClick={() => void onSendEmail()}
          disabled={isEmailSending || !invoice.customer?.email}
          className={`text-xs px-3 py-2 flex items-center gap-1 flex-1 min-w-0 transition-all duration-300 rounded-lg font-medium ${
            isEmailSending || !invoice.customer?.email
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed opacity-50'
              : 'btn-accent hover:bg-accent/90'
          }`}
          title={!invoice.customer?.email ? 'Customer email required' : 'Send invoice notification via email'}
        >
          <span>{isEmailSending ? '⏳' : '📧'}</span>
          <span className="truncate">{isEmailSending ? 'Sending...' : 'Email'}</span>
        </button>
        <button
          onClick={onEdit}
          className="btn-secondary text-xs px-3 py-2 flex-1 min-w-0"
        >
          Edit
        </button>
      </div>

      {/* PDF Email Delivery section */}
      {showPDFEmailDelivery && onSendPDFEmail && (
        <div className="border-t pt-2">
          <div className="flex items-center justify-between text-xs text-gray-600 dark:text-gray-400 mb-2">
            <span>PDF Email Delivery</span>
            <span className={`px-2 py-1 rounded-full text-xs ${
              hasPDF
                ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
            }`}>
              {hasPDF ? 'PDF Ready' : 'Will Generate'}
            </span>
          </div>
          <div className="flex gap-2">
            <button
              onClick={() => void onSendPDFEmail()}
              disabled={isPDFEmailSending || !invoice.customer?.email}
              className={`text-xs px-3 py-2 flex items-center gap-1 flex-1 transition-all duration-300 rounded-lg font-medium ${
                isPDFEmailSending || !invoice.customer?.email
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed opacity-50'
                  : 'bg-gradient-to-r from-green-500 to-green-600 text-white hover:from-green-600 hover:to-green-700'
              }`}
              title={!invoice.customer?.email ? 'Customer email required' : hasPDF ? 'Send existing PDF via email' : 'Generate PDF and send via email'}
            >
              <span>{isPDFEmailSending ? '⏳' : '📄'}</span>
              <span className="truncate">
                {isPDFEmailSending ? 'Sending...' : hasPDF ? 'Send PDF' : 'Generate & Send PDF'}
              </span>
            </button>
          </div>
        </div>
      )}

      {/* PDF Management section */}
      {showPDFManagement && (
        <div className="border-t pt-2">
          <div className="flex items-center justify-between text-xs text-gray-600 dark:text-gray-400 mb-2">
            <span>PDF Management</span>
            <span className={`px-2 py-1 rounded-full text-xs ${
              hasPDF
                ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                : 'bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400'
            }`}>
              {hasPDF ? 'Stored' : 'Not Generated'}
            </span>
          </div>
          <div className="flex gap-2">
            {!hasPDF && onGeneratePDF && (
              <button
                onClick={() => void onGeneratePDF()}
                className="btn-primary text-xs px-3 py-1 flex items-center gap-1 flex-1"
              >
                <span>⚡</span>
                <span>Generate & Store</span>
              </button>
            )}
            {hasPDF && onDeletePDF && (
              <button
                onClick={() => void onDeletePDF()}
                className="btn-danger text-xs px-3 py-1 flex items-center gap-1"
              >
                <span>🗑️</span>
                <span>Delete PDF</span>
              </button>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

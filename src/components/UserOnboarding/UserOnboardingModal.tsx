import React, { useState } from "react";
import { useMutation } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { toast } from "sonner";

interface UserOnboardingModalProps {
  user: any;
  onComplete: () => void;
}

interface FormData {
  name: string;
  phone: string;
  department: string;
  role: string;
}

interface FormErrors {
  name?: string;
  phone?: string;
  department?: string;
  role?: string;
}

export function UserOnboardingModal({ user, onComplete }: UserOnboardingModalProps) {
  const updateProfile = useMutation(api.users.updateProfile);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<FormData>({
    name: user.name || "",
    phone: user.phone || "",
    department: user.department || "",
    role: user.role || "",
  });
  const [errors, setErrors] = useState<FormErrors>({});

  const validateStep = (step: number): boolean => {
    const newErrors: FormErrors = {};

    if (step === 1) {
      // Step 1: Basic Information
      if (!formData.name.trim()) {
        newErrors.name = "Full name is required";
      } else if (formData.name.trim().length < 2) {
        newErrors.name = "Name must be at least 2 characters";
      } else if (formData.name.trim().length > 100) {
        newErrors.name = "Name must be less than 100 characters";
      } else if (!/^[a-zA-Z\s\-'\.]+$/.test(formData.name.trim())) {
        newErrors.name = "Name can only contain letters, spaces, hyphens, apostrophes, and periods";
      }

      if (!formData.phone.trim()) {
        newErrors.phone = "Phone number is required";
      } else {
        // Remove all non-digit characters for validation
        const cleanPhone = formData.phone.replace(/\D/g, '');
        if (cleanPhone.length < 10) {
          newErrors.phone = "Phone number must be at least 10 digits";
        } else if (cleanPhone.length > 15) {
          newErrors.phone = "Phone number must be less than 15 digits";
        }
      }
    }

    if (step === 2) {
      // Step 2: Role and Department
      if (!formData.department) {
        newErrors.department = "Please select a department";
      }

      if (!formData.role) {
        newErrors.role = "Please select a role";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(2);
    }
  };

  const handleBack = () => {
    setCurrentStep(1);
    setErrors({});
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateStep(2)) {
      return;
    }

    setIsSubmitting(true);
    try {
      await updateProfile({
        name: formData.name.trim(),
        phone: formData.phone.trim(),
        department: formData.department,
        role: formData.role,
      });

      toast.success("Welcome! Your profile has been set up successfully.");
      onComplete();
    } catch (error: any) {
      console.error("Profile update error:", error);

      // Handle specific error types
      let errorMessage = "Failed to update profile. Please try again.";

      if (error?.message) {
        if (error.message.includes("Not authenticated")) {
          errorMessage = "Your session has expired. Please sign in again.";
        } else if (error.message.includes("validation")) {
          errorMessage = "Please check your information and try again.";
        } else if (error.message.includes("network") || error.message.includes("fetch")) {
          errorMessage = "Network error. Please check your connection and try again.";
        }
      }

      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatPhoneNumber = (value: string): string => {
    // Remove all non-digit characters
    const cleaned = value.replace(/\D/g, '');

    // Format as (XXX) XXX-XXXX for US numbers
    if (cleaned.length >= 10) {
      const match = cleaned.match(/^(\d{3})(\d{3})(\d{4})(\d*)$/);
      if (match) {
        const formatted = `(${match[1]}) ${match[2]}-${match[3]}`;
        return match[4] ? formatted + match[4] : formatted;
      }
    }

    // Partial formatting for incomplete numbers
    if (cleaned.length >= 6) {
      const match = cleaned.match(/^(\d{3})(\d{3})(\d*)$/);
      if (match) {
        return `(${match[1]}) ${match[2]}-${match[3]}`;
      }
    }

    if (cleaned.length >= 3) {
      const match = cleaned.match(/^(\d{3})(\d*)$/);
      if (match) {
        return `(${match[1]}) ${match[2]}`;
      }
    }

    return cleaned;
  };

  const handleInputChange = (field: keyof FormData, value: string) => {
    let processedValue = value;

    // Format phone number as user types
    if (field === 'phone') {
      processedValue = formatPhoneNumber(value);
    }

    setFormData(prev => ({ ...prev, [field]: processedValue }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const totalSteps = 2;
  const progressPercentage = (currentStep / totalSteps) * 100;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md max-h-[95vh] overflow-y-auto">
        {/* Header */}
        <div className="text-center mb-6">
          <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-2xl text-white">👋</span>
          </div>
          <h2 className="text-2xl font-bold text-charcoal dark:text-gray-100 mb-2">
            Welcome to Bernie's Heating CRM!
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Let's set up your profile to get started
          </p>
        </div>

        {/* Progress Bar */}
        <div className="mb-6">
          <div className="flex justify-between text-sm text-gray-500 dark:text-gray-400 mb-2">
            <span>Step {currentStep} of {totalSteps}</span>
            <span>{Math.round(progressPercentage)}% complete</span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              className="bg-primary h-2 rounded-full transition-all duration-300"
              style={{ width: `${progressPercentage}%` }}
            />
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          {currentStep === 1 && (
            <>
              <div>
                <label className="form-label">
                  Full Name <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  className={`form-input ${errors.name ? 'border-red-500' : ''}`}
                  placeholder="Enter your full name"
                  autoFocus
                />
                {errors.name && (
                  <p className="text-red-500 text-sm mt-1">{errors.name}</p>
                )}
              </div>

              <div>
                <label className="form-label">
                  Phone Number <span className="text-red-500">*</span>
                </label>
                <input
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => handleInputChange("phone", e.target.value)}
                  className={`form-input ${errors.phone ? 'border-red-500' : ''}`}
                  placeholder="Enter your phone number"
                />
                {errors.phone && (
                  <p className="text-red-500 text-sm mt-1">{errors.phone}</p>
                )}
              </div>

              <div className="pt-4">
                <button
                  type="button"
                  onClick={handleNext}
                  className="btn-primary w-full"
                >
                  Continue
                </button>
              </div>
            </>
          )}

          {currentStep === 2 && (
            <>
              <div>
                <label className="form-label">
                  Department <span className="text-red-500">*</span>
                </label>
                <select
                  value={formData.department}
                  onChange={(e) => handleInputChange("department", e.target.value)}
                  className={`form-input ${errors.department ? 'border-red-500' : ''}`}
                >
                  <option value="">Select your department</option>
                  <option value="installation">Installation</option>
                  <option value="maintenance">Maintenance</option>
                  <option value="sales">Sales</option>
                  <option value="management">Management</option>
                </select>
                {errors.department && (
                  <p className="text-red-500 text-sm mt-1">{errors.department}</p>
                )}
              </div>

              <div>
                <label className="form-label">
                  Role <span className="text-red-500">*</span>
                </label>
                <select
                  value={formData.role}
                  onChange={(e) => handleInputChange("role", e.target.value)}
                  className={`form-input ${errors.role ? 'border-red-500' : ''}`}
                >
                  <option value="">Select your role</option>
                  <option value="staff">Staff Member</option>
                  <option value="admin">Administrator</option>
                  <option value="master">Master</option>
                </select>
                {errors.role && (
                  <p className="text-red-500 text-sm mt-1">{errors.role}</p>
                )}
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Your role determines your access level in the system
                </p>
              </div>

              <div className="flex gap-3 pt-4">
                <button
                  type="button"
                  onClick={handleBack}
                  className="btn-secondary flex-1"
                  disabled={isSubmitting}
                >
                  Back
                </button>
                <button
                  type="submit"
                  className="btn-primary flex-1"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <span className="flex items-center justify-center">
                      <div className="spinner h-4 w-4 mr-2"></div>
                      Setting up...
                    </span>
                  ) : (
                    "Complete Setup"
                  )}
                </button>
              </div>
            </>
          )}
        </form>

        {/* Footer */}
        <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
          <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
            This information helps us personalize your experience and ensure proper access controls.
          </p>
        </div>
      </div>
    </div>
  );
}

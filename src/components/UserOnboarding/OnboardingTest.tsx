import React, { useState } from "react";
import { UserOnboardingModal } from "./UserOnboardingModal";

// Test component to simulate the onboarding flow
export function OnboardingTest() {
  const [showModal, setShowModal] = useState(false);
  const [testUser, setTestUser] = useState({
    name: "",
    email: "<EMAIL>",
    phone: "",
    department: "",
    role: "",
  });

  const handleTestComplete = () => {
    setShowModal(false);
    console.log("Onboarding completed!");
  };

  const resetUser = () => {
    setTestUser({
      name: "",
      email: "<EMAIL>", 
      phone: "",
      department: "",
      role: "",
    });
  };

  const setPartialUser = () => {
    setTestUser({
      name: "<PERSON>",
      email: "<EMAIL>",
      phone: "",
      department: "",
      role: "",
    });
  };

  const setCompleteUser = () => {
    setTestUser({
      name: "<PERSON>",
      email: "<EMAIL>",
      phone: "(*************",
      department: "installation",
      role: "staff",
    });
  };

  return (
    <div className="p-6 max-w-2xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">User Onboarding Test</h1>
      
      <div className="space-y-4 mb-6">
        <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-2">Current Test User:</h2>
          <pre className="text-sm bg-white dark:bg-gray-900 p-2 rounded">
            {JSON.stringify(testUser, null, 2)}
          </pre>
        </div>

        <div className="flex flex-wrap gap-2">
          <button
            onClick={resetUser}
            className="btn-secondary"
          >
            Reset User (Empty Profile)
          </button>
          <button
            onClick={setPartialUser}
            className="btn-secondary"
          >
            Set Partial Profile
          </button>
          <button
            onClick={setCompleteUser}
            className="btn-secondary"
          >
            Set Complete Profile
          </button>
        </div>

        <button
          onClick={() => setShowModal(true)}
          className="btn-primary"
        >
          Show Onboarding Modal
        </button>
      </div>

      <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
        <h3 className="font-semibold mb-2">Test Instructions:</h3>
        <ol className="list-decimal list-inside space-y-1 text-sm">
          <li>Click "Reset User" to simulate a new user with empty profile</li>
          <li>Click "Show Onboarding Modal" to test the modal</li>
          <li>Fill out the form and test validation</li>
          <li>Test both steps of the onboarding process</li>
          <li>Verify form submission and completion</li>
        </ol>
      </div>

      {showModal && (
        <UserOnboardingModal
          user={testUser}
          onComplete={handleTestComplete}
        />
      )}
    </div>
  );
}

import { useState, useEffect } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { toast } from "../utils/toast";
import { useTheme } from "../hooks/useTheme";
import CustomerImport from "./CustomerImport";
import { DynamicTable } from "./common/DynamicTable/DynamicTable";
import { AddressAutocomplete, AddressFormData } from "./common/AddressAutocomplete";

// Define the CustomerPreview component at the top level
// Export the CustomerPreview component so it can be used by other files
export function CustomerPreview({ customer, onEdit }: { customer: any; onEdit: () => void }) {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-charcoal">{customer.name}</h2>
        <button
          onClick={onEdit}
          className="btn-primary text-sm px-4 py-2"
        >
          Edit
        </button>
      </div>
      
      <div className="space-y-4">
        <div>
          <h3 className="text-sm font-medium text-gray-600 mb-1">Email</h3>
          <p className="text-charcoal">{customer.email}</p>
        </div>
        
        <div>
          <h3 className="text-sm font-medium text-gray-600 mb-1">Phone</h3>
          <p className="text-charcoal">{customer.phone || "N/A"}</p>
        </div>
        
        <div>
          <h3 className="text-sm font-medium text-gray-600 mb-1">Address</h3>
          <p className="text-charcoal whitespace-pre-line">
            {customer.address || "N/A"}
            {customer.city ? `, ${customer.city}` : ""}
            {customer.state ? `, ${customer.state}` : ""}
            {customer.zipCode ? ` ${customer.zipCode}` : ""}
          </p>
        </div>
        
        {customer.notes && (
          <div>
            <h3 className="text-sm font-medium text-gray-600 mb-1">Notes</h3>
            <p className="text-charcoal whitespace-pre-line">{customer.notes}</p>
          </div>
        )}
      </div>
    </div>
  );
}

// Customer form component for create/edit operations
function CustomerForm({ customer, currentUserId, onClose, onSuccess }: {
  customer: any;
  currentUserId: string;
  onClose: () => void;
  onSuccess: () => void;
}) {
  const createCustomer = useMutation(api.customers.create);
  const updateCustomer = useMutation(api.customers.update);
  const { isDark } = useTheme();
  const [formData, setFormData] = useState({
    name: customer?.name || "",
    email: customer?.email || "",
    phone: customer?.phone || "",
    address: customer?.address || "",
    city: customer?.city || "",
    state: customer?.state || "",
    zipCode: customer?.zipCode || "",
    notes: customer?.notes || "",
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Basic validation
    if (!formData.name.trim() || !formData.email.trim()) {
      toast.error("Please fill in all required fields");
      return;
    }
    
    try {
      const submitData = {
        name: formData.name,
        email: formData.email,
        phone: formData.phone,
        address: formData.address,
        city: formData.city,
        state: formData.state,
        zipCode: formData.zipCode,
        notes: formData.notes,
      };

      if (customer) {
        await updateCustomer({ id: customer._id, ...submitData });
        toast.success("Customer updated successfully");
      } else {
        await createCustomer(submitData);
        toast.success("Customer created successfully");
      }
      onSuccess();
    } catch (error) {
      toast.error("Failed to save customer");
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className={`${isDark ? 'bg-gray-800 text-white' : 'bg-white text-gray-900'} rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto`}>
        <h2 className="text-xl font-semibold mb-4">
          {customer ? "Edit Customer" : "Add Customer"}
        </h2>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Name *
            </label>
            <input
              type="text"
              required
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Email *
            </label>
            <input
              type="email"
              required
              value={formData.email}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Phone
            </label>
            <input
              type="tel"
              value={formData.phone}
              onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Address Autocomplete */}
          <AddressAutocomplete
            value={formData.address}
            onChange={(address) => setFormData({ ...formData, address })}
            onFormDataChange={(addressData: Partial<AddressFormData>) => {
              setFormData(prev => ({
                ...prev,
                address: addressData.address || prev.address,
                city: addressData.city || prev.city,
                state: addressData.state || prev.state,
                zipCode: addressData.zipCode || prev.zipCode,
              }));
            }}
            label="Address"
            placeholder="Enter street address..."
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />

          {/* City, State, ZIP fields - now auto-populated but still editable */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              City
            </label>
            <input
              type="text"
              value={formData.city}
              onChange={(e) => setFormData({ ...formData, city: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="City (auto-filled from address)"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                State
              </label>
              <input
                type="text"
                value={formData.state}
                onChange={(e) => setFormData({ ...formData, state: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="State (auto-filled)"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                ZIP Code
              </label>
              <input
                type="text"
                value={formData.zipCode}
                onChange={(e) => setFormData({ ...formData, zipCode: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="ZIP (auto-filled)"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Notes
            </label>
            <textarea
              value={formData.notes}
              onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div className="flex gap-3 pt-4">
            <button
              type="submit"
              className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
            >
              {customer ? "Update" : "Create"}
            </button>
            <button
              type="button"
              onClick={onClose}
              className={`flex-1 ${isDark ? 'bg-gray-600 text-white hover:bg-gray-700' : 'bg-gray-300 text-gray-700 hover:bg-gray-400'} py-2 px-4 rounded-md transition-colors`}
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

// Customer management component with CRUD operations
export function Customers({ selectedItemId }: { selectedItemId?: string | null }) {
  const customers = useQuery(api.customers.list);
  const loggedInUser = useQuery(api.auth.loggedInUser);
  const deleteCustomer = useMutation(api.customers.remove);
  const [selectedCustomer, setSelectedCustomer] = useState<any>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [showImport, setShowImport] = useState(false);

  // Auto-select customer when selectedItemId is provided from search navigation
  useEffect(() => {
    if (selectedItemId && customers) {
      const customerToSelect = customers.find(customer => customer._id === selectedItemId);
      if (customerToSelect) {
        setSelectedCustomer(customerToSelect);
        setIsEditing(false); // Show detail view, not edit form
      }
    }
  }, [selectedItemId, customers]);

  // Handle row click in the table
  const handleRowClick = (customer: any) => {
    setSelectedCustomer(customer);
  };

  const handleEdit = (customer: any) => {
    setSelectedCustomer(customer);
    setIsEditing(true);
  };

  const handleAdd = () => {
    setSelectedCustomer(null);
    setIsEditing(true);
  };

  // Function to handle customer deletion
  const handleRowDeletion = async (customer: any) => {
    try {
      await deleteCustomer({ id: customer._id });
      toast.success(`${customer.name} has been deleted`);
      // If the deleted customer was selected, clear the selection
      if (selectedCustomer && selectedCustomer._id === customer._id) {
        setSelectedCustomer(null);
        setIsEditing(false);
      }
    } catch (error) {
      toast.error("Failed to delete customer");
      console.error("Delete error:", error);
    }
  };

  const toggleImport = () => {
    setShowImport(!showImport);
  };

  // Define table columns - include address with defaultVisible set to true
  const columns = [
    {
      id: "name",
      header: "Name",
      accessor: (row: any) => (
        <div className="text-sm font-medium text-charcoal">{row.name}</div>
      ),
      sortable: true,
      filterable: true,
      getFilterValue: (row: any) => row.name,
      defaultVisible: true
    },
    {
      id: "phone",
      header: "Phone",
      accessor: (row: any) => (
        <div className="text-sm text-charcoal">{row.phone || "N/A"}</div>
      ),
      sortable: true,
      filterable: true,
      getFilterValue: (row: any) => row.phone,
      defaultVisible: true
    },
    {
      id: "email",
      header: "Email",
      accessor: (row: any) => (
        <div className="text-sm text-charcoal">{row.email}</div>
      ),
      sortable: true,
      filterable: true,
      getFilterValue: (row: any) => row.email,
      defaultVisible: true
    },
    {
      id: "address",
      header: "Address",
      accessor: (row: any) => {
        // Format the address to include city, state, zip if available
        let formattedAddress = row.address || "N/A";
        if (row.city) formattedAddress += `, ${row.city}`;
        if (row.state) formattedAddress += `, ${row.state}`;
        if (row.zipCode) formattedAddress += ` ${row.zipCode}`;

        return <div className="text-sm text-charcoal">{formattedAddress}</div>;
      },
      sortable: true,
      filterable: true,
      getFilterValue: (row: any) => row.address,
      defaultVisible: true // This is key to make it visible by default
    }
  ];

  if (!customers || !loggedInUser) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-[1fr_400px] gap-6 h-full">
      {/* Left Column - Customers List */}
      <div className="space-y-4">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold text-charcoal">Customers</h1>
            <p className="text-gray-600 text-sm md:text-base">Manage your HVAC customers</p>
          </div>
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-4">
            <button
              onClick={handleAdd}
              className="btn-primary w-full sm:w-auto touch-manipulation"
            >
              <span className="mr-2">➕</span>
              Add Customer
            </button>
            <button
              onClick={toggleImport}
              className="btn-secondary w-full sm:w-auto touch-manipulation"
            >
              <span className="mr-2">📥</span>
              Import CSV
            </button>
          </div>
        </div>

        {/* CSV Import Section */}
        {showImport && <CustomerImport />}

        {/* Dynamic Customers Table - Used for both desktop and mobile */}
        <DynamicTable
          data={customers}
          columns={columns}
          rowKey="_id"
          tableId="customers-table"
          searchable
          filterable
          reorderable
          pageSize={10}
          onRowClick={handleRowClick}
          selectedId={selectedCustomer?._id}
          onDelete={handleRowDeletion}
          showDeleteButton={true}
          deleteConfirmMessage="Are you sure you want to delete this customer?"
        />
      </div>

      {/* Right Column - Preview/Edit Panel */}
      <div className="card p-6 h-full">
        {isEditing ? (
          <CustomerForm
            customer={selectedCustomer}
            currentUserId={loggedInUser._id}
            onClose={() => {
              setSelectedCustomer(null);
              setIsEditing(false);
            }}
            onSuccess={() => {
              setSelectedCustomer(null);
              setIsEditing(false);
            }}
          />
        ) : selectedCustomer ? (
          <CustomerPreview customer={selectedCustomer} onEdit={() => setIsEditing(true)} />
        ) : (
          <div className="flex flex-col items-center justify-center h-full text-center">
            <h3 className="text-xl font-semibold text-charcoal mb-2">
              Select a customer to view details
            </h3>
            <p className="text-gray-600">
              Click on a customer in the list to see more information or edit it
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
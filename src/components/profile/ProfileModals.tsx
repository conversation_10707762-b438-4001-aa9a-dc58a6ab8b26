import { useState } from "react";
import { useMutation } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { toast } from "sonner";

// Edit profile modal
export function EditProfileModal({ user, userProfile, onClose, onSuccess }: {
  user: any;
  userProfile: any;
  onClose: () => void;
  onSuccess: () => void;
}) {
  const updateProfile = useMutation(api.users.updateProfile);
  const [formData, setFormData] = useState({
    name: user.name || "",
    phone: userProfile.phone || "",
    bio: userProfile.bio || "",
    department: userProfile.department || "",
    role: userProfile.role || "",
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await updateProfile(formData);
      onSuccess();
    } catch (error) {
      toast.error("Failed to update profile");
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-4 md:p-6 w-full max-w-md max-h-[95vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4 md:mb-6">
          <h2 className="text-lg md:text-xl font-semibold text-charcoal dark:text-gray-100">Edit Profile</h2>
          <button
            onClick={onClose}
            className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 text-xl p-2 touch-manipulation"
          >
            ✕
          </button>
        </div>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="form-label">Full Name</label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="form-input"
            />
          </div>

          <div>
            <label className="form-label">Phone Number</label>
            <input
              type="tel"
              value={formData.phone}
              onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
              className="form-input"
            />
          </div>

          <div>
            <label className="form-label">Role</label>
            <select
              value={formData.role}
              onChange={(e) => setFormData({ ...formData, role: e.target.value })}
              className="form-input"
            >
              <option value="">Select Role</option>
              <option value="admin">Administrator</option>
              <option value="staff">Staff Member</option>
            </select>
          </div>

          <div>
            <label className="form-label">Department</label>
            <select
              value={formData.department}
              onChange={(e) => setFormData({ ...formData, department: e.target.value })}
              className="form-input"
            >
              <option value="">Select Department</option>
              <option value="installation">Installation</option>
              <option value="maintenance">Maintenance</option>
              <option value="sales">Sales</option>
              <option value="management">Management</option>
            </select>
          </div>

          <div>
            <label className="form-label">Bio</label>
            <textarea
              value={formData.bio}
              onChange={(e) => { setFormData({ ...formData, bio: e.target.value }); }}
              rows={3}
              className="form-input"
              placeholder="Tell us about yourself..."
            />
          </div>

          <div className="flex flex-col md:flex-row gap-3 pt-4">
            <button
              type="submit"
              className="btn-primary flex-1 touch-manipulation"
            >
              Save Changes
            </button>
            <button
              type="button"
              onClick={onClose}
              className="btn-secondary flex-1 touch-manipulation"
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

// Password change modal
export function PasswordChangeModal({ onClose }: { onClose: () => void }) {
  const [formData, setFormData] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (formData.newPassword !== formData.confirmPassword) {
      toast.error("New passwords don't match");
      return;
    }

    if (formData.newPassword.length < 8) {
      toast.error("Password must be at least 8 characters long");
      return;
    }

    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success("Password updated successfully");
      onClose();
    } catch (error) {
      toast.error("Failed to update password");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-4 md:p-6 w-full max-w-md">
        <div className="flex justify-between items-center mb-4 md:mb-6">
          <h2 className="text-lg md:text-xl font-semibold text-charcoal dark:text-gray-100">Change Password</h2>
          <button
            onClick={onClose}
            className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 text-xl p-2 touch-manipulation"
          >
            ✕
          </button>
        </div>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="form-label">Current Password</label>
            <input
              type="password"
              value={formData.currentPassword}
              onChange={(e) => setFormData({ ...formData, currentPassword: e.target.value })}
              className="form-input"
              required
            />
          </div>

          <div>
            <label className="form-label">New Password</label>
            <input
              type="password"
              value={formData.newPassword}
              onChange={(e) => setFormData({ ...formData, newPassword: e.target.value })}
              className="form-input"
              required
              minLength={8}
            />
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Must be at least 8 characters long
            </p>
          </div>

          <div>
            <label className="form-label">Confirm New Password</label>
            <input
              type="password"
              value={formData.confirmPassword}
              onChange={(e) => setFormData({ ...formData, confirmPassword: e.target.value })}
              className="form-input"
              required
            />
          </div>

          <div className="flex flex-col md:flex-row gap-3 pt-4">
            <button
              type="submit"
              disabled={isLoading}
              className="btn-primary flex-1 touch-manipulation disabled:opacity-50"
            >
              {isLoading ? "Updating..." : "Update Password"}
            </button>
            <button
              type="button"
              onClick={onClose}
              className="btn-secondary flex-1 touch-manipulation"
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

// Two-factor authentication modal
export function TwoFactorModal({ enabled, onClose, onToggle }: {
  enabled: boolean;
  onClose: () => void;
  onToggle: (enabled: boolean) => void;
}) {
  const [step, setStep] = useState(1);
  const [qrCode, setQrCode] = useState("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iI2Y5ZmFmYiIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBkb21pbmFudC1iYXNlbGluZT0ibWlkZGxlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmb250LWZhbWlseT0ibW9ub3NwYWNlIiBmb250LXNpemU9IjE0cHgiIGZpbGw9IiM2YjcyODAiPlFSIENvZGU8L3RleHQ+PC9zdmc+");
  const [verificationCode, setVerificationCode] = useState("");

  const handleToggle = () => {
    if (enabled) {
      // Disable 2FA
      onToggle(false);
      onClose();
    } else {
      // Enable 2FA - show setup process
      setStep(1);
    }
  };

  const handleVerify = () => {
    if (verificationCode.length === 6) {
      onToggle(true);
      onClose();
    } else {
      toast.error("Please enter a valid 6-digit code");
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-4 md:p-6 w-full max-w-md">
        <div className="flex justify-between items-center mb-4 md:mb-6">
          <h2 className="text-lg md:text-xl font-semibold text-charcoal dark:text-gray-100">
            {enabled ? "Disable" : "Enable"} Two-Factor Authentication
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 text-xl p-2 touch-manipulation"
          >
            ✕
          </button>
        </div>
        
        {enabled ? (
          <div className="space-y-4">
            <p className="text-gray-600 dark:text-gray-400">
              Are you sure you want to disable two-factor authentication? This will make your account less secure.
            </p>
            <div className="flex flex-col md:flex-row gap-3">
              <button
                onClick={handleToggle}
                className="btn-danger flex-1 touch-manipulation"
              >
                Disable 2FA
              </button>
              <button
                onClick={onClose}
                className="btn-secondary flex-1 touch-manipulation"
              >
                Cancel
              </button>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {step === 1 && (
              <>
                <div className="text-center">
                  <img src={qrCode} alt="QR Code" className="mx-auto mb-4 rounded-lg" />
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                    Scan this QR code with your authenticator app
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400 font-mono bg-gray-100 dark:bg-gray-700 p-2 rounded">
                    JBSWY3DPEHPK3PXP
                  </p>
                </div>
                <button
                  onClick={() => setStep(2)}
                  className="btn-primary w-full touch-manipulation"
                >
                  I've Added the Account
                </button>
              </>
            )}
            
            {step === 2 && (
              <>
                <div>
                  <label className="form-label">Enter Verification Code</label>
                  <input
                    type="text"
                    value={verificationCode}
                    onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                    className="form-input text-center text-lg tracking-widest"
                    placeholder="000000"
                    maxLength={6}
                  />
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Enter the 6-digit code from your authenticator app
                  </p>
                </div>
                <div className="flex flex-col md:flex-row gap-3">
                  <button
                    onClick={handleVerify}
                    disabled={verificationCode.length !== 6}
                    className="btn-primary flex-1 touch-manipulation disabled:opacity-50"
                  >
                    Verify & Enable
                  </button>
                  <button
                    onClick={() => setStep(1)}
                    className="btn-secondary flex-1 touch-manipulation"
                  >
                    Back
                  </button>
                </div>
              </>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
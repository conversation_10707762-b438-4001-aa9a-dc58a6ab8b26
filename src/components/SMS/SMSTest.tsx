import React, { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { toast } from "sonner";

export function SMSTest() {
  const [testPhone, setTestPhone] = useState("");
  const [testMessage, setTestMessage] = useState("Test message from HVAC CRM SMS system. If you receive this, SMS is working correctly!");
  const [isLoading, setIsLoading] = useState(false);

  const user = useQuery(api.auth.loggedInUser);
  const configStatus = useQuery(api.twilioConfig.getConfigStatus);
  const templates = useQuery(api.smsTemplates.list, { activeOnly: true });
  const initializeTemplates = useMutation(api.smsTemplates.initializeDefaults);

  const isAdmin = user?.role === "admin" || user?.role === "master";

  const handleInitializeTemplates = async () => {
    try {
      const created = await initializeTemplates();
      toast.success(`Initialized ${created.length} default templates`);
    } catch (error: any) {
      toast.error(`Failed to initialize templates: ${error.message}`);
    }
  };

  const handleTestSMS = async () => {
    if (!testPhone || !testMessage) {
      toast.error("Please enter both phone number and message");
      return;
    }

    setIsLoading(true);
    try {
      // For testing, we'll create a temporary customer record
      // In a real implementation, you'd select an existing customer
      toast.info("SMS test functionality requires a customer record. Please use the main SMS composer with an existing customer.");
    } catch (error: any) {
      toast.error(`Test failed: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  if (!isAdmin) {
    return (
      <div className="max-w-2xl mx-auto p-6">
        <div className="card p-6 text-center">
          <h2 className="text-xl font-semibold text-charcoal dark:text-gray-100 mb-4">
            SMS Test - Admin Only
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            SMS testing is only available to administrators.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto p-6 space-y-6">
      <div className="card p-6">
        <h2 className="text-xl font-semibold text-charcoal dark:text-gray-100 mb-6">
          SMS System Test
        </h2>

        {/* Configuration Status */}
        <div className="mb-6">
          <h3 className="text-lg font-medium text-charcoal dark:text-gray-100 mb-3">
            Configuration Status
          </h3>
          <div className="space-y-2">
            <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <span className="text-sm font-medium">SMS Service</span>
              <span className={`text-sm font-medium ${
                configStatus?.isConfigured ? 'text-green-600' : 'text-red-600'
              }`}>
                {configStatus?.isConfigured ? '✅ Configured' : '❌ Not Configured'}
              </span>
            </div>
            
            {configStatus?.isConfigured && (
              <>
                <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <span className="text-sm font-medium">Status</span>
                  <span className={`text-sm font-medium ${
                    configStatus.isActive ? 'text-green-600' : 'text-yellow-600'
                  }`}>
                    {configStatus.isActive ? '✅ Active' : '⚠️ Inactive'}
                  </span>
                </div>
                
                <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <span className="text-sm font-medium">Test Mode</span>
                  <span className={`text-sm font-medium ${
                    configStatus.testMode ? 'text-yellow-600' : 'text-green-600'
                  }`}>
                    {configStatus.testMode ? '⚠️ Enabled' : '✅ Disabled'}
                  </span>
                </div>
                
                {configStatus.phoneNumber && (
                  <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <span className="text-sm font-medium">Phone Number</span>
                    <span className="text-sm font-medium text-charcoal dark:text-gray-100">
                      {configStatus.phoneNumber}
                    </span>
                  </div>
                )}
              </>
            )}
          </div>
        </div>

        {/* Templates Status */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-3">
            <h3 className="text-lg font-medium text-charcoal dark:text-gray-100">
              SMS Templates
            </h3>
            <button
              onClick={handleInitializeTemplates}
              className="btn-secondary text-sm"
            >
              Initialize Defaults
            </button>
          </div>
          
          <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Active Templates</span>
              <span className="text-sm font-medium text-charcoal dark:text-gray-100">
                {templates?.length || 0} templates
              </span>
            </div>
            
            {templates && templates.length > 0 && (
              <div className="mt-2 space-y-1">
                {templates.slice(0, 3).map((template) => (
                  <div key={template._id} className="text-xs text-gray-600 dark:text-gray-400">
                    • {template.name} ({template.category})
                  </div>
                ))}
                {templates.length > 3 && (
                  <div className="text-xs text-gray-500">
                    ... and {templates.length - 3} more
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Test SMS Form */}
        <div className="mb-6">
          <h3 className="text-lg font-medium text-charcoal dark:text-gray-100 mb-3">
            Test SMS Sending
          </h3>
          
          <div className="space-y-4">
            <div>
              <label className="form-label">Test Phone Number</label>
              <input
                type="tel"
                value={testPhone}
                onChange={(e) => setTestPhone(e.target.value)}
                className="form-input"
                placeholder="+1234567890"
              />
              <p className="text-xs text-gray-500 mt-1">
                Enter a phone number in E.164 format (e.g., +1234567890)
              </p>
            </div>
            
            <div>
              <label className="form-label">Test Message</label>
              <textarea
                value={testMessage}
                onChange={(e) => setTestMessage(e.target.value)}
                className="form-input h-24"
                maxLength={1600}
              />
              <p className="text-xs text-gray-500 mt-1">
                {testMessage.length}/1600 characters
              </p>
            </div>
            
            <button
              onClick={handleTestSMS}
              disabled={isLoading || !configStatus?.isConfigured || !testPhone || !testMessage}
              className="btn-primary w-full"
            >
              {isLoading ? "Sending Test SMS..." : "Send Test SMS"}
            </button>
          </div>
        </div>

        {/* Instructions */}
        <div className="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
          <h4 className="font-medium text-blue-800 dark:text-blue-300 mb-2">
            Testing Instructions
          </h4>
          <ol className="text-sm text-blue-700 dark:text-blue-400 space-y-1 list-decimal list-inside">
            <li>Ensure SMS service is configured and active</li>
            <li>Initialize default templates if none exist</li>
            <li>For full testing, use the main SMS composer with existing customers</li>
            <li>Check SMS history to verify delivery status</li>
            <li>Monitor Twilio console for detailed delivery information</li>
          </ol>
        </div>

        {/* Troubleshooting */}
        {!configStatus?.isConfigured && (
          <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <h4 className="font-medium text-red-800 dark:text-red-300 mb-2">
              SMS Not Configured
            </h4>
            <p className="text-sm text-red-700 dark:text-red-400 mb-2">
              SMS service is not configured. Please set up Twilio credentials first.
            </p>
            <button
              onClick={() => window.location.hash = '#sms-config'}
              className="text-sm text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 underline"
            >
              Go to SMS Configuration
            </button>
          </div>
        )}
      </div>
    </div>
  );
}

import React, { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { toast } from "sonner";

interface SMSTemplatesProps {
  onSelectTemplate?: (template: any) => void;
  selectionMode?: boolean;
}

export function SMSTemplates({ onSelectTemplate, selectionMode = false }: SMSTemplatesProps) {
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<any>(null);

  const templates = useQuery(api.smsTemplates.list, {
    category: selectedCategory === "all" ? undefined : selectedCategory,
  });
  const availableVariables = useQuery(api.smsTemplates.getAvailableVariables);

  const categories = [
    { value: "all", label: "All Templates" },
    { value: "appointment", label: "Appointments" },
    { value: "service", label: "Service" },
    { value: "invoice", label: "Invoices" },
    { value: "general", label: "General" },
  ];

  const handleTemplateSelect = (template: any) => {
    if (onSelectTemplate) {
      onSelectTemplate(template);
    }
  };

  const handleEdit = (template: any) => {
    setEditingTemplate(template);
    setShowCreateModal(true);
  };

  const handleCreate = () => {
    setEditingTemplate(null);
    setShowCreateModal(true);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold text-charcoal dark:text-gray-100">
            SMS Templates
          </h2>
          <p className="text-gray-600 dark:text-gray-400 text-sm">
            {selectionMode ? "Select a template to use" : "Manage your SMS templates"}
          </p>
        </div>
        {!selectionMode && (
          <button onClick={handleCreate} className="btn-primary">
            Create Template
          </button>
        )}
      </div>

      {/* Category Filter */}
      <div className="flex flex-wrap gap-2">
        {categories.map((category) => (
          <button
            key={category.value}
            onClick={() => setSelectedCategory(category.value)}
            className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
              selectedCategory === category.value
                ? "bg-primary text-white"
                : "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
            }`}
          >
            {category.label}
          </button>
        ))}
      </div>

      {/* Templates Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {templates?.map((template) => (
          <TemplateCard
            key={template._id}
            template={template}
            onSelect={selectionMode ? () => handleTemplateSelect(template) : undefined}
            onEdit={!selectionMode ? () => handleEdit(template) : undefined}
            selectionMode={selectionMode}
          />
        ))}
      </div>

      {templates?.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 text-6xl mb-4">📱</div>
          <h3 className="text-lg font-medium text-gray-600 dark:text-gray-400 mb-2">
            No templates found
          </h3>
          <p className="text-gray-500 dark:text-gray-500 mb-4">
            {selectedCategory === "all" 
              ? "Create your first SMS template to get started"
              : `No templates found in the ${categories.find(c => c.value === selectedCategory)?.label} category`
            }
          </p>
          {!selectionMode && (
            <button onClick={handleCreate} className="btn-primary">
              Create Template
            </button>
          )}
        </div>
      )}

      {/* Create/Edit Modal */}
      {showCreateModal && (
        <TemplateModal
          template={editingTemplate}
          availableVariables={availableVariables || []}
          onClose={() => {
            setShowCreateModal(false);
            setEditingTemplate(null);
          }}
        />
      )}
    </div>
  );
}

interface TemplateCardProps {
  template: any;
  onSelect?: () => void;
  onEdit?: () => void;
  selectionMode: boolean;
}

function TemplateCard({ template, onSelect, onEdit, selectionMode }: TemplateCardProps) {
  const toggleActive = useMutation(api.smsTemplates.toggleActive);
  const deleteTemplate = useMutation(api.smsTemplates.remove);

  const handleToggleActive = async (e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await toggleActive({
        id: template._id,
        isActive: !template.isActive,
      });
      toast.success(`Template ${template.isActive ? 'deactivated' : 'activated'}`);
    } catch (error: any) {
      toast.error(`Failed to update template: ${error.message}`);
    }
  };

  const handleDelete = async (e: React.MouseEvent) => {
    e.stopPropagation();
    if (confirm(`Are you sure you want to delete the template "${template.name}"?`)) {
      try {
        await deleteTemplate({ id: template._id });
        toast.success("Template deleted successfully");
      } catch (error: any) {
        toast.error(`Failed to delete template: ${error.message}`);
      }
    }
  };

  const categoryColors = {
    appointment: "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300",
    service: "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300",
    invoice: "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300",
    general: "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300",
  };

  return (
    <div
      className={`card p-4 cursor-pointer transition-all hover:shadow-md ${
        selectionMode ? "hover:border-primary" : ""
      } ${!template.isActive ? "opacity-60" : ""}`}
      onClick={onSelect}
    >
      <div className="flex justify-between items-start mb-3">
        <div className="flex-1">
          <h3 className="font-medium text-charcoal dark:text-gray-100 mb-1">
            {template.name}
          </h3>
          <span
            className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${
              categoryColors[template.category as keyof typeof categoryColors] ||
              categoryColors.general
            }`}
          >
            {template.category}
          </span>
        </div>
        {!selectionMode && (
          <div className="flex gap-1">
            <button
              onClick={handleToggleActive}
              className={`p-1 rounded text-xs ${
                template.isActive
                  ? "text-green-600 hover:bg-green-100 dark:hover:bg-green-900/30"
                  : "text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800"
              }`}
              title={template.isActive ? "Deactivate" : "Activate"}
            >
              {template.isActive ? "●" : "○"}
            </button>
            <button
              onClick={onEdit}
              className="p-1 rounded text-xs text-blue-600 hover:bg-blue-100 dark:hover:bg-blue-900/30"
              title="Edit"
            >
              ✏️
            </button>
            <button
              onClick={handleDelete}
              className="p-1 rounded text-xs text-red-600 hover:bg-red-100 dark:hover:bg-red-900/30"
              title="Delete"
            >
              🗑️
            </button>
          </div>
        )}
      </div>

      <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-3">
        {template.content}
      </p>

      {template.description && (
        <p className="text-xs text-gray-500 dark:text-gray-500 mb-2">
          {template.description}
        </p>
      )}

      {template.variables && template.variables.length > 0 && (
        <div className="flex flex-wrap gap-1">
          {template.variables.slice(0, 3).map((variable: string) => (
            <span
              key={variable}
              className="inline-block px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 text-xs rounded"
            >
              {`{${variable}}`}
            </span>
          ))}
          {template.variables.length > 3 && (
            <span className="text-xs text-gray-500">
              +{template.variables.length - 3} more
            </span>
          )}
        </div>
      )}
    </div>
  );
}

interface TemplateModalProps {
  template?: any;
  availableVariables: string[];
  onClose: () => void;
}

function TemplateModal({ template, availableVariables, onClose }: TemplateModalProps) {
  const [formData, setFormData] = useState({
    name: template?.name || "",
    content: template?.content || "",
    category: template?.category || "general",
    description: template?.description || "",
  });
  const [isLoading, setIsLoading] = useState(false);

  const createTemplate = useMutation(api.smsTemplates.create);
  const updateTemplate = useMutation(api.smsTemplates.update);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Extract variables from content
      const variableRegex = /\{([^}]+)\}/g;
      const variables: string[] = [];
      let match;
      while ((match = variableRegex.exec(formData.content)) !== null) {
        if (!variables.includes(match[1])) {
          variables.push(match[1]);
        }
      }

      if (template) {
        await updateTemplate({
          id: template._id,
          ...formData,
          variables,
        });
        toast.success("Template updated successfully");
      } else {
        await createTemplate({
          ...formData,
          variables,
        });
        toast.success("Template created successfully");
      }
      onClose();
    } catch (error: any) {
      toast.error(`Failed to save template: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const insertVariable = (variable: string) => {
    const textarea = document.getElementById("content") as HTMLTextAreaElement;
    if (textarea) {
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const text = formData.content;
      const before = text.substring(0, start);
      const after = text.substring(end);
      const newContent = before + `{${variable}}` + after;
      
      setFormData(prev => ({ ...prev, content: newContent }));
      
      // Set cursor position after the inserted variable
      setTimeout(() => {
        textarea.focus();
        textarea.setSelectionRange(start + variable.length + 2, start + variable.length + 2);
      }, 0);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold text-charcoal dark:text-gray-100">
            {template ? "Edit Template" : "Create Template"}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
          >
            ✕
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="form-label">Template Name *</label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              className="form-input"
              required
            />
          </div>

          <div>
            <label className="form-label">Category *</label>
            <select
              value={formData.category}
              onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
              className="form-input"
              required
            >
              <option value="appointment">Appointments</option>
              <option value="service">Service</option>
              <option value="invoice">Invoices</option>
              <option value="general">General</option>
            </select>
          </div>

          <div>
            <label className="form-label">Description</label>
            <input
              type="text"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              className="form-input"
              placeholder="Brief description of when to use this template"
            />
          </div>

          <div>
            <label className="form-label">Message Content *</label>
            <textarea
              id="content"
              value={formData.content}
              onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
              className="form-input h-32"
              placeholder="Enter your SMS message content..."
              required
            />
            <p className="text-xs text-gray-500 mt-1">
              {formData.content.length}/1600 characters
            </p>
          </div>

          <div>
            <label className="form-label">Available Variables</label>
            <div className="flex flex-wrap gap-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              {availableVariables.map((variable) => (
                <button
                  key={variable}
                  type="button"
                  onClick={() => insertVariable(variable)}
                  className="px-2 py-1 bg-white dark:bg-gray-600 text-gray-700 dark:text-gray-300 text-xs rounded border hover:bg-gray-100 dark:hover:bg-gray-500"
                >
                  {`{${variable}}`}
                </button>
              ))}
            </div>
            <p className="text-xs text-gray-500 mt-1">
              Click on a variable to insert it into your message
            </p>
          </div>

          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="btn-secondary flex-1"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="btn-primary flex-1"
            >
              {isLoading ? "Saving..." : template ? "Update Template" : "Create Template"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

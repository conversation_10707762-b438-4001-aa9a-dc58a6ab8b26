import React, { useState, useEffect } from "react";
import { useQuery, useAction } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { toast } from "sonner";
import { SMSTemplates } from "./SMSTemplates";

interface SMSComposerProps {
  customerId?: string;
  jobId?: string;
  invoiceId?: string;
  onClose?: () => void;
  onSent?: () => void;
}

export function SMSComposer({ customerId, jobId, invoiceId, onClose, onSent }: SMSComposerProps) {
  const [message, setMessage] = useState("");
  const [selectedTemplate, setSelectedTemplate] = useState<any>(null);
  const [showTemplates, setShowTemplates] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [templateVariables, setTemplateVariables] = useState<Record<string, string>>({});

  const customer = useQuery(
    api.customers.get,
    customerId ? { id: customerId } : "skip"
  );
  const job = useQuery(
    api.jobs.getById,
    jobId ? { id: jobId } : "skip"
  );
  const invoice = useQuery(
    api.invoices.get,
    invoiceId ? { id: invoiceId } : "skip"
  );
  const settings = useQuery(api.settings.get);

  const sendSMS = useAction(api.sms.sendToCustomer);
  const sendWithTemplate = useAction(api.sms.sendWithTemplate);

  useEffect(() => {
    if (customer && settings) {
      setTemplateVariables({
        customerName: customer.name || "",
        companyName: customer.company || "",
        customerPhone: customer.phone || "",
        customerEmail: customer.email || "",
        customerAddress: `${customer.address}, ${customer.city}, ${customer.state} ${customer.zipCode}`,
        companyPhone: settings.contactPhone || "",
        companyEmail: settings.contactEmail || "",
        // Add job-specific variables if available
        ...(job && {
          jobTitle: job.title || "",
          jobDescription: job.description || "",
          appointmentDate: job.scheduledDate ? new Date(job.scheduledDate).toLocaleDateString() : "",
          appointmentTime: job.scheduledDate ? new Date(job.scheduledDate).toLocaleTimeString() : "",
        }),
        // Add invoice-specific variables if available
        ...(invoice && {
          invoiceNumber: invoice.invoiceNumber || "",
          invoiceAmount: invoice.total ? `$${invoice.total.toFixed(2)}` : "",
          dueDate: invoice.dueDate ? new Date(invoice.dueDate).toLocaleDateString() : "",
        }),
      });
    }
  }, [customer, job, invoice, settings]);

  const handleTemplateSelect = (template: any) => {
    setSelectedTemplate(template);
    
    // Replace variables in template content
    let content = template.content;
    Object.entries(templateVariables).forEach(([key, value]) => {
      const placeholder = `{${key}}`;
      content = content.replace(new RegExp(placeholder, 'g'), value || '');
    });
    
    setMessage(content);
    setShowTemplates(false);
  };

  const handleSend = async () => {
    if (!customerId) {
      toast.error("No customer selected");
      return;
    }

    if (!message.trim()) {
      toast.error("Please enter a message");
      return;
    }

    if (!customer?.phone) {
      toast.error("Customer phone number is required");
      return;
    }

    setIsLoading(true);
    try {
      if (selectedTemplate) {
        await sendWithTemplate({
          customerId,
          templateId: selectedTemplate._id,
          variables: templateVariables,
          jobId,
          invoiceId,
        });
      } else {
        await sendSMS({
          customerId,
          message: message.trim(),
          jobId,
          invoiceId,
        });
      }

      toast.success("SMS sent successfully!");
      setMessage("");
      setSelectedTemplate(null);
      if (onSent) onSent();
      if (onClose) onClose();
    } catch (error: any) {
      toast.error(`Failed to send SMS: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClear = () => {
    setMessage("");
    setSelectedTemplate(null);
  };

  if (showTemplates) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold text-charcoal dark:text-gray-100">
            Select SMS Template
          </h3>
          <button
            onClick={() => setShowTemplates(false)}
            className="btn-secondary"
          >
            Back to Composer
          </button>
        </div>
        <SMSTemplates
          onSelectTemplate={handleTemplateSelect}
          selectionMode={true}
        />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-semibold text-charcoal dark:text-gray-100">
            Send SMS
          </h3>
          {customer && (
            <p className="text-sm text-gray-600 dark:text-gray-400">
              To: {customer.name} ({customer.phone})
            </p>
          )}
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
          >
            ✕
          </button>
        )}
      </div>

      {/* Customer Info */}
      {customer && (
        <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <div className="flex items-center gap-4">
            <div className="w-10 h-10 bg-primary text-white rounded-full flex items-center justify-center font-semibold">
              {customer.name.charAt(0).toUpperCase()}
            </div>
            <div>
              <h4 className="font-medium text-charcoal dark:text-gray-100">
                {customer.name}
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {customer.company && `${customer.company} • `}
                {customer.phone}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Template Selection */}
      <div className="flex gap-2">
        <button
          onClick={() => setShowTemplates(true)}
          className="btn-secondary flex-1"
        >
          📱 Use Template
        </button>
        {selectedTemplate && (
          <button
            onClick={handleClear}
            className="btn-secondary"
            title="Clear template and start fresh"
          >
            Clear
          </button>
        )}
      </div>

      {/* Selected Template Info */}
      {selectedTemplate && (
        <div className="p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
          <div className="flex justify-between items-center">
            <div>
              <span className="text-sm font-medium text-blue-800 dark:text-blue-300">
                Using template: {selectedTemplate.name}
              </span>
              <p className="text-xs text-blue-600 dark:text-blue-400">
                {selectedTemplate.description}
              </p>
            </div>
            <button
              onClick={() => setSelectedTemplate(null)}
              className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
            >
              ✕
            </button>
          </div>
        </div>
      )}

      {/* Message Composer */}
      <div>
        <label className="form-label">Message</label>
        <textarea
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          className="form-input h-32 resize-none"
          placeholder="Type your SMS message here..."
          maxLength={1600}
        />
        <div className="flex justify-between items-center mt-2">
          <p className="text-xs text-gray-500">
            {message.length}/1600 characters
          </p>
          <p className="text-xs text-gray-500">
            ~{Math.ceil(message.length / 160)} SMS segment{Math.ceil(message.length / 160) !== 1 ? 's' : ''}
          </p>
        </div>
      </div>

      {/* Context Info */}
      {(job || invoice) && (
        <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <h5 className="text-sm font-medium text-charcoal dark:text-gray-100 mb-2">
            Related Information
          </h5>
          {job && (
            <div className="text-sm text-gray-600 dark:text-gray-400">
              <strong>Job:</strong> {job.title}
              {job.scheduledDate && (
                <span className="ml-2">
                  • Scheduled: {new Date(job.scheduledDate).toLocaleDateString()}
                </span>
              )}
            </div>
          )}
          {invoice && (
            <div className="text-sm text-gray-600 dark:text-gray-400">
              <strong>Invoice:</strong> #{invoice.invoiceNumber}
              <span className="ml-2">
                • Amount: ${invoice.total?.toFixed(2)}
              </span>
              {invoice.dueDate && (
                <span className="ml-2">
                  • Due: {new Date(invoice.dueDate).toLocaleDateString()}
                </span>
              )}
            </div>
          )}
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex gap-3">
        {onClose && (
          <button
            onClick={onClose}
            className="btn-secondary flex-1"
          >
            Cancel
          </button>
        )}
        <button
          onClick={handleSend}
          disabled={isLoading || !message.trim() || !customer?.phone}
          className="btn-primary flex-1"
        >
          {isLoading ? "Sending..." : "Send SMS"}
        </button>
      </div>

      {/* Warning for missing phone */}
      {customer && !customer.phone && (
        <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
          <p className="text-sm text-yellow-800 dark:text-yellow-300">
            ⚠️ This customer doesn't have a phone number. Please add one before sending SMS.
          </p>
        </div>
      )}
    </div>
  );
}

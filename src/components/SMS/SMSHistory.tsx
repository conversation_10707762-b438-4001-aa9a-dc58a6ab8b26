import React, { useState } from "react";
import { useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";

interface SMSHistoryProps {
  customerId?: string;
  showAllHistory?: boolean;
}

export function SMSHistory({ customerId, showAllHistory = false }: SMSHistoryProps) {
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [searchQuery, setSearchQuery] = useState("");

  const customerHistory = useQuery(
    api.sms.getCustomerHistory,
    customerId ? { customerId } : "skip"
  );

  const myHistory = useQuery(
    api.sms.getMyHistory,
    !customerId && !showAllHistory ? { limit: 100 } : "skip"
  );

  const allHistory = useQuery(
    api.sms.getAllHistory,
    showAllHistory ? { limit: 200, status: statusFilter === "all" ? undefined : statusFilter } : "skip"
  );

  const recentActivity = useQuery(api.sms.getRecentActivity, { limit: 50 });

  const history = customerId ? customerHistory : showAllHistory ? allHistory : myHistory;

  const filteredHistory = history?.filter(record => {
    const matchesStatus = statusFilter === "all" || record.status === statusFilter;
    const matchesSearch = !searchQuery || 
      record.message.toLowerCase().includes(searchQuery.toLowerCase()) ||
      record.phoneNumber.includes(searchQuery);
    return matchesStatus && matchesSearch;
  });

  const statusOptions = [
    { value: "all", label: "All Status" },
    { value: "sent", label: "Sent" },
    { value: "delivered", label: "Delivered" },
    { value: "failed", label: "Failed" },
    { value: "pending", label: "Pending" },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "delivered":
        return "text-green-600 bg-green-100 dark:bg-green-900/30 dark:text-green-300";
      case "sent":
        return "text-blue-600 bg-blue-100 dark:bg-blue-900/30 dark:text-blue-300";
      case "failed":
        return "text-red-600 bg-red-100 dark:bg-red-900/30 dark:text-red-300";
      case "pending":
        return "text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30 dark:text-yellow-300";
      default:
        return "text-gray-600 bg-gray-100 dark:bg-gray-900/30 dark:text-gray-300";
    }
  };

  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 24 * 7) {
      return date.toLocaleDateString([], { weekday: 'short', hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' });
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h3 className="text-lg font-semibold text-charcoal dark:text-gray-100 mb-2">
          SMS History
          {customerId && " for Customer"}
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          {customerId 
            ? "All SMS messages sent to this customer"
            : showAllHistory 
              ? "All SMS messages sent by your team"
              : "SMS messages you've sent"
          }
        </p>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search messages or phone numbers..."
            className="form-input"
          />
        </div>
        <div className="sm:w-48">
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="form-input"
          >
            {statusOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* History List */}
      <div className="space-y-3">
        {filteredHistory?.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-400 text-6xl mb-4">📱</div>
            <h4 className="text-lg font-medium text-gray-600 dark:text-gray-400 mb-2">
              No SMS messages found
            </h4>
            <p className="text-gray-500 dark:text-gray-500">
              {searchQuery || statusFilter !== "all"
                ? "Try adjusting your filters"
                : "No SMS messages have been sent yet"
              }
            </p>
          </div>
        ) : (
          filteredHistory?.map((record) => (
            <SMSHistoryItem
              key={record._id}
              record={record}
              showCustomer={!customerId}
              formatDate={formatDate}
              getStatusColor={getStatusColor}
            />
          ))
        )}
      </div>

      {/* Load More */}
      {filteredHistory && filteredHistory.length >= 50 && (
        <div className="text-center">
          <button className="btn-secondary">
            Load More Messages
          </button>
        </div>
      )}
    </div>
  );
}

interface SMSHistoryItemProps {
  record: any;
  showCustomer: boolean;
  formatDate: (timestamp: number) => string;
  getStatusColor: (status: string) => string;
}

function SMSHistoryItem({ record, showCustomer, formatDate, getStatusColor }: SMSHistoryItemProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const customer = useQuery(api.customers.get, { id: record.customerId });
  // TODO: Add user query when available
  const sender = null;

  return (
    <div className="card p-4 hover:shadow-md transition-shadow">
      <div className="flex justify-between items-start mb-3">
        <div className="flex-1">
          {showCustomer && customer && (
            <div className="flex items-center gap-2 mb-2">
              <div className="w-6 h-6 bg-primary text-white rounded-full flex items-center justify-center text-xs font-semibold">
                {customer.name.charAt(0).toUpperCase()}
              </div>
              <span className="font-medium text-charcoal dark:text-gray-100">
                {customer.name}
              </span>
              {customer.company && (
                <span className="text-sm text-gray-500">
                  • {customer.company}
                </span>
              )}
            </div>
          )}
          <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
            <span>{record.phoneNumber}</span>
            <span>•</span>
            <span>{formatDate(record.sentAt)}</span>
            {sender && (
              <>
                <span>•</span>
                <span>by {sender.name}</span>
              </>
            )}
          </div>
        </div>
        <div className="flex items-center gap-2">
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(record.status)}`}>
            {record.status}
          </span>
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            {isExpanded ? "▼" : "▶"}
          </button>
        </div>
      </div>

      {/* Message Preview */}
      <div className="mb-3">
        <p className={`text-charcoal dark:text-gray-100 ${isExpanded ? '' : 'line-clamp-2'}`}>
          {record.message}
        </p>
      </div>

      {/* Expanded Details */}
      {isExpanded && (
        <div className="border-t border-gray-200 dark:border-gray-700 pt-3 space-y-2">
          {record.twilioSid && (
            <div className="text-sm">
              <span className="text-gray-500 dark:text-gray-400">Twilio SID:</span>
              <span className="ml-2 font-mono text-xs">{record.twilioSid}</span>
            </div>
          )}
          
          {record.deliveredAt && (
            <div className="text-sm">
              <span className="text-gray-500 dark:text-gray-400">Delivered:</span>
              <span className="ml-2">{formatDate(record.deliveredAt)}</span>
            </div>
          )}
          
          {record.errorMessage && (
            <div className="text-sm">
              <span className="text-red-500">Error:</span>
              <span className="ml-2 text-red-600 dark:text-red-400">{record.errorMessage}</span>
            </div>
          )}

          {(record.jobId || record.invoiceId) && (
            <div className="text-sm">
              <span className="text-gray-500 dark:text-gray-400">Related to:</span>
              {record.jobId && (
                <span className="ml-2 px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded text-xs">
                  Job
                </span>
              )}
              {record.invoiceId && (
                <span className="ml-2 px-2 py-1 bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300 rounded text-xs">
                  Invoice
                </span>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
}

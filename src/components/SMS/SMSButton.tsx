import React, { useState } from "react";
import { useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { SMSComposer } from "./SMSComposer";

interface SMSButtonProps {
  customerId: string;
  jobId?: string;
  invoiceId?: string;
  variant?: "button" | "icon" | "menu-item";
  size?: "sm" | "md" | "lg";
  className?: string;
  disabled?: boolean;
}

export function SMSButton({ 
  customerId, 
  jobId, 
  invoiceId, 
  variant = "button", 
  size = "md",
  className = "",
  disabled = false
}: SMSButtonProps) {
  const [showComposer, setShowComposer] = useState(false);
  
  const customer = useQuery(api.customers.get, { id: customerId });
  const configStatus = useQuery(api.twilioConfig.getConfigStatus);

  // Don't show SMS button if SMS is not configured or customer has no phone
  const canSendSMS = configStatus?.isConfigured && customer?.phone;

  if (!canSendSMS || disabled) {
    return null;
  }

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowComposer(true);
  };

  const renderButton = () => {
    const baseClasses = "transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2";
    
    switch (variant) {
      case "icon":
        return (
          <button
            onClick={handleClick}
            className={`${baseClasses} p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-full ${className}`}
            title="Send SMS"
          >
            📱
          </button>
        );
      
      case "menu-item":
        return (
          <button
            onClick={handleClick}
            className={`${baseClasses} w-full text-left px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-2 ${className}`}
          >
            <span>📱</span>
            Send SMS
          </button>
        );
      
      default: // button
        const sizeClasses = {
          sm: "px-2 py-1 text-xs",
          md: "px-3 py-2 text-sm",
          lg: "px-4 py-2 text-base"
        };
        
        return (
          <button
            onClick={handleClick}
            className={`${baseClasses} ${sizeClasses[size]} bg-blue-600 hover:bg-blue-700 text-white rounded-md font-medium ${className}`}
          >
            <span className="mr-1">📱</span>
            SMS
          </button>
        );
    }
  };

  return (
    <>
      {renderButton()}
      
      {showComposer && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <SMSComposer
              customerId={customerId}
              jobId={jobId}
              invoiceId={invoiceId}
              onClose={() => setShowComposer(false)}
              onSent={() => setShowComposer(false)}
            />
          </div>
        </div>
      )}
    </>
  );
}

// Quick SMS action component for dropdown menus
export function SMSAction({ 
  customerId, 
  jobId, 
  invoiceId, 
  onClose 
}: { 
  customerId: string; 
  jobId?: string; 
  invoiceId?: string; 
  onClose?: () => void;
}) {
  return (
    <SMSButton
      customerId={customerId}
      jobId={jobId}
      invoiceId={invoiceId}
      variant="menu-item"
      className="w-full"
    />
  );
}

// SMS history widget for customer/job/invoice detail views
export function SMSHistoryWidget({ 
  customerId, 
  jobId, 
  invoiceId,
  maxItems = 3 
}: { 
  customerId: string; 
  jobId?: string; 
  invoiceId?: string;
  maxItems?: number;
}) {
  const history = useQuery(api.sms.getCustomerHistory, { customerId });
  
  // Filter history by job or invoice if specified
  const filteredHistory = history?.filter(record => {
    if (jobId && record.jobId !== jobId) return false;
    if (invoiceId && record.invoiceId !== invoiceId) return false;
    return true;
  }).slice(0, maxItems);

  if (!filteredHistory || filteredHistory.length === 0) {
    return null;
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "delivered":
        return "text-green-600 bg-green-100 dark:bg-green-900/30 dark:text-green-300";
      case "sent":
        return "text-blue-600 bg-blue-100 dark:bg-blue-900/30 dark:text-blue-300";
      case "failed":
        return "text-red-600 bg-red-100 dark:bg-red-900/30 dark:text-red-300";
      default:
        return "text-gray-600 bg-gray-100 dark:bg-gray-900/30 dark:text-gray-300";
    }
  };

  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <h4 className="text-sm font-medium text-charcoal dark:text-gray-100">
          Recent SMS
        </h4>
        <SMSButton
          customerId={customerId}
          jobId={jobId}
          invoiceId={invoiceId}
          variant="icon"
          size="sm"
        />
      </div>
      
      <div className="space-y-2">
        {filteredHistory.map((record) => (
          <div
            key={record._id}
            className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg text-sm"
          >
            <div className="flex items-center justify-between mb-1">
              <span className="text-gray-600 dark:text-gray-400">
                {formatDate(record.sentAt)}
              </span>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(record.status)}`}>
                {record.status}
              </span>
            </div>
            <p className="text-charcoal dark:text-gray-100 line-clamp-2">
              {record.message}
            </p>
          </div>
        ))}
      </div>
      
      {history && history.length > maxItems && (
        <div className="text-center">
          <button className="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
            View all {history.length} messages
          </button>
        </div>
      )}
    </div>
  );
}

// SMS status indicator for lists
export function SMSStatusIndicator({ customerId }: { customerId: string }) {
  const history = useQuery(api.sms.getCustomerHistory, { customerId });
  const customer = useQuery(api.customers.get, { id: customerId });
  
  if (!customer?.phone) {
    return (
      <span 
        className="text-gray-400 text-xs" 
        title="No phone number"
      >
        📱❌
      </span>
    );
  }

  const recentSMS = history?.[0];
  if (!recentSMS) {
    return (
      <span 
        className="text-gray-400 text-xs" 
        title="No SMS sent"
      >
        📱
      </span>
    );
  }

  const statusIcons = {
    delivered: "📱✅",
    sent: "📱📤",
    failed: "📱❌",
    pending: "📱⏳",
  };

  const statusTitles = {
    delivered: "Last SMS delivered",
    sent: "Last SMS sent",
    failed: "Last SMS failed",
    pending: "Last SMS pending",
  };

  return (
    <span 
      className="text-xs" 
      title={`${statusTitles[recentSMS.status as keyof typeof statusTitles]} - ${new Date(recentSMS.sentAt).toLocaleDateString()}`}
    >
      {statusIcons[recentSMS.status as keyof typeof statusIcons] || "📱"}
    </span>
  );
}

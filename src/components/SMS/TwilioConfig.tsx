import React, { useState, useEffect } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { toast } from "sonner";

interface TwilioConfigProps {
  onClose?: () => void;
}

export function TwilioConfig({ onClose }: TwilioConfigProps) {
  const [formData, setFormData] = useState({
    accountSid: "",
    authToken: "",
    phoneNumber: "",
    testMode: false,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [isTesting, setIsTesting] = useState(false);

  const config = useQuery(api.twilioConfig.getConfig);
  const configStatus = useQuery(api.twilioConfig.getConfigStatus);
  const envStatus = useQuery(api.twilioConfig.getEnvConfigStatus);
  
  const saveConfig = useMutation(api.twilioConfig.saveConfig);
  const testConfig = useMutation(api.twilioConfig.testConfig);
  const migrateFromEnv = useMutation(api.twilioConfig.migrateFromEnv);

  useEffect(() => {
    if (config) {
      setFormData({
        accountSid: "",
        authToken: "",
        phoneNumber: config.phoneNumber || "",
        testMode: config.testMode || false,
      });
    }
  }, [config]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleTest = async () => {
    if (!formData.accountSid || !formData.authToken || !formData.phoneNumber) {
      toast.error("Please fill in all required fields before testing.");
      return;
    }

    setIsTesting(true);
    try {
      const result = await testConfig({
        accountSid: formData.accountSid,
        authToken: formData.authToken,
        phoneNumber: formData.phoneNumber,
      });

      if (result.success) {
        toast.success(`Configuration test successful! Account: ${result.accountName}`);
      } else {
        toast.error(result.error || "Configuration test failed.");
      }
    } catch (error: any) {
      toast.error(`Test failed: ${error.message}`);
    } finally {
      setIsTesting(false);
    }
  };

  const handleSave = async () => {
    if (!formData.accountSid || !formData.authToken || !formData.phoneNumber) {
      toast.error("Please fill in all required fields.");
      return;
    }

    setIsLoading(true);
    try {
      await saveConfig({
        accountSid: formData.accountSid,
        authToken: formData.authToken,
        phoneNumber: formData.phoneNumber,
        testMode: formData.testMode,
      });

      toast.success("Twilio configuration saved successfully!");
      if (onClose) onClose();
    } catch (error: any) {
      toast.error(`Failed to save configuration: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleMigrate = async () => {
    setIsLoading(true);
    try {
      await migrateFromEnv();
      toast.success("Configuration migrated from environment variables!");
    } catch (error: any) {
      toast.error(`Migration failed: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-6">
      <div className="card">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold text-charcoal dark:text-gray-100">
            Twilio SMS Configuration
          </h2>
          {onClose && (
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
            >
              ✕
            </button>
          )}
        </div>

        {/* Configuration Status */}
        <div className="mb-6 p-4 rounded-lg bg-gray-50 dark:bg-gray-800">
          <h3 className="font-medium text-charcoal dark:text-gray-100 mb-2">Status</h3>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span>SMS Service:</span>
              <span className={`font-medium ${configStatus?.isConfigured ? 'text-green-600' : 'text-red-600'}`}>
                {configStatus?.isConfigured ? 'Configured' : 'Not Configured'}
              </span>
            </div>
            {configStatus?.isConfigured && (
              <>
                <div className="flex justify-between">
                  <span>Status:</span>
                  <span className={`font-medium ${configStatus.isActive ? 'text-green-600' : 'text-yellow-600'}`}>
                    {configStatus.isActive ? 'Active' : 'Inactive'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Test Mode:</span>
                  <span className={`font-medium ${configStatus.testMode ? 'text-yellow-600' : 'text-green-600'}`}>
                    {configStatus.testMode ? 'Enabled' : 'Disabled'}
                  </span>
                </div>
                {configStatus.phoneNumber && (
                  <div className="flex justify-between">
                    <span>Phone Number:</span>
                    <span className="font-medium">{configStatus.phoneNumber}</span>
                  </div>
                )}
              </>
            )}
          </div>
        </div>

        {/* Environment Migration */}
        {envStatus?.hasEnvironmentConfig && !configStatus?.isConfigured && (
          <div className="mb-6 p-4 rounded-lg bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800">
            <h3 className="font-medium text-blue-800 dark:text-blue-300 mb-2">
              Environment Configuration Detected
            </h3>
            <p className="text-sm text-blue-700 dark:text-blue-400 mb-3">
              We found Twilio configuration in your environment variables. You can migrate this to the database for easier management.
            </p>
            <button
              onClick={handleMigrate}
              disabled={isLoading}
              className="btn-primary text-sm"
            >
              {isLoading ? "Migrating..." : "Migrate Configuration"}
            </button>
          </div>
        )}

        {/* Configuration Form */}
        <div className="space-y-4">
          <div>
            <label className="form-label">
              Account SID *
            </label>
            <input
              type="text"
              name="accountSid"
              value={formData.accountSid}
              onChange={handleInputChange}
              className="form-input"
              placeholder="ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
              maxLength={34}
            />
            <p className="text-xs text-gray-500 mt-1">
              Your Twilio Account SID (starts with AC, 34 characters)
            </p>
          </div>

          <div>
            <label className="form-label">
              Auth Token *
            </label>
            <input
              type="password"
              name="authToken"
              value={formData.authToken}
              onChange={handleInputChange}
              className="form-input"
              placeholder="Your Twilio Auth Token"
              maxLength={32}
            />
            <p className="text-xs text-gray-500 mt-1">
              Your Twilio Auth Token (32 characters)
            </p>
          </div>

          <div>
            <label className="form-label">
              Phone Number *
            </label>
            <input
              type="tel"
              name="phoneNumber"
              value={formData.phoneNumber}
              onChange={handleInputChange}
              className="form-input"
              placeholder="+**********"
            />
            <p className="text-xs text-gray-500 mt-1">
              Your Twilio phone number (E.164 format)
            </p>
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              name="testMode"
              checked={formData.testMode}
              onChange={handleInputChange}
              className="mr-2"
            />
            <label className="text-sm text-charcoal dark:text-gray-300">
              Enable test mode (for development)
            </label>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3 mt-6">
          <button
            onClick={handleTest}
            disabled={isTesting || !formData.accountSid || !formData.authToken || !formData.phoneNumber}
            className="btn-secondary flex-1"
          >
            {isTesting ? "Testing..." : "Test Configuration"}
          </button>
          <button
            onClick={handleSave}
            disabled={isLoading || !formData.accountSid || !formData.authToken || !formData.phoneNumber}
            className="btn-primary flex-1"
          >
            {isLoading ? "Saving..." : "Save Configuration"}
          </button>
        </div>

        {/* Help Text */}
        <div className="mt-6 p-4 rounded-lg bg-gray-50 dark:bg-gray-800">
          <h4 className="font-medium text-charcoal dark:text-gray-100 mb-2">
            How to get your Twilio credentials:
          </h4>
          <ol className="text-sm text-gray-600 dark:text-gray-400 space-y-1 list-decimal list-inside">
            <li>Sign up for a Twilio account at <a href="https://www.twilio.com" target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">twilio.com</a></li>
            <li>Go to your Twilio Console Dashboard</li>
            <li>Find your Account SID and Auth Token in the Account Info section</li>
            <li>Purchase a phone number from the Phone Numbers section</li>
            <li>Copy the credentials and phone number to the form above</li>
          </ol>
        </div>
      </div>
    </div>
  );
}

import React, { useState } from "react";
import { useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { TwilioConfig } from "./TwilioConfig";
import { SMSTemplates } from "./SMSTemplates";
import { SMSComposer } from "./SMSComposer";
import { SMSHistory } from "./SMSHistory";
import { SMSTest } from "./SMSTest";

export function SMSDashboard() {
  const [activeTab, setActiveTab] = useState("compose");
  const [showConfig, setShowConfig] = useState(false);

  const user = useQuery(api.auth.loggedInUser);
  const configStatus = useQuery(api.twilioConfig.getConfigStatus);
  const smsStats = useQuery(
    api.sms.getStatistics,
    (user?.role === "admin" || user?.role === "master") ? {} : "skip"
  );

  const isAdmin = user?.role === "admin" || user?.role === "master";

  // Check if user has admin role - SMS dashboard is admin-only
  if (user && !isAdmin) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="text-center py-12">
          <div className="text-6xl mb-4">🔒</div>
          <h2 className="text-2xl font-semibold text-charcoal dark:text-gray-100 mb-4">
            Access Restricted
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto">
            SMS functionality is only available to administrators. Please contact your system administrator if you need access.
          </p>
        </div>
      </div>
    );
  }

  const tabs = [
    { id: "compose", label: "Compose", icon: "✏️" },
    { id: "history", label: "History", icon: "📋" },
    { id: "templates", label: "Templates", icon: "📝" },
    ...(isAdmin ? [
      { id: "analytics", label: "Analytics", icon: "📊" },
      { id: "test", label: "Test", icon: "🧪" }
    ] : []),
  ];

  // Show configuration if SMS is not set up and user is admin
  if (isAdmin && !configStatus?.isConfigured && !showConfig) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="text-center py-12">
          <div className="text-6xl mb-4">📱</div>
          <h2 className="text-2xl font-semibold text-charcoal dark:text-gray-100 mb-4">
            SMS Service Not Configured
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto">
            To start sending SMS messages to your customers, you need to configure your Twilio credentials first.
          </p>
          <button
            onClick={() => setShowConfig(true)}
            className="btn-primary"
          >
            Configure Twilio SMS
          </button>
        </div>
      </div>
    );
  }

  if (showConfig) {
    return <TwilioConfig onClose={() => setShowConfig(false)} />;
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-semibold text-charcoal dark:text-gray-100">
            SMS Management
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Send SMS messages to your customers and manage templates
          </p>
        </div>
        {isAdmin && (
          <button
            onClick={() => setShowConfig(true)}
            className="btn-secondary"
          >
            ⚙️ SMS Settings
          </button>
        )}
      </div>

      {/* Status Banner */}
      {configStatus && (
        <div className={`p-4 rounded-lg mb-6 ${
          configStatus.isConfigured && configStatus.isActive
            ? "bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800"
            : "bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800"
        }`}>
          <div className="flex items-center justify-between">
            <div>
              <h3 className={`font-medium ${
                configStatus.isConfigured && configStatus.isActive
                  ? "text-green-800 dark:text-green-300"
                  : "text-yellow-800 dark:text-yellow-300"
              }`}>
                SMS Service Status
              </h3>
              <p className={`text-sm ${
                configStatus.isConfigured && configStatus.isActive
                  ? "text-green-700 dark:text-green-400"
                  : "text-yellow-700 dark:text-yellow-400"
              }`}>
                {configStatus.isConfigured && configStatus.isActive
                  ? `SMS service is active${configStatus.testMode ? " (Test Mode)" : ""}`
                  : configStatus.isConfigured
                    ? "SMS service is configured but inactive"
                    : "SMS service is not configured"
                }
                {configStatus.phoneNumber && ` • Phone: ${configStatus.phoneNumber}`}
              </p>
            </div>
            {configStatus.testMode && (
              <span className="px-3 py-1 bg-yellow-200 dark:bg-yellow-800 text-yellow-800 dark:text-yellow-200 rounded-full text-sm font-medium">
                Test Mode
              </span>
            )}
          </div>
        </div>
      )}

      {/* Quick Stats */}
      {isAdmin && smsStats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="card p-4">
            <div className="text-2xl font-semibold text-charcoal dark:text-gray-100">
              {smsStats.total}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Total Messages</div>
          </div>
          <div className="card p-4">
            <div className="text-2xl font-semibold text-green-600">
              {smsStats.delivered}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Delivered</div>
          </div>
          <div className="card p-4">
            <div className="text-2xl font-semibold text-red-600">
              {smsStats.failed}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Failed</div>
          </div>
          <div className="card p-4">
            <div className="text-2xl font-semibold text-blue-600">
              {smsStats.deliveryRate.toFixed(1)}%
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Delivery Rate</div>
          </div>
        </div>
      )}

      {/* Navigation Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700 mb-6">
        <nav className="flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === tab.id
                  ? "border-primary text-primary"
                  : "border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="min-h-[400px]">
        {activeTab === "compose" && (
          <div className="max-w-2xl">
            <SMSComposer />
          </div>
        )}
        
        {activeTab === "history" && (
          <SMSHistory showAllHistory={isAdmin} />
        )}
        
        {activeTab === "templates" && (
          <SMSTemplates />
        )}
        
        {activeTab === "analytics" && isAdmin && (
          <SMSAnalytics stats={smsStats} />
        )}
      </div>
    </div>
  );
}

interface SMSAnalyticsProps {
  stats: any;
}

function SMSAnalytics({ stats }: SMSAnalyticsProps) {
  if (!stats) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 text-6xl mb-4">📊</div>
        <h3 className="text-lg font-medium text-gray-600 dark:text-gray-400 mb-2">
          Loading Analytics...
        </h3>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-charcoal dark:text-gray-100 mb-4">
          SMS Analytics
        </h3>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="card p-6">
          <div className="text-3xl font-bold text-charcoal dark:text-gray-100 mb-2">
            {stats.total}
          </div>
          <div className="text-gray-600 dark:text-gray-400">Total Messages</div>
        </div>
        
        <div className="card p-6">
          <div className="text-3xl font-bold text-green-600 mb-2">
            {stats.delivered}
          </div>
          <div className="text-gray-600 dark:text-gray-400">Delivered</div>
          <div className="text-sm text-green-600 mt-1">
            {stats.total > 0 ? ((stats.delivered / stats.total) * 100).toFixed(1) : 0}% of total
          </div>
        </div>
        
        <div className="card p-6">
          <div className="text-3xl font-bold text-red-600 mb-2">
            {stats.failed}
          </div>
          <div className="text-gray-600 dark:text-gray-400">Failed</div>
          <div className="text-sm text-red-600 mt-1">
            {stats.total > 0 ? ((stats.failed / stats.total) * 100).toFixed(1) : 0}% of total
          </div>
        </div>
        
        <div className="card p-6">
          <div className="text-3xl font-bold text-blue-600 mb-2">
            {stats.deliveryRate.toFixed(1)}%
          </div>
          <div className="text-gray-600 dark:text-gray-400">Delivery Rate</div>
          <div className="text-sm text-blue-600 mt-1">
            Delivered vs Sent
          </div>
        </div>
      </div>

      {/* Status Breakdown */}
      <div className="card p-6">
        <h4 className="text-lg font-semibold text-charcoal dark:text-gray-100 mb-4">
          Message Status Breakdown
        </h4>
        <div className="space-y-3">
          {[
            { status: "delivered", count: stats.delivered, color: "bg-green-500" },
            { status: "sent", count: stats.sent, color: "bg-blue-500" },
            { status: "failed", count: stats.failed, color: "bg-red-500" },
            { status: "pending", count: stats.pending, color: "bg-yellow-500" },
          ].map(({ status, count, color }) => (
            <div key={status} className="flex items-center">
              <div className="w-32 text-sm text-gray-600 dark:text-gray-400 capitalize">
                {status}
              </div>
              <div className="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-4">
                <div
                  className={`h-2 rounded-full ${color}`}
                  style={{
                    width: stats.total > 0 ? `${(count / stats.total) * 100}%` : "0%",
                  }}
                />
              </div>
              <div className="w-16 text-sm font-medium text-charcoal dark:text-gray-100">
                {count}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Usage by User */}
      {Object.keys(stats.byUser).length > 0 && (
        <div className="card p-6">
          <h4 className="text-lg font-semibold text-charcoal dark:text-gray-100 mb-4">
            Usage by User
          </h4>
          <div className="space-y-2">
            {Object.entries(stats.byUser)
              .sort(([, a], [, b]) => (b as number) - (a as number))
              .slice(0, 10)
              .map(([userId, count]) => (
                <div key={userId} className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    User {userId.slice(-8)}
                  </span>
                  <span className="font-medium text-charcoal dark:text-gray-100">
                    {count as number}
                  </span>
                </div>
              ))}
          </div>
        </div>
      )}
    </div>
  );
}

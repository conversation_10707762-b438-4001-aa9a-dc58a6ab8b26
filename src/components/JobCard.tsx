interface JobCardProps {
  job: {
    _id: string;
    title: string;
    status: string;
    priority: string;
    customer?: {
      name: string;
    };
  };
}

export function JobCard({ job }: JobCardProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "scheduled": return "status-pending";
      case "in-progress": return "bg-blue-100 text-blue-800";
      case "completed": return "status-active";
      case "cancelled": return "status-error";
      default: return "status-inactive";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "emergency": return "text-red-600";
      case "high": return "text-orange-600";
      case "medium": return "text-yellow-600";
      case "low": return "text-green-600";
      default: return "text-gray-600";
    }
  };

  return (
    <div className="flex items-center gap-3 p-3 bg-neutral rounded-lg border border-gray-100 hover:bg-gray-50 transition-colors">
      <div className="w-8 h-8 md:w-10 md:h-10 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0">
        <span className="text-sm md:text-base">🔧</span>
      </div>
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2 mb-1">
          <h3 className="font-medium text-charcoal text-sm md:text-base truncate">{job.title}</h3>
          <span className={`text-xs font-medium ${getPriorityColor(job.priority)}`}>
            {job.priority}
          </span>
        </div>
        <p className="text-xs md:text-sm text-gray-600 truncate">{job.customer?.name}</p>
      </div>
      <span className={`status-badge ${getStatusColor(job.status)} flex-shrink-0`}>
        {job.status}
      </span>
    </div>
  );
}
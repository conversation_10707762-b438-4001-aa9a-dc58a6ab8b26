import React, { useState } from "react";
import { useQuery, useAction } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { toast } from "sonner";
import { usePDFOperations } from "../hooks/usePDFOperations";

interface PDFTestComponentProps {
  invoiceId?: Id<"invoices">;
}

export function PDFTestComponent({ invoiceId }: PDFTestComponentProps) {
  const [selectedInvoiceId, setSelectedInvoiceId] = useState<Id<"invoices"> | undefined>(invoiceId);
  const [testResults, setTestResults] = useState<string[]>([]);
  
  // Get list of invoices for testing
  const invoices = useQuery(api.invoices.list);
  
  // PDF operations for the selected invoice
  const {
    hasPDF,
    pdfUrl,
    generatePDF,
    downloadPDF,
    downloadPDFDirect,
    removePDF,
    getPDFInfo,
  } = usePDFOperations(selectedInvoiceId);

  const addTestResult = (message: string, success: boolean = true) => {
    const timestamp = new Date().toLocaleTimeString();
    const status = success ? "✅" : "❌";
    const result = `[${timestamp}] ${status} ${message}`;
    setTestResults(prev => [result, ...prev]);
  };

  const runFullTest = async () => {
    if (!selectedInvoiceId) {
      toast.error("Please select an invoice first");
      return;
    }

    addTestResult("Starting PDF functionality test...");
    
    try {
      // Test 1: Check initial PDF status
      addTestResult(`Initial PDF status: ${hasPDF ? "Has PDF" : "No PDF"}`);
      
      // Test 2: Generate PDF if not exists
      if (!hasPDF) {
        addTestResult("Generating PDF...");
        const generateResult = await generatePDF();
        addTestResult(`PDF Generation: ${generateResult.message}`, generateResult.success);
        
        if (!generateResult.success) {
          addTestResult("Test failed at PDF generation", false);
          return;
        }
      }
      
      // Test 3: Get PDF info
      addTestResult("Getting PDF info...");
      const pdfInfo = await getPDFInfo();
      if (pdfInfo) {
        addTestResult(`PDF Info: ${pdfInfo.hasPdf ? "Available" : "Not available"} - ${pdfInfo.invoiceNumber || "No invoice number"}`);
      } else {
        addTestResult("Failed to get PDF info", false);
      }
      
      // Test 4: Test download functionality
      if (pdfUrl) {
        addTestResult(`PDF URL available: ${pdfUrl.substring(0, 50)}...`);
        addTestResult("Testing download (will open in new tab)...");
        await downloadPDF();
      } else {
        addTestResult("PDF URL not available", false);
      }
      
      addTestResult("PDF functionality test completed!");
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error";
      addTestResult(`Test failed with error: ${errorMessage}`, false);
    }
  };

  const testPDFDeletion = async () => {
    if (!selectedInvoiceId) {
      toast.error("Please select an invoice first");
      return;
    }

    if (!hasPDF) {
      toast.error("No PDF to delete");
      return;
    }

    addTestResult("Testing PDF deletion...");
    const deleteResult = await removePDF();
    addTestResult(`PDF Deletion: ${deleteResult.message}`, deleteResult.success);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  if (!invoices) {
    return <div>Loading invoices...</div>;
  }

  return (
    <div className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">
        PDF Functionality Test
      </h2>
      
      {/* Invoice Selection */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Select Invoice for Testing:
        </label>
        <select
          value={selectedInvoiceId || ""}
          onChange={(e) => setSelectedInvoiceId(e.target.value as Id<"invoices"> || undefined)}
          className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
        >
          <option value="">-- Select an Invoice --</option>
          {invoices.map((invoice) => (
            <option key={invoice._id} value={invoice._id}>
              #{invoice.invoiceNumber} - {invoice.customer?.name || "Unknown"} - ${invoice.total.toFixed(2)}
              {invoice.pdfStorageId ? " (Has PDF)" : " (No PDF)"}
            </option>
          ))}
        </select>
      </div>

      {/* Current Status */}
      {selectedInvoiceId && (
        <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <h3 className="text-lg font-semibold mb-2 text-gray-900 dark:text-white">Current Status</h3>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium">Has PDF:</span> 
              <span className={hasPDF ? "text-green-600" : "text-red-600"}>
                {hasPDF ? "Yes" : "No"}
              </span>
            </div>
            <div>
              <span className="font-medium">PDF URL:</span> 
              <span className={pdfUrl ? "text-green-600" : "text-gray-500"}>
                {pdfUrl ? "Available" : "Not available"}
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Test Controls */}
      <div className="mb-6 flex flex-wrap gap-3">
        <button
          onClick={runFullTest}
          disabled={!selectedInvoiceId}
          className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Run Full Test
        </button>
        
        <button
          onClick={testPDFDeletion}
          disabled={!selectedInvoiceId || !hasPDF}
          className="btn-danger disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Test PDF Deletion
        </button>
        
        <button
          onClick={clearResults}
          className="btn-secondary"
        >
          Clear Results
        </button>
      </div>

      {/* Test Results */}
      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
        <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">Test Results</h3>
        <div className="max-h-96 overflow-y-auto">
          {testResults.length === 0 ? (
            <p className="text-gray-500 dark:text-gray-400 italic">No test results yet. Run a test to see results here.</p>
          ) : (
            <div className="space-y-1">
              {testResults.map((result, index) => (
                <div
                  key={index}
                  className="text-sm font-mono bg-white dark:bg-gray-800 p-2 rounded border"
                >
                  {result}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

import { useTheme } from '../hooks/useTheme';

export function ThemeToggle() {
  const { theme, setTheme, isDark } = useTheme();

  const themes = [
    { value: 'light', label: 'Light', icon: '☀️' },
    { value: 'dark', label: 'Dark', icon: '🌙' },
    { value: 'system', label: 'System', icon: '💻' }
  ] as const;

  return (
    <div className="relative">
      <button
        onClick={() => {
          const currentIndex = themes.findIndex(t => t.value === theme);
          const nextIndex = (currentIndex + 1) % themes.length;
          setTheme(themes[nextIndex].value);
        }}
        className="flex items-center gap-1 px-2 py-1.5 rounded-md bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors touch-manipulation"
        title={`Current theme: ${themes.find(t => t.value === theme)?.label}`}
      >
        <span className="text-sm">
          {isDark ? '🌙' : '☀️'}
        </span>
        <span className="hidden sm:inline text-xs font-medium text-gray-700 dark:text-gray-300">
          {themes.find(t => t.value === theme)?.label}
        </span>
      </button>
    </div>
  );
}

export function ThemeSelector() {
  const { theme, setTheme } = useTheme();

  const themes = [
    { value: 'light', label: 'Light', icon: '☀️', description: 'Light theme' },
    { value: 'dark', label: 'Dark', icon: '🌙', description: 'Dark theme' },
    { value: 'system', label: 'System', icon: '💻', description: 'Follow system preference' }
  ] as const;

  return (
    <div className="space-y-3">
      {themes.map((themeOption) => (
        <button
          key={themeOption.value}
          onClick={() => setTheme(themeOption.value)}
          className={`w-full flex items-center gap-3 p-3 md:p-4 rounded-lg border transition-colors touch-manipulation ${
            theme === themeOption.value
              ? 'bg-primary/10 border-primary text-primary dark:bg-primary/20'
              : 'bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-700'
          }`}
        >
          <div className="w-8 h-8 md:w-10 md:h-10 bg-white dark:bg-gray-900 rounded-full flex items-center justify-center border border-gray-200 dark:border-gray-700">
            <span className="text-sm md:text-base">{themeOption.icon}</span>
          </div>
          <div className="text-left flex-1">
            <h4 className="font-medium text-sm md:text-base">{themeOption.label}</h4>
            <p className="text-xs md:text-sm text-gray-600 dark:text-gray-400">{themeOption.description}</p>
          </div>
          {theme === themeOption.value && (
            <div className="w-5 h-5 bg-primary rounded-full flex items-center justify-center">
              <span className="text-white text-xs">✓</span>
            </div>
          )}
        </button>
      ))}
    </div>
  );
}

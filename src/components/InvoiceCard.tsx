interface InvoiceCardProps {
  invoice: {
    _id: string;
    invoiceNumber: string;
    total: number;
    status: string;
    dueDate: number;
    customer?: {
      name: string;
      email?: string;
      phone?: string;
      address?: string;
      city?: string;
      state?: string;
      zipCode?: string;
      company?: string;
    };
  };
}

export function InvoiceCard({ invoice }: InvoiceCardProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "draft": return "status-inactive";
      case "sent": return "bg-blue-100 text-blue-800";
      case "paid": return "status-active";
      case "overdue": return "status-error";
      default: return "status-inactive";
    }
  };

  const isOverdue = invoice.status === "sent" && invoice.dueDate < Date.now();

  return (
    <div className="flex items-center gap-3 p-3 bg-neutral rounded-lg border border-gray-100 hover:bg-gray-50 transition-colors">
      <div className="w-8 h-8 md:w-10 md:h-10 bg-accent/10 rounded-full flex items-center justify-center flex-shrink-0">
        <span className="text-sm md:text-base">💰</span>
      </div>
      <div className="flex-1 min-w-0">
        <h3 className="font-medium text-charcoal text-sm md:text-base truncate">{invoice.invoiceNumber}</h3>
        <div className="text-xs md:text-sm text-gray-600">
          <p className="truncate font-medium">{invoice.customer?.name || "N/A"}</p>
          {invoice.customer?.email && (
            <p className="truncate text-xs">{invoice.customer.email}</p>
          )}
        </div>
      </div>
      <div className="text-right flex-shrink-0">
        <div className="font-semibold text-charcoal text-sm md:text-base">
          ${invoice.total.toLocaleString()}
        </div>
        <span className={`status-badge ${getStatusColor(isOverdue ? "overdue" : invoice.status)}`}>
          {isOverdue ? "overdue" : invoice.status}
        </span>
      </div>
    </div>
  );
}
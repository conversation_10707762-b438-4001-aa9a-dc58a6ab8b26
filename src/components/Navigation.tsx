import { useState } from "react";
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";

interface NavigationProps {
  activeView: string;
  onViewChange: (view: string) => void;
}

export function Navigation({ activeView, onViewChange }: NavigationProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const user = useQuery(api.auth.loggedInUser);
  const isAdmin = user?.role === 'admin' || user?.role === 'master';
  const isMaster = user?.role === 'master';

  // Base navigation items for all users
  const baseNavItems = [
    { id: "dashboard", label: "Dashboard", icon: "📊", color: "text-blue-600 dark:text-blue-400" },
    { id: "customers", label: "Customers", icon: "👥", color: "text-green-600 dark:text-green-400" },
    { id: "jobs", label: "Jobs", icon: "🔧", color: "text-orange-600 dark:text-orange-400" },
    { id: "products", label: "Products", icon: "📦", color: "text-purple-600 dark:text-purple-400" },
    { id: "invoices", label: "Invoices", icon: "💰", color: "text-emerald-600 dark:text-emerald-400" },
    { id: "profile", label: "Profile", icon: "👤", color: "text-gray-600 dark:text-gray-400" },
  ];

  // Admin-only navigation items
  const adminNavItems = isAdmin ? [
    { id: "conversations", label: "Conversations", icon: "💬", color: "text-indigo-600 dark:text-indigo-400" },
    { id: "sms", label: "SMS", icon: "📱", color: "text-blue-500 dark:text-blue-400" },
    { id: "settings", label: "Settings", icon: "⚙️", color: "text-red-600 dark:text-red-400" },
  ] : [];

  // Master-only navigation items
  const masterNavItems = isMaster ? [
    { id: "user-management", label: "User Management", icon: "👥", color: "text-yellow-600 dark:text-yellow-400" },
    { id: "admin-approvals", label: "Admin Approvals", icon: "✅", color: "text-green-600 dark:text-green-400" },
  ] : [];

  // Combined navigation items
  const navItems = [...baseNavItems, ...adminNavItems, ...masterNavItems];

  const handleNavClick = (viewId: string) => {
    onViewChange(viewId);
    setIsMobileMenuOpen(false);
  };

  return (
    <>
      {/* Desktop Sidebar - Fixed height, never scrolls */}
      <nav className={`hidden md:flex md:flex-col w-full sidebar-bg border-r border-gray-200 dark:border-gray-700 transition-all duration-300 h-full`}>
        <div className="px-4 lg:px-6 py-6 space-y-2 flex flex-col flex-1 h-full w-full">
          <div className="flex-1 flex flex-col space-y-2 overflow-hidden w-full">
          {navItems.map((item) => (
            <button
              key={item.id}
              onClick={() => handleNavClick(item.id)}
              className={`w-full flex items-center gap-3 px-4 lg:px-6 py-3 rounded-lg text-left transition-all duration-300 ${
                activeView === item.id
                  ? "sidebar-item-active"
                  : "sidebar-item-inactive sidebar-item-hover"
              }`}
            >
              <span className={`text-xl lg:text-2xl ${
                activeView === item.id
                  ? "text-primary dark:text-accent"
                  : item.color
              }`}>
                {item.icon}
              </span>
              <span className="font-medium lg:text-lg">{item.label}</span>
              {activeView === item.id && (
                <div className="ml-auto w-2 h-2 bg-primary dark:bg-accent rounded-full"></div>
              )}
            </button>
          ))}
        </div>
        </div>
      </nav>

      {/* Mobile Bottom Navigation - Fixed positioning with stable background */}
      <nav className="md:hidden fixed bottom-0 left-0 right-0 z-50 mobile-nav-bg border-t border-gray-200 dark:border-gray-700">
        {/* Safe area padding for devices with home indicators */}
        <div className="px-2 pt-2 pb-2 safe-area-bottom">
          <div className="flex justify-around items-center">
            {/* Main navigation items - show first 4 on mobile */}
            {navItems.slice(0, 4).map((item) => (
                  <button
                      key={item.id}
                      onClick={() => handleNavClick(item.id)}
                className={`flex flex-col items-center justify-center gap-1 px-3 py-2 rounded-lg touch-manipulation min-w-0 flex-1 ${
                  activeView === item.id
                    ? "text-primary bg-primary/10 dark:bg-primary/20 scale-105"
                    : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 active:scale-95"
                      }`}
                      style={{
                        WebkitTapHighlightColor: 'transparent',
                  minHeight: '60px',
                  transition: 'transform 0.2s ease, color 0.2s ease' // Only animate transform and color
                      }}
                    >
                <span className={`text-lg ${
                  activeView === item.id ? 'scale-110' : ''
                }`} style={{ transition: 'transform 0.2s ease' }}>
                        {item.icon}
                      </span>
                <span className="text-xs font-medium truncate w-full text-center leading-tight">
                  {item.label}
                </span>
                {activeView === item.id && (
                  <div className="w-1 h-1 bg-primary rounded-full mt-0.5 animate-pulse"></div>
                )}
              </button>
            ))}

            {/* More Menu Button */}
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className={`flex flex-col items-center justify-center gap-1 px-3 py-2 rounded-lg touch-manipulation min-w-0 flex-1 ${
                isMobileMenuOpen
                  ? "text-primary bg-primary/10 dark:bg-primary/20 scale-105"
                  : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 active:scale-95"
              }`}
              style={{
                WebkitTapHighlightColor: 'transparent',
                minHeight: '60px',
                transition: 'transform 0.2s ease, color 0.2s ease' // Only animate transform and color
              }}
            >
              <span className={`text-lg ${
                isMobileMenuOpen ? 'rotate-90 scale-110' : ''
              }`} style={{ transition: 'transform 0.2s ease' }}>
                ⋯
              </span>
              <span className="text-xs font-medium leading-tight text-center">More</span>
              {isMobileMenuOpen && (
                <div className="w-1 h-1 bg-primary rounded-full mt-0.5 animate-pulse"></div>
              )}
            </button>
          </div>
        </div>
      </nav>

      {/* Mobile More Menu Overlay - Fixed background color */}
      {isMobileMenuOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="md:hidden fixed inset-0 bg-black bg-opacity-50 z-40"
            onClick={() => setIsMobileMenuOpen(false)}
            style={{ transition: 'opacity 0.3s ease' }}
          />
          
          {/* Menu Panel - Fixed background */}
          <div className="md:hidden fixed bottom-0 left-0 right-0 z-50" style={{ transition: 'transform 0.3s ease' }}>
            <div className="mobile-nav-overlay-bg rounded-t-xl shadow-2xl border-t border-gray-200 dark:border-gray-700">
              {/* Handle bar for visual feedback */}
              <div className="flex justify-center pt-3 pb-2">
                <div className="w-10 h-1 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
              </div>
              
              <div className="px-4 pb-4 safe-area-bottom">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-semibold text-charcoal dark:text-gray-100">Menu</h3>
                  <button
                    onClick={() => setIsMobileMenuOpen(false)}
                    className="w-8 h-8 flex items-center justify-center rounded-full bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600 touch-manipulation active:scale-95"
                    style={{ 
                      WebkitTapHighlightColor: 'transparent',
                      transition: 'transform 0.2s ease, background-color 0.2s ease'
                    }}
                  >
                    ✕
                  </button>
                </div>
                
                {/* All navigation items in grid */}
                <div className="grid grid-cols-2 gap-3">
                  {navItems.map((item) => (
                    <button
                      key={item.id}
                      onClick={() => handleNavClick(item.id)}
                      className={`flex items-center gap-3 p-4 rounded-lg text-left touch-manipulation active:scale-95 ${
                        activeView === item.id
                          ? "bg-primary/10 dark:bg-primary/20 text-primary border border-primary/20 shadow-sm"
                          : "bg-gray-50 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 border border-transparent"
                      }`}
                      style={{
                        WebkitTapHighlightColor: 'transparent',
                        minHeight: '64px',
                        transition: 'transform 0.2s ease, background-color 0.2s ease, color 0.2s ease'
                      }}
                    >
                      <span className={`text-xl ${activeView === item.id ? "text-primary" : item.color}`}>
                        {item.icon}
                      </span>
                      <div className="flex-1 min-w-0">
                        <span className="font-medium block truncate">{item.label}</span>
                        {activeView === item.id && (
                          <span className="text-xs text-primary/70 dark:text-primary/80">Active</span>
                        )}
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </>
  );
}
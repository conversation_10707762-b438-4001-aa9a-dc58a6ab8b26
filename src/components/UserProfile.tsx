import { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { toast } from "sonner";
import { ThemeSelector } from "./ThemeToggle";

// User profile interface with role-based features
export function UserProfile() {
  const user = useQuery(api.auth.loggedInUser);
  const userProfile = useQuery(api.users.getProfile);
  const [isEditing, setIsEditing] = useState(false);
  const [activeTab, setActiveTab] = useState("overview");

  if (!user || !userProfile) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4 md:space-y-6 pb-20 md:pb-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold text-charcoal dark:text-gray-100">User Profile</h1>
          <p className="text-gray-600 dark:text-gray-400 text-sm md:text-base">Manage your account settings and preferences</p>
        </div>
      </div>

      {/* Profile Card */}
      <div className="card overflow-hidden">
        <div className="relative h-24 md:h-32 bg-gradient-to-r from-primary to-accent"></div>
        
        <div className="relative px-4 md:px-8 pb-6 md:pb-8">
          {/* Profile Photo */}
          <div className="absolute -top-12 md:-top-16 left-4 md:left-8">
            <div className="relative">
              <div className="w-24 h-24 md:w-32 md:h-32 rounded-full bg-white dark:bg-gray-800 p-1 shadow-lg">
                <div className="w-full h-full rounded-full bg-gradient-to-br from-primary to-accent flex items-center justify-center">
                  <span className="text-2xl md:text-4xl font-bold text-white">
                    {user.name ? user.name.charAt(0).toUpperCase() : "U"}
                  </span>
                </div>
              </div>
              <button className="absolute bottom-1 md:bottom-2 right-1 md:right-2 w-6 h-6 md:w-8 md:h-8 bg-white dark:bg-gray-800 rounded-full shadow-md flex items-center justify-center hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors touch-manipulation">
                <span className="text-xs md:text-sm">📷</span>
              </button>
            </div>
          </div>

          {/* Profile Info */}
          <div className="pt-16 md:pt-20">
            <div className="flex flex-col md:flex-row md:items-start md:justify-between gap-4">
              <div className="flex-1">
                <div className="flex flex-col md:flex-row md:items-center gap-2 md:gap-3 mb-2">
                  <h2 className="text-xl md:text-2xl font-bold text-charcoal dark:text-gray-100">
                    {user.name || "User"}
                  </h2>
                  {userProfile.role && <RoleBadge role={userProfile.role} />}
                </div>
                <p className="text-gray-600 dark:text-gray-400 mb-1 text-sm md:text-base">{user.email}</p>
                <p className="text-gray-600 dark:text-gray-400 text-sm md:text-base">{userProfile.phone || "No phone number"}</p>
                
                {/* Activity Summary */}
                <div className="grid grid-cols-3 gap-4 md:gap-6 mt-4">
                  <div className="text-center">
                    <div className="text-lg md:text-xl font-bold text-primary">{userProfile.stats.totalJobs}</div>
                    <div className="text-xs md:text-sm text-gray-600 dark:text-gray-400">Jobs</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg md:text-xl font-bold text-accent">{userProfile.stats.totalCustomers}</div>
                    <div className="text-xs md:text-sm text-gray-600 dark:text-gray-400">Customers</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg md:text-xl font-bold text-primary">${userProfile.stats.totalRevenue.toLocaleString()}</div>
                    <div className="text-xs md:text-sm text-gray-600 dark:text-gray-400">Revenue</div>
                  </div>
                </div>
              </div>

              <button
                onClick={() => setIsEditing(true)}
                className="btn-primary w-full md:w-auto touch-manipulation"
              >
                <span className="mr-2">✏️</span>
                Edit Profile
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="card">
        <div className="border-b border-gray-100 dark:border-gray-700">
          <nav className="flex overflow-x-auto px-4 md:px-6">
            {[
              { id: "overview", label: "Overview", icon: "📊" },
              { id: "settings", label: "Settings", icon: "⚙️" },
              { id: "security", label: "Security", icon: "🔒" },
              ...(userProfile.role === "admin" || userProfile.role === "master"
                ? [{ id: "admin", label: "Admin", icon: "👑" }]
                : [])
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center gap-2 py-3 md:py-4 px-3 md:px-4 border-b-2 font-medium text-sm whitespace-nowrap transition-colors touch-manipulation ${
                  activeTab === tab.id
                    ? "border-primary text-primary"
                    : "border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
                }`}
              >
                <span>{tab.icon}</span>
                <span className="hidden sm:inline">{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>

        <div className="p-4 md:p-6">
          <TabContent activeTab={activeTab} userProfile={userProfile} />
        </div>
      </div>

      {/* Edit Profile Modal */}
      {isEditing && (
        <EditProfileModal
          user={user}
          userProfile={userProfile}
          onClose={() => setIsEditing(false)}
          onSuccess={() => {
            setIsEditing(false);
            toast.success("Profile updated successfully");
          }}
        />
      )}
    </div>
  );
}

// Role badge component
function RoleBadge({ role }: { role: string }) {
  const roleConfig = {
    master: {
      label: "Master",
      icon: "⭐",
      className: "bg-gradient-to-r from-yellow-100 to-orange-100 dark:from-yellow-900/30 dark:to-orange-900/30 text-yellow-800 dark:text-yellow-300 border border-yellow-200 dark:border-yellow-700 shadow-md"
    },
    admin: {
      label: "Administrator",
      icon: "👑",
      className: "bg-gradient-to-r from-purple-100 to-pink-100 dark:from-purple-900/30 dark:to-pink-900/30 text-purple-800 dark:text-purple-300 border border-purple-200 dark:border-purple-700"
    },
    staff: {
      label: "Staff Member",
      icon: "👤",
      className: "bg-gradient-to-r from-blue-100 to-teal-100 dark:from-blue-900/30 dark:to-teal-900/30 text-blue-800 dark:text-blue-300 border border-blue-200 dark:border-blue-700"
    }
  };

  const config = roleConfig[role as keyof typeof roleConfig] || roleConfig.staff;

  return (
    <span className={`inline-flex items-center gap-1 px-2 md:px-3 py-1 rounded-full text-xs md:text-sm font-medium ${config.className}`}>
      <span>{config.icon}</span>
      <span className="hidden sm:inline">{config.label}</span>
    </span>
  );
}

// Tab content component
function TabContent({ activeTab, userProfile }: { activeTab: string; userProfile: any }) {
  switch (activeTab) {
    case "overview":
      return <OverviewTab userProfile={userProfile} />;
    case "settings":
      return <SettingsTab userProfile={userProfile} />;
    case "security":
      return <SecurityTab />;
    case "admin":
      return (userProfile.role === "admin" || userProfile.role === "master") ? <AdminTab /> : null;
    default:
      return <OverviewTab userProfile={userProfile} />;
  }
}

// Overview tab content
function OverviewTab({ userProfile }: { userProfile: any }) {
  return (
    <div className="space-y-4 md:space-y-6">
      <div>
        <h3 className="text-base md:text-lg font-semibold text-charcoal dark:text-gray-100 mb-3 md:mb-4">Recent Activity</h3>
        <div className="space-y-3">
          {userProfile.recentActivity?.map((activity: any, index: number) => (
            <div key={index} className="flex items-center gap-3 p-3 md:p-4 bg-neutral dark:bg-gray-700 rounded-lg border border-gray-100 dark:border-gray-600">
              <div className="w-8 h-8 md:w-10 md:h-10 bg-primary/10 dark:bg-primary/20 rounded-full flex items-center justify-center flex-shrink-0">
                <span className="text-sm md:text-base">{activity.type === "job" ? "🔧" : "💰"}</span>
              </div>
              <div className="flex-1 min-w-0">
                <p className="font-medium text-charcoal dark:text-gray-100 text-sm md:text-base truncate">{activity.title}</p>
                <p className="text-xs md:text-sm text-gray-600 dark:text-gray-400 truncate">{activity.description}</p>
              </div>
              <span className="text-xs md:text-sm text-gray-500 dark:text-gray-400 flex-shrink-0">
                {new Date(activity.timestamp).toLocaleDateString()}
              </span>
            </div>
          )) || (
            <p className="text-gray-500 dark:text-gray-400 text-center py-8 text-sm md:text-base">No recent activity</p>
          )}
        </div>
      </div>

      <div>
        <h3 className="text-base md:text-lg font-semibold text-charcoal dark:text-gray-100 mb-3 md:mb-4">Performance Metrics</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3 md:gap-4">
          <MetricCard
            title="Jobs Completed"
            value={userProfile.stats.completedJobs}
            icon="✅"
            trend="+12%"
            trendUp={true}
          />
          <MetricCard
            title="Customer Satisfaction"
            value="4.8/5"
            icon="⭐"
            trend="+0.2"
            trendUp={true}
          />
          <MetricCard
            title="Response Time"
            value="2.3h"
            icon="⏱️"
            trend="-15min"
            trendUp={true}
          />
        </div>
      </div>
    </div>
  );
}

// Settings tab content
function SettingsTab({ userProfile }: { userProfile: any }) {
  const updateSettings = useMutation(api.users.updateSettings);
  const [notifications, setNotifications] = useState(userProfile.settings?.notifications || {
    email: true,
    push: true,
    sms: false
  });
  const [language, setLanguage] = useState(userProfile.settings?.language || "en");
  const [timezone, setTimezone] = useState(userProfile.settings?.timezone || "America/New_York");
  const [autoSave, setAutoSave] = useState(userProfile.settings?.autoSave || true);

  const handleNotificationChange = async (key: string, value: boolean) => {
    const newNotifications = { ...notifications, [key]: value };
    setNotifications(newNotifications);
    
    try {
      await updateSettings({ notifications: newNotifications });
      toast.success("Notification settings updated");
    } catch (error) {
      toast.error("Failed to update notification settings");
      // Revert on error
      setNotifications(notifications);
    }
  };

  const handleLanguageChange = async (newLanguage: string) => {
    setLanguage(newLanguage);
    try {
      await updateSettings({ language: newLanguage });
      toast.success("Language preference updated");
    } catch (error) {
      toast.error("Failed to update language preference");
      setLanguage(language);
    }
  };

  const handleTimezoneChange = async (newTimezone: string) => {
    setTimezone(newTimezone);
    try {
      await updateSettings({ timezone: newTimezone });
      toast.success("Timezone updated");
    } catch (error) {
      toast.error("Failed to update timezone");
      setTimezone(timezone);
    }
  };

  const handleAutoSaveChange = async (value: boolean) => {
    setAutoSave(value);
    try {
      await updateSettings({ autoSave: value });
      toast.success("Auto-save preference updated");
    } catch (error) {
      toast.error("Failed to update auto-save preference");
      setAutoSave(autoSave);
    }
  };

  return (
    <div className="space-y-4 md:space-y-6">
      <div>
        <h3 className="text-base md:text-lg font-semibold text-charcoal dark:text-gray-100 mb-3 md:mb-4">Theme Preferences</h3>
        <ThemeSelector />
      </div>

      <div>
        <h3 className="text-base md:text-lg font-semibold text-charcoal dark:text-gray-100 mb-3 md:mb-4">Language & Region</h3>
        <div className="space-y-3 md:space-y-4">
          <div className="p-3 md:p-4 bg-neutral dark:bg-gray-700 rounded-lg border border-gray-100 dark:border-gray-600">
            <label className="form-label">Language</label>
            <select
              value={language}
              onChange={(e) => handleLanguageChange(e.target.value)}
              className="form-input"
            >
              <option value="en">English</option>
              <option value="es">Spanish</option>
              <option value="fr">French</option>
            </select>
          </div>
          
          <div className="p-3 md:p-4 bg-neutral dark:bg-gray-700 rounded-lg border border-gray-100 dark:border-gray-600">
            <label className="form-label">Timezone</label>
            <select
              value={timezone}
              onChange={(e) => handleTimezoneChange(e.target.value)}
              className="form-input"
            >
              <option value="America/New_York">Eastern Time (ET)</option>
              <option value="America/Chicago">Central Time (CT)</option>
              <option value="America/Denver">Mountain Time (MT)</option>
              <option value="America/Los_Angeles">Pacific Time (PT)</option>
            </select>
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-base md:text-lg font-semibold text-charcoal dark:text-gray-100 mb-3 md:mb-4">Notification Preferences</h3>
        <div className="space-y-3 md:space-y-4">
          {[
            { key: "email", label: "Email Notifications", description: "Receive updates via email" },
            { key: "push", label: "Push Notifications", description: "Browser push notifications" },
            { key: "sms", label: "SMS Notifications", description: "Text message alerts" }
          ].map((setting) => (
            <div key={setting.key} className="flex items-center justify-between p-3 md:p-4 bg-neutral dark:bg-gray-700 rounded-lg border border-gray-100 dark:border-gray-600">
              <div className="flex-1 min-w-0 mr-4">
                <h4 className="font-medium text-charcoal dark:text-gray-100 text-sm md:text-base">{setting.label}</h4>
                <p className="text-xs md:text-sm text-gray-600 dark:text-gray-400">{setting.description}</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer touch-manipulation">
                <input
                  type="checkbox"
                  checked={notifications[setting.key as keyof typeof notifications]}
                  onChange={(e) => handleNotificationChange(setting.key, e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 dark:bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
              </label>
            </div>
          ))}
        </div>
      </div>

      <div>
        <h3 className="text-base md:text-lg font-semibold text-charcoal dark:text-gray-100 mb-3 md:mb-4">Application Preferences</h3>
        <div className="space-y-3 md:space-y-4">
          <div className="flex items-center justify-between p-3 md:p-4 bg-neutral dark:bg-gray-700 rounded-lg border border-gray-100 dark:border-gray-600">
            <div className="flex-1 min-w-0 mr-4">
              <h4 className="font-medium text-charcoal dark:text-gray-100 text-sm md:text-base">Auto-save</h4>
              <p className="text-xs md:text-sm text-gray-600 dark:text-gray-400">Automatically save changes as you work</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer touch-manipulation">
              <input
                type="checkbox"
                checked={autoSave}
                onChange={(e) => handleAutoSaveChange(e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 dark:bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
            </label>
          </div>
        </div>
      </div>
    </div>
  );
}

// Security tab content
function SecurityTab() {
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [showTwoFactorModal, setShowTwoFactorModal] = useState(false);
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(true);
  const [sessions, setSessions] = useState([
    { id: 1, device: "Chrome on Windows", location: "New York, NY", time: "2 hours ago", current: true },
    { id: 2, device: "Safari on iPhone", location: "New York, NY", time: "1 day ago", current: false },
    { id: 3, device: "Chrome on Windows", location: "New York, NY", time: "3 days ago", current: false }
  ]);

  const handleRevokeSession = (sessionId: number) => {
    setSessions(sessions.filter(session => session.id !== sessionId));
    toast.success("Session revoked successfully");
  };

  const handleToggleTwoFactor = () => {
    setTwoFactorEnabled(!twoFactorEnabled);
    setShowTwoFactorModal(true);
  };

  return (
    <div className="space-y-4 md:space-y-6">
      <div>
        <h3 className="text-base md:text-lg font-semibold text-charcoal dark:text-gray-100 mb-3 md:mb-4">Password & Security</h3>
        <div className="space-y-3 md:space-y-4">
          <button 
            onClick={() => setShowPasswordModal(true)}
            className="w-full flex items-center justify-between p-3 md:p-4 bg-neutral dark:bg-gray-700 rounded-lg border border-gray-100 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors touch-manipulation"
          >
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 md:w-10 md:h-10 bg-primary/10 dark:bg-primary/20 rounded-full flex items-center justify-center">
                <span className="text-sm md:text-base">🔑</span>
              </div>
              <div className="text-left">
                <h4 className="font-medium text-charcoal dark:text-gray-100 text-sm md:text-base">Change Password</h4>
                <p className="text-xs md:text-sm text-gray-600 dark:text-gray-400">Update your account password</p>
              </div>
            </div>
            <span className="text-gray-400">→</span>
          </button>

          <button 
            onClick={handleToggleTwoFactor}
            className="w-full flex items-center justify-between p-3 md:p-4 bg-neutral dark:bg-gray-700 rounded-lg border border-gray-100 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors touch-manipulation"
          >
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 md:w-10 md:h-10 bg-accent/10 dark:bg-accent/20 rounded-full flex items-center justify-center">
                <span className="text-sm md:text-base">📱</span>
              </div>
              <div className="text-left">
                <h4 className="font-medium text-charcoal dark:text-gray-100 text-sm md:text-base">Two-Factor Authentication</h4>
                <p className="text-xs md:text-sm text-gray-600 dark:text-gray-400">Add an extra layer of security</p>
              </div>
            </div>
            <span className={`status-badge ${twoFactorEnabled ? 'status-active' : 'status-inactive'}`}>
              {twoFactorEnabled ? 'Enabled' : 'Disabled'}
            </span>
          </button>
        </div>
      </div>

      <div>
        <h3 className="text-base md:text-lg font-semibold text-charcoal dark:text-gray-100 mb-3 md:mb-4">Login Activity</h3>
        <div className="space-y-3">
          {sessions.map((session) => (
            <div key={session.id} className="flex items-center justify-between p-3 md:p-4 bg-neutral dark:bg-gray-700 rounded-lg border border-gray-100 dark:border-gray-600">
              <div className="flex items-center gap-3 flex-1 min-w-0">
                <div className="w-8 h-8 md:w-10 md:h-10 bg-primary/10 dark:bg-primary/20 rounded-full flex items-center justify-center flex-shrink-0">
                  <span className="text-sm md:text-base">💻</span>
                </div>
                <div className="min-w-0 flex-1">
                  <h4 className="font-medium text-charcoal dark:text-gray-100 text-sm md:text-base truncate">{session.device}</h4>
                  <p className="text-xs md:text-sm text-gray-600 dark:text-gray-400 truncate">{session.location} • {session.time}</p>
                </div>
              </div>
              {session.current ? (
                <span className="status-badge status-active flex-shrink-0">Current</span>
              ) : (
                <button 
                  onClick={() => handleRevokeSession(session.id)}
                  className="text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 text-xs md:text-sm flex-shrink-0 touch-manipulation"
                >
                  Revoke
                </button>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Password Change Modal */}
      {showPasswordModal && (
        <PasswordChangeModal onClose={() => setShowPasswordModal(false)} />
      )}

      {/* Two-Factor Authentication Modal */}
      {showTwoFactorModal && (
        <TwoFactorModal 
          enabled={twoFactorEnabled}
          onClose={() => setShowTwoFactorModal(false)}
          onToggle={(enabled) => {
            setTwoFactorEnabled(enabled);
            toast.success(`Two-factor authentication ${enabled ? 'enabled' : 'disabled'}`);
          }}
        />
      )}
    </div>
  );
}

// Admin tab content (only visible to admins)
function AdminTab() {
  return (
    <div className="space-y-4 md:space-y-6">
      <div>
        <h3 className="text-base md:text-lg font-semibold text-charcoal dark:text-gray-100 mb-3 md:mb-4">User Management</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4">
          <AdminCard
            title="Manage Users"
            description="Add, edit, or remove user accounts"
            icon="👥"
            action="Manage"
          />
          <AdminCard
            title="Role Permissions"
            description="Configure role-based access controls"
            icon="🔐"
            action="Configure"
          />
        </div>
      </div>

      <div>
        <h3 className="text-base md:text-lg font-semibold text-charcoal dark:text-gray-100 mb-3 md:mb-4">System Settings</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4">
          <AdminCard
            title="System Logs"
            description="View application logs and activity"
            icon="📋"
            action="View Logs"
          />
          <AdminCard
            title="Backup & Restore"
            description="Manage data backups and restoration"
            icon="💾"
            action="Manage"
          />
        </div>
      </div>
    </div>
  );
}

// Metric card component
function MetricCard({ title, value, icon, trend, trendUp }: {
  title: string;
  value: string | number;
  icon: string;
  trend: string;
  trendUp: boolean;
}) {
  return (
    <div className="p-3 md:p-4 bg-neutral dark:bg-gray-700 rounded-lg border border-gray-100 dark:border-gray-600">
      <div className="flex items-center justify-between mb-2">
        <span className="text-lg md:text-2xl">{icon}</span>
        <span className={`text-xs md:text-sm font-medium ${trendUp ? "text-green-600 dark:text-green-400" : "text-red-600 dark:text-red-400"}`}>
          {trend}
        </span>
      </div>
      <div className="text-lg md:text-2xl font-bold text-charcoal dark:text-gray-100 mb-1">{value}</div>
      <div className="text-xs md:text-sm text-gray-600 dark:text-gray-400">{title}</div>
    </div>
  );
}

// Admin card component
function AdminCard({ title, description, icon, action }: {
  title: string;
  description: string;
  icon: string;
  action: string;
}) {
  return (
    <div className="p-4 md:p-6 bg-neutral dark:bg-gray-700 rounded-lg border border-gray-100 dark:border-gray-600 hover:shadow-md dark:hover:shadow-lg transition-shadow touch-manipulation">
      <div className="flex items-start gap-3 md:gap-4">
        <div className="w-10 h-10 md:w-12 md:h-12 bg-primary/10 dark:bg-primary/20 rounded-lg flex items-center justify-center flex-shrink-0">
          <span className="text-lg md:text-2xl">{icon}</span>
        </div>
        <div className="flex-1 min-w-0">
          <h4 className="font-semibold text-charcoal dark:text-gray-100 mb-1 text-sm md:text-base">{title}</h4>
          <p className="text-xs md:text-sm text-gray-600 dark:text-gray-400 mb-2 md:mb-3">{description}</p>
          <button className="text-primary hover:text-primary/80 text-xs md:text-sm font-medium">
            {action} →
          </button>
        </div>
      </div>
    </div>
  );
}

// Edit profile modal
function EditProfileModal({ user, userProfile, onClose, onSuccess }: {
  user: any;
  userProfile: any;
  onClose: () => void;
  onSuccess: () => void;
}) {
  const updateProfile = useMutation(api.users.updateProfile);
  const [formData, setFormData] = useState({
    name: user.name || "",
    phone: userProfile.phone || "",
    bio: userProfile.bio || "",
    department: userProfile.department || "",
    role: userProfile.role || "",
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await updateProfile(formData);
      onSuccess();
    } catch (error) {
      toast.error("Failed to update profile");
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-4 md:p-6 w-full max-w-md max-h-[95vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4 md:mb-6">
          <h2 className="text-lg md:text-xl font-semibold text-charcoal dark:text-gray-100">Edit Profile</h2>
          <button
            onClick={onClose}
            className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 text-xl p-2 touch-manipulation"
          >
            ✕
          </button>
        </div>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="form-label">Full Name</label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="form-input"
            />
          </div>

          <div>
            <label className="form-label">Phone Number</label>
            <input
              type="tel"
              value={formData.phone}
              onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
              className="form-input"
            />
          </div>

          <div>
            <label className="form-label">Role</label>
            <select
              value={formData.role}
              onChange={(e) => setFormData({ ...formData, role: e.target.value })}
              className="form-input"
            >
              <option value="">Select Role</option>
              <option value="admin">Administrator</option>
              <option value="staff">Staff Member</option>
            </select>
          </div>

          <div>
            <label className="form-label">Department</label>
            <select
              value={formData.department}
              onChange={(e) => setFormData({ ...formData, department: e.target.value })}
              className="form-input"
            >
              <option value="">Select Department</option>
              <option value="installation">Installation</option>
              <option value="maintenance">Maintenance</option>
              <option value="sales">Sales</option>
              <option value="management">Management</option>
            </select>
          </div>

          <div>
            <label className="form-label">Bio</label>
            <textarea
              value={formData.bio}
              onChange={(e) => { setFormData({ ...formData, bio: e.target.value }); }}
              rows={3}
              className="form-input"
              placeholder="Tell us about yourself..."
            />
          </div>

          <div className="flex flex-col md:flex-row gap-3 pt-4">
            <button
              type="submit"
              className="btn-primary flex-1 touch-manipulation"
            >
              Save Changes
            </button>
            <button
              type="button"
              onClick={onClose}
              className="btn-secondary flex-1 touch-manipulation"
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

// Password change modal
function PasswordChangeModal({ onClose }: { onClose: () => void }) {
  const [formData, setFormData] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (formData.newPassword !== formData.confirmPassword) {
      toast.error("New passwords don't match");
      return;
    }

    if (formData.newPassword.length < 8) {
      toast.error("Password must be at least 8 characters long");
      return;
    }

    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success("Password updated successfully");
      onClose();
    } catch (error) {
      toast.error("Failed to update password");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-4 md:p-6 w-full max-w-md">
        <div className="flex justify-between items-center mb-4 md:mb-6">
          <h2 className="text-lg md:text-xl font-semibold text-charcoal dark:text-gray-100">Change Password</h2>
          <button
            onClick={onClose}
            className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 text-xl p-2 touch-manipulation"
          >
            ✕
          </button>
        </div>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="form-label">Current Password</label>
            <input
              type="password"
              value={formData.currentPassword}
              onChange={(e) => setFormData({ ...formData, currentPassword: e.target.value })}
              className="form-input"
              required
            />
          </div>

          <div>
            <label className="form-label">New Password</label>
            <input
              type="password"
              value={formData.newPassword}
              onChange={(e) => setFormData({ ...formData, newPassword: e.target.value })}
              className="form-input"
              required
              minLength={8}
            />
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Must be at least 8 characters long
            </p>
          </div>

          <div>
            <label className="form-label">Confirm New Password</label>
            <input
              type="password"
              value={formData.confirmPassword}
              onChange={(e) => setFormData({ ...formData, confirmPassword: e.target.value })}
              className="form-input"
              required
            />
          </div>

          <div className="flex flex-col md:flex-row gap-3 pt-4">
            <button
              type="submit"
              disabled={isLoading}
              className="btn-primary flex-1 touch-manipulation disabled:opacity-50"
            >
              {isLoading ? "Updating..." : "Update Password"}
            </button>
            <button
              type="button"
              onClick={onClose}
              className="btn-secondary flex-1 touch-manipulation"
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

// Two-factor authentication modal
function TwoFactorModal({ enabled, onClose, onToggle }: {
  enabled: boolean;
  onClose: () => void;
  onToggle: (enabled: boolean) => void;
}) {
  const [step, setStep] = useState(1);
  const [qrCode, setQrCode] = useState("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iI2Y5ZmFmYiIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBkb21pbmFudC1iYXNlbGluZT0ibWlkZGxlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmb250LWZhbWlseT0ibW9ub3NwYWNlIiBmb250LXNpemU9IjE0cHgiIGZpbGw9IiM2YjcyODAiPlFSIENvZGU8L3RleHQ+PC9zdmc+");
  const [verificationCode, setVerificationCode] = useState("");

  const handleToggle = () => {
    if (enabled) {
      // Disable 2FA
      onToggle(false);
      onClose();
    } else {
      // Enable 2FA - show setup process
      setStep(1);
    }
  };

  const handleVerify = () => {
    if (verificationCode.length === 6) {
      onToggle(true);
      onClose();
    } else {
      toast.error("Please enter a valid 6-digit code");
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-4 md:p-6 w-full max-w-md">
        <div className="flex justify-between items-center mb-4 md:mb-6">
          <h2 className="text-lg md:text-xl font-semibold text-charcoal dark:text-gray-100">
            {enabled ? "Disable" : "Enable"} Two-Factor Authentication
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 text-xl p-2 touch-manipulation"
          >
            ✕
          </button>
        </div>
        
        {enabled ? (
          <div className="space-y-4">
            <p className="text-gray-600 dark:text-gray-400">
              Are you sure you want to disable two-factor authentication? This will make your account less secure.
            </p>
            <div className="flex flex-col md:flex-row gap-3">
              <button
                onClick={handleToggle}
                className="btn-danger flex-1 touch-manipulation"
              >
                Disable 2FA
              </button>
              <button
                onClick={onClose}
                className="btn-secondary flex-1 touch-manipulation"
              >
                Cancel
              </button>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {step === 1 && (
              <>
                <div className="text-center">
                  <img src={qrCode} alt="QR Code" className="mx-auto mb-4 rounded-lg" />
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                    Scan this QR code with your authenticator app
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400 font-mono bg-gray-100 dark:bg-gray-700 p-2 rounded">
                    JBSWY3DPEHPK3PXP
                  </p>
                </div>
                <button
                  onClick={() => setStep(2)}
                  className="btn-primary w-full touch-manipulation"
                >
                  I've Added the Account
                </button>
              </>
            )}
            
            {step === 2 && (
              <>
                <div>
                  <label className="form-label">Enter Verification Code</label>
                  <input
                    type="text"
                    value={verificationCode}
                    onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                    className="form-input text-center text-lg tracking-widest"
                    placeholder="000000"
                    maxLength={6}
                  />
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Enter the 6-digit code from your authenticator app
                  </p>
                </div>
                <div className="flex flex-col md:flex-row gap-3">
                  <button
                    onClick={handleVerify}
                    disabled={verificationCode.length !== 6}
                    className="btn-primary flex-1 touch-manipulation disabled:opacity-50"
                  >
                    Verify & Enable
                  </button>
                  <button
                    onClick={() => setStep(1)}
                    className="btn-secondary flex-1 touch-manipulation"
                  >
                    Back
                  </button>
                </div>
              </>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

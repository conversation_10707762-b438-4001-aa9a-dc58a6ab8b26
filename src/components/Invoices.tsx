import { useState, useEffect } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { toast } from "sonner";
import { DynamicTable } from "./common/DynamicTable/DynamicTable";
import { StatusDropdown } from "./common/StatusDropdown";
import { InvoicePreview, InvoiceForm } from "./invoices";




// Invoice management component with CRUD operations
export function Invoices({ selectedItemId }: { selectedItemId?: string | null }) {
  const invoices = useQuery(api.invoices.list);
  const loggedInUser = useQuery(api.auth.loggedInUser);
  const deleteInvoice = useMutation(api.invoices.remove);
  const updateInvoiceStatus = useMutation(api.invoices.updateStatus);
  const checkOverdueInvoices = useMutation(api.invoices.checkOverdueInvoices);
  const [selectedInvoice, setSelectedInvoice] = useState<any>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [isCheckingOverdue, setIsCheckingOverdue] = useState(false);

  // Auto-select invoice when selectedItemId is provided from search navigation
  useEffect(() => {
    if (selectedItemId && invoices) {
      const invoiceToSelect = invoices.find(invoice => invoice._id === selectedItemId);
      if (invoiceToSelect) {
        setSelectedInvoice(invoiceToSelect);
        setIsEditing(false); // Show detail view, not edit form
      }
    }
  }, [selectedItemId, invoices]);

  // Clear selection if the selected invoice is no longer in the list (e.g., deleted)
  useEffect(() => {
    if (selectedInvoice && invoices) {
      const stillExists = invoices.find(invoice => invoice._id === selectedInvoice._id);
      if (!stillExists) {
        setSelectedInvoice(null);
        setIsEditing(false);
      }
    }
  }, [selectedInvoice, invoices]);

  // Handle row click in the table
  const handleRowClick = (invoice: any) => {
    setSelectedInvoice(invoice);
  };

  // Handle adding a new invoice
  const handleAdd = () => {
    setSelectedInvoice(null);
    setIsEditing(true);
  };

  // Handle checking for overdue invoices
  const handleCheckOverdue = async () => {
    setIsCheckingOverdue(true);
    try {
      const result = await checkOverdueInvoices({});
      if (result.updatedCount > 0) {
        toast.success(`Updated ${result.updatedCount} overdue invoice(s)`);
      } else {
        toast.info("No overdue invoices found");
      }
    } catch (error: any) {
      console.error("Error checking overdue invoices:", error);
      toast.error("Failed to check overdue invoices");
    } finally {
      setIsCheckingOverdue(false);
    }
  };

  // Function to handle invoice deletion
  const handleRowDeletion = async (invoice: any) => {
    try {
      await deleteInvoice({ id: invoice._id });
      toast.success(`Invoice #${invoice.invoiceNumber} has been deleted`);
      // If the deleted invoice was selected, clear the selection
      if (selectedInvoice && selectedInvoice._id === invoice._id) {
        setSelectedInvoice(null);
        setIsEditing(false);
      }
    } catch (error) {
      toast.error("Failed to delete invoice");
      console.error("Delete error:", error);
    }
  };
  
  // Define table columns - include status with defaultVisible set to true
  const columns = [
    {
      id: "invoiceNumber",
      header: "Invoice #",
      accessor: (row: any) => (
        <div className="text-sm font-medium text-charcoal">{row.invoiceNumber}</div>
      ),
      sortable: true,
      filterable: true,
      getFilterValue: (row: any) => row.invoiceNumber,
      defaultVisible: true
    },
    {
      id: "customer",
      header: "Customer",
      accessor: (row: any) => (
        <div className="text-sm">
          <div className="text-charcoal font-medium">{row.customer?.name || "N/A"}</div>
          {row.customer?.email && (
            <div className="text-gray-500 text-xs">{row.customer.email}</div>
          )}
          {row.customer?.phone && (
            <div className="text-gray-500 text-xs">{row.customer.phone}</div>
          )}
          {row.customer?.address && (
            <div className="text-gray-500 text-xs">{row.customer.address}</div>
          )}
        </div>
      ),
      sortable: true,
      filterable: true,
      getFilterValue: (row: any) => row.customer?.name,
      defaultVisible: true
    },
    {
      id: "issueDate",
      header: "Date",
      accessor: (row: any) => (
        <div className="text-sm text-charcoal">{new Date(row.issueDate).toLocaleDateString()}</div>
      ),
      sortable: true,
      filterable: true,
      getFilterValue: (row: any) => row.issueDate,
      defaultVisible: true
    },
    {
      id: "status",
      header: "Status",
      accessor: (row: any) => (
        <StatusDropdown
          currentStatus={row.status}
          onStatusChange={async (newStatus: string) => {
            try {
              await updateInvoiceStatus({ id: row._id, status: newStatus });
              toast.success(`Invoice status updated to '${newStatus}'`);
            } catch (error: any) {
              toast.error(error.message || "Failed to update status");
              throw error;
            }
          }}
          size="sm"
        />
      ),
      sortable: true,
      filterable: true,
      getFilterValue: (row: any) => row.status,
      defaultVisible: true // This is key to make it visible by default
    },
    {
      id: "total",
      header: "Total",
      accessor: (row: any) => (
        <div className="text-sm font-medium text-charcoal">${row.total.toFixed(2)}</div>
      ),
      sortable: true,
      filterable: true,
      getFilterValue: (row: any) => row.total.toString(),
      defaultVisible: true
    },
    {
      id: "pdfStatus",
      header: "PDF",
      accessor: (row: any) => (
        <div className="flex items-center justify-center">
          {row.pdfStorageId ? (
            <span
              className="text-green-600 dark:text-green-400 text-lg"
              title="PDF available"
            >
              📄
            </span>
          ) : (
            <span
              className="text-gray-400 dark:text-gray-600 text-lg"
              title="No PDF generated"
            >
              📝
            </span>
          )}
        </div>
      ),
      sortable: true,
      filterable: true,
      getFilterValue: (row: any) => row.pdfStorageId ? "available" : "not generated",
      defaultVisible: true
    }
  ];

  if (!invoices || !loggedInUser) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-[1fr_400px] gap-6 h-full">
      {/* Left Column - Invoices List */}
      <div className="space-y-4">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold text-charcoal">Invoices</h1>
            <p className="text-gray-600 text-sm md:text-base">Manage your HVAC invoices</p>
          </div>
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-4">
            <button
              onClick={handleAdd}
              className="btn-primary w-full sm:w-auto touch-manipulation"
            >
              <span className="mr-2">➕</span>
              Add Invoice
            </button>
            <button
              onClick={handleCheckOverdue}
              disabled={isCheckingOverdue}
              className="btn-secondary w-full sm:w-auto touch-manipulation"
            >
              <span className="mr-2">{isCheckingOverdue ? "⏳" : "⚠️"}</span>
              {isCheckingOverdue ? "Checking..." : "Check Overdue"}
            </button>
          </div>
        </div>
        
        {/* Dynamic Invoices Table - Used for both desktop and mobile */}
        <DynamicTable
          data={invoices}
          columns={columns}
          rowKey="_id"
          tableId="invoices-table"
          searchable
          filterable
          reorderable
          pageSize={10}
          onRowClick={handleRowClick}
          selectedId={selectedInvoice?._id}
          onDelete={handleRowDeletion}
          showDeleteButton={true}
          deleteConfirmMessage="Are you sure you want to delete this invoice?"
        />
      </div>

      {/* Right Column - Preview/Edit Panel */}
      <div className="card h-full flex flex-col overflow-hidden">
        <div className="flex-1 overflow-hidden">
          {isEditing ? (
            <InvoiceForm
              invoice={selectedInvoice}
              currentUserId={loggedInUser._id}
              onClose={() => {
                setIsEditing(false);
              }}
              onSuccess={() => {
                setSelectedInvoice(null);
                setIsEditing(false);
              }}
            />
          ) : selectedInvoice ? (
            <div className="h-full p-6">
              <InvoicePreview invoice={selectedInvoice} onEdit={() => setIsEditing(true)} />
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-full text-center p-6">
              <div className="max-w-sm">
                <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                  <span className="text-2xl">📄</span>
                </div>
                <h3 className="text-xl font-semibold text-charcoal dark:text-white mb-2">
                  Select an invoice to view details
                </h3>
                <p className="text-gray-600 dark:text-gray-400 text-sm">
                  Click on an invoice in the list to see more information or edit it
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
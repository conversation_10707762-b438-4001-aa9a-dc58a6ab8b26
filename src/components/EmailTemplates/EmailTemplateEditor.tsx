import React, { useState, useEffect } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { toast } from "sonner";

interface EmailTemplateEditorProps {
  template?: any;
  onClose: () => void;
  onSuccess: () => void;
}

export function EmailTemplateEditor({ template, onClose, onSuccess }: EmailTemplateEditorProps) {
  const [formData, setFormData] = useState({
    name: "",
    subject: "",
    content: "",
    category: "invoice",
    description: "",
  });
  const [loading, setLoading] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState("invoice");

  const createTemplate = useMutation(api.emailTemplates.create);
  const updateTemplate = useMutation(api.emailTemplates.update);
  const availableVariables = useQuery(api.emailTemplates.getAvailableVariables, { 
    category: selectedCategory 
  });

  const categories = [
    { value: "invoice", label: "Invoice Emails" },
    { value: "notification", label: "Notifications" },
    { value: "reminder", label: "Reminders" },
    { value: "general", label: "General" },
  ];

  useEffect(() => {
    if (template) {
      setFormData({
        name: template.name || "",
        subject: template.subject || "",
        content: template.content || "",
        category: template.category || "invoice",
        description: template.description || "",
      });
      setSelectedCategory(template.category || "invoice");
    }
  }, [template]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      if (template) {
        await updateTemplate({
          id: template._id,
          ...formData,
        });
        toast.success("Email template updated successfully");
      } else {
        await createTemplate(formData);
        toast.success("Email template created successfully");
      }
      onSuccess();
    } catch (error: any) {
      toast.error(error.message || "Failed to save email template");
    } finally {
      setLoading(false);
    }
  };

  const insertVariable = (variable: string) => {
    const textarea = document.getElementById("template-content") as HTMLTextAreaElement;
    if (textarea) {
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const newContent = 
        formData.content.substring(0, start) + 
        variable + 
        formData.content.substring(end);
      
      setFormData({ ...formData, content: newContent });
      
      // Set cursor position after the inserted variable
      setTimeout(() => {
        textarea.focus();
        textarea.setSelectionRange(start + variable.length, start + variable.length);
      }, 0);
    }
  };

  const insertVariableInSubject = (variable: string) => {
    const input = document.getElementById("template-subject") as HTMLInputElement;
    if (input) {
      const start = input.selectionStart || 0;
      const end = input.selectionEnd || 0;
      const newSubject = 
        formData.subject.substring(0, start) + 
        variable + 
        formData.subject.substring(end);
      
      setFormData({ ...formData, subject: newSubject });
      
      // Set cursor position after the inserted variable
      setTimeout(() => {
        input.focus();
        input.setSelectionRange(start + variable.length, start + variable.length);
      }, 0);
    }
  };

  const generatePreview = () => {
    let preview = formData.content;
    const sampleData: Record<string, string> = {
      "{customerName}": "John Doe",
      "{customerEmail}": "<EMAIL>",
      "{customerPhone}": "(*************",
      "{customerAddress}": "123 Main St",
      "{customerCity}": "Anytown",
      "{customerState}": "CA",
      "{customerZipCode}": "12345",
      "{customerCompany}": "Acme Corp",
      "{invoiceNumber}": "INV-001",
      "{invoiceDate}": "January 15, 2024",
      "{dueDate}": "February 14, 2024",
      "{subtotal}": "$1,000.00",
      "{taxAmount}": "$80.00",
      "{total}": "$1,080.00",
      "{companyName}": "HVAC CRM",
      "{companyEmail}": "<EMAIL>",
      "{companyPhone}": "(*************",
      "{companyAddress}": "456 Business Ave",
      "{paymentTerms}": "Net 30",
      "{invoiceUrl}": "#",
      "{notes}": "Thank you for your business!",
      "{message}": "This is a sample notification message.",
      "{date}": "January 15, 2024",
      "{daysPastDue}": "5"
    };

    Object.entries(sampleData).forEach(([variable, value]) => {
      preview = preview.replace(new RegExp(variable.replace(/[{}]/g, '\\$&'), 'g'), value);
    });

    return preview;
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] overflow-hidden">
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-charcoal dark:text-gray-100">
            {template ? "Edit Email Template" : "Create Email Template"}
          </h2>
          <div className="flex items-center gap-2">
            <button
              type="button"
              onClick={() => setShowPreview(!showPreview)}
              className="px-4 py-2 text-sm bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded-lg hover:bg-blue-200 dark:hover:bg-blue-800"
            >
              {showPreview ? "Hide Preview" : "Show Preview"}
            </button>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              ✕
            </button>
          </div>
        </div>

        <div className="flex h-[calc(90vh-120px)]">
          {/* Form Section */}
          <div className={`${showPreview ? 'w-1/2' : 'w-full'} p-6 overflow-y-auto border-r border-gray-200 dark:border-gray-700`}>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Basic Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-charcoal dark:text-gray-100 mb-2">
                    Template Name *
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-gray-100"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-charcoal dark:text-gray-100 mb-2">
                    Category *
                  </label>
                  <select
                    value={formData.category}
                    onChange={(e) => {
                      setFormData({ ...formData, category: e.target.value });
                      setSelectedCategory(e.target.value);
                    }}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-gray-100"
                    required
                  >
                    {categories.map((category) => (
                      <option key={category.value} value={category.value}>
                        {category.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-charcoal dark:text-gray-100 mb-2">
                  Description
                </label>
                <input
                  type="text"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-gray-100"
                  placeholder="Brief description of this template"
                />
              </div>

              {/* Subject Line */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-charcoal dark:text-gray-100">
                    Subject Line *
                  </label>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    Click variables to insert
                  </div>
                </div>
                <input
                  id="template-subject"
                  type="text"
                  value={formData.subject}
                  onChange={(e) => setFormData({ ...formData, subject: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-gray-100"
                  placeholder="Enter email subject line..."
                  required
                />
                {/* Subject Variables */}
                <div className="mt-2 flex flex-wrap gap-1">
                  {availableVariables?.variables?.slice(0, 8).map((variable: string) => (
                    <button
                      key={variable}
                      type="button"
                      onClick={() => insertVariableInSubject(variable)}
                      className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600"
                    >
                      {variable}
                    </button>
                  ))}
                </div>
              </div>

              {/* Content */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-charcoal dark:text-gray-100">
                    Email Content (HTML) *
                  </label>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    Click variables to insert at cursor
                  </div>
                </div>
                <textarea
                  id="template-content"
                  value={formData.content}
                  onChange={(e) => setFormData({ ...formData, content: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-gray-100 font-mono text-sm"
                  rows={12}
                  placeholder="Enter HTML email content..."
                  required
                />
                
                {/* Available Variables */}
                <div className="mt-3">
                  <div className="text-sm font-medium text-charcoal dark:text-gray-100 mb-2">
                    Available Variables:
                  </div>
                  <div className="flex flex-wrap gap-1 max-h-24 overflow-y-auto">
                    {availableVariables?.variables?.map((variable: string) => (
                      <button
                        key={variable}
                        type="button"
                        onClick={() => insertVariable(variable)}
                        className="px-2 py-1 text-xs bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded hover:bg-blue-200 dark:hover:bg-blue-800"
                      >
                        {variable}
                      </button>
                    ))}
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="flex justify-end gap-3 pt-4 border-t border-gray-200 dark:border-gray-700">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  className="btn-primary"
                >
                  {loading ? "Saving..." : template ? "Update Template" : "Create Template"}
                </button>
              </div>
            </form>
          </div>

          {/* Preview Section */}
          {showPreview && (
            <div className="w-1/2 p-6 overflow-y-auto bg-gray-50 dark:bg-gray-900">
              <h3 className="text-lg font-medium text-charcoal dark:text-gray-100 mb-4">
                Preview
              </h3>
              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                <div className="mb-4 pb-4 border-b border-gray-200 dark:border-gray-700">
                  <div className="text-sm text-gray-600 dark:text-gray-400 mb-1">Subject:</div>
                  <div className="font-medium text-charcoal dark:text-gray-100">
                    {formData.subject.replace(/\{(\w+)\}/g, (match, key) => {
                      const sampleData: Record<string, string> = {
                        customerName: "John Doe",
                        invoiceNumber: "INV-001",
                        companyName: "HVAC CRM"
                      };
                      return sampleData[key] || match;
                    })}
                  </div>
                </div>
                <div 
                  className="prose prose-sm max-w-none dark:prose-invert"
                  dangerouslySetInnerHTML={{ __html: generatePreview() }}
                />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

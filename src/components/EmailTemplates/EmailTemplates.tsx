import React, { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { toast } from "sonner";
import { EmailTemplateEditor } from "./EmailTemplateEditor";

export function EmailTemplates() {
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<any>(null);
  const [showPreview, setShowPreview] = useState<any>(null);

  const templates = useQuery(api.emailTemplates.list, {
    category: selectedCategory === "all" ? undefined : selectedCategory,
  });
  const removeTemplate = useMutation(api.emailTemplates.remove);
  const updateTemplate = useMutation(api.emailTemplates.update);
  const initializeDefaults = useMutation(api.emailTemplates.initializeDefaults);

  const categories = [
    { value: "all", label: "All Templates" },
    { value: "invoice", label: "Invoice Emails" },
    { value: "notification", label: "Notifications" },
    { value: "reminder", label: "Reminders" },
    { value: "general", label: "General" },
  ];

  const handleEdit = (template: any) => {
    setEditingTemplate(template);
    setShowCreateModal(true);
  };

  const handleCreate = () => {
    setEditingTemplate(null);
    setShowCreateModal(true);
  };

  const handleDelete = async (template: any) => {
    if (template.isDefault) {
      toast.error("Cannot delete default templates");
      return;
    }

    if (confirm(`Are you sure you want to delete "${template.name}"?`)) {
      try {
        await removeTemplate({ id: template._id });
        toast.success("Template deleted successfully");
      } catch (error: any) {
        toast.error(error.message || "Failed to delete template");
      }
    }
  };

  const handleToggleActive = async (template: any) => {
    try {
      await updateTemplate({
        id: template._id,
        isActive: !template.isActive,
      });
      toast.success(`Template ${template.isActive ? 'deactivated' : 'activated'} successfully`);
    } catch (error: any) {
      toast.error(error.message || "Failed to update template");
    }
  };

  const handleInitializeDefaults = async () => {
    try {
      const result = await initializeDefaults({});
      toast.success(result.message);
    } catch (error: any) {
      toast.error(error.message || "Failed to initialize default templates");
    }
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getCategoryBadgeColor = (category: string) => {
    const colors = {
      invoice: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
      notification: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
      reminder: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
      general: "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300",
    };
    return colors[category as keyof typeof colors] || colors.general;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold text-charcoal dark:text-gray-100">
            Email Templates
          </h2>
          <p className="text-gray-600 dark:text-gray-400 text-sm">
            Manage customizable email templates for your communications
          </p>
        </div>
        <div className="flex gap-2">
          <button
            onClick={handleInitializeDefaults}
            className="px-4 py-2 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600"
          >
            Initialize Defaults
          </button>
          <button onClick={handleCreate} className="btn-primary">
            Create Template
          </button>
        </div>
      </div>

      {/* Category Filter */}
      <div className="flex flex-wrap gap-2">
        {categories.map((category) => (
          <button
            key={category.value}
            onClick={() => setSelectedCategory(category.value)}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              selectedCategory === category.value
                ? "bg-primary text-white"
                : "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
            }`}
          >
            {category.label}
          </button>
        ))}
      </div>

      {/* Templates List */}
      <div className="space-y-4">
        {templates === undefined ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : templates.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-400 dark:text-gray-500 text-lg mb-2">📧</div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              No email templates found
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              {selectedCategory === "all" 
                ? "Create your first email template to get started"
                : `No templates found in the ${categories.find(c => c.value === selectedCategory)?.label} category`
              }
            </p>
            <button onClick={handleCreate} className="btn-primary">
              Create Template
            </button>
          </div>
        ) : (
          templates.map((template) => (
            <div
              key={template._id}
              className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-3 mb-2">
                    <h3 className="text-lg font-medium text-charcoal dark:text-gray-100 truncate">
                      {template.name}
                    </h3>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getCategoryBadgeColor(template.category)}`}>
                      {template.category}
                    </span>
                    {template.isDefault && (
                      <span className="px-2 py-1 text-xs font-medium rounded-full bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300">
                        Default
                      </span>
                    )}
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      template.isActive 
                        ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
                        : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
                    }`}>
                      {template.isActive ? "Active" : "Inactive"}
                    </span>
                  </div>
                  
                  <p className="text-gray-600 dark:text-gray-400 text-sm mb-2 line-clamp-2">
                    <strong>Subject:</strong> {template.subject}
                  </p>
                  
                  {template.description && (
                    <p className="text-gray-600 dark:text-gray-400 text-sm mb-3">
                      {template.description}
                    </p>
                  )}
                  
                  <div className="flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400">
                    <span>Updated: {formatDate(template.updatedAt)}</span>
                    <span>{template.variables?.length || 0} variables</span>
                  </div>
                </div>
                
                <div className="flex items-center gap-2 ml-4">
                  <button
                    onClick={() => setShowPreview(template)}
                    className="px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600"
                  >
                    Preview
                  </button>
                  <button
                    onClick={() => handleToggleActive(template)}
                    className={`px-3 py-1 text-sm rounded ${
                      template.isActive
                        ? "bg-red-100 text-red-700 hover:bg-red-200 dark:bg-red-900 dark:text-red-300 dark:hover:bg-red-800"
                        : "bg-green-100 text-green-700 hover:bg-green-200 dark:bg-green-900 dark:text-green-300 dark:hover:bg-green-800"
                    }`}
                  >
                    {template.isActive ? "Deactivate" : "Activate"}
                  </button>
                  <button
                    onClick={() => handleEdit(template)}
                    className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-300 dark:hover:bg-blue-800"
                  >
                    Edit
                  </button>
                  {!template.isDefault && (
                    <button
                      onClick={() => handleDelete(template)}
                      className="px-3 py-1 text-sm bg-red-100 text-red-700 rounded hover:bg-red-200 dark:bg-red-900 dark:text-red-300 dark:hover:bg-red-800"
                    >
                      Delete
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Create/Edit Modal */}
      {showCreateModal && (
        <EmailTemplateEditor
          template={editingTemplate}
          onClose={() => {
            setShowCreateModal(false);
            setEditingTemplate(null);
          }}
          onSuccess={() => {
            setShowCreateModal(false);
            setEditingTemplate(null);
          }}
        />
      )}

      {/* Preview Modal */}
      {showPreview && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <h2 className="text-xl font-semibold text-charcoal dark:text-gray-100">
                Preview: {showPreview.name}
              </h2>
              <button
                onClick={() => setShowPreview(null)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                ✕
              </button>
            </div>
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
              <div className="mb-4 pb-4 border-b border-gray-200 dark:border-gray-700">
                <div className="text-sm text-gray-600 dark:text-gray-400 mb-1">Subject:</div>
                <div className="font-medium text-charcoal dark:text-gray-100">
                  {showPreview.subject}
                </div>
              </div>
              <div 
                className="prose prose-sm max-w-none dark:prose-invert"
                dangerouslySetInnerHTML={{ __html: showPreview.content }}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

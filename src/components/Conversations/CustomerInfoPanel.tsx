import React, { useState } from "react";
import { useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";

interface CustomerInfoPanelProps {
  customerId: string;
}

export function CustomerInfoPanel({ customerId }: CustomerInfoPanelProps) {
  const [activeTab, setActiveTab] = useState("details");

  const customer = useQuery(api.customers.get, { id: customerId });
  const customerJobs = useQuery(api.jobs.getByCustomer, { customerId });
  const customerInvoices = useQuery(api.invoices.getByCustomer, { customerId });
  const smsHistory = useQuery(api.sms.getCustomerHistory, { customerId });

  if (!customer) {
    return (
      <div className="p-4 flex items-center justify-center h-32">
        <div className="text-center">
          <div className="text-2xl mb-2">⏳</div>
          <p className="text-sm text-gray-500">Loading customer info...</p>
        </div>
      </div>
    );
  }

  const tabs = [
    { id: "details", label: "Details", icon: "👤" },
    { id: "activity", label: "Activity", icon: "📋" },
    { id: "jobs", label: "Jobs", icon: "🔧" },
    { id: "invoices", label: "Invoices", icon: "💰" },
  ];

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString([], {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const getJobStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'in-progress': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'scheduled': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'cancelled': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getInvoiceStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'sent': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'overdue': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'draft': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  return (
    <div className="h-full flex flex-col bg-white dark:bg-gray-800">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center gap-3 mb-4">
          <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
            <span className="text-lg font-semibold text-primary">
              {customer.name.charAt(0).toUpperCase()}
            </span>
          </div>
          <div>
            <h3 className="font-semibold text-gray-900 dark:text-gray-100">
              {customer.name}
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Customer
            </p>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-2 gap-2">
          <button className="flex items-center justify-center gap-2 px-3 py-2 text-sm 
                           bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 
                           rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
            <span>📞</span>
            Call
          </button>
          <button className="flex items-center justify-center gap-2 px-3 py-2 text-sm 
                           bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 
                           rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
            <span>✉️</span>
            Email
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <div className="flex">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex-1 px-3 py-2 text-sm font-medium border-b-2 transition-colors ${
                activeTab === tab.id
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
              }`}
            >
              <span className="mr-1">{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </div>
      </div>

      {/* Tab Content */}
      <div className="flex-1 overflow-y-auto p-4">
        {activeTab === "details" && (
          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                Contact Information
              </h4>
              <div className="space-y-2 text-sm">
                <div className="flex items-center gap-2">
                  <span className="text-gray-500">📞</span>
                  <span className="text-gray-900 dark:text-gray-100">{customer.phone}</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-gray-500">✉️</span>
                  <span className="text-gray-900 dark:text-gray-100">{customer.email}</span>
                </div>
                <div className="flex items-start gap-2">
                  <span className="text-gray-500 mt-0.5">📍</span>
                  <div className="text-gray-900 dark:text-gray-100">
                    {customer.address}<br />
                    {customer.city}, {customer.state} {customer.zipCode}
                  </div>
                </div>
              </div>
            </div>

            {customer.company && (
              <div>
                <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                  Company Information
                </h4>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center gap-2">
                    <span className="text-gray-500">🏢</span>
                    <span className="text-gray-900 dark:text-gray-100">{customer.company}</span>
                  </div>
                  {customer.industry && (
                    <div className="flex items-center gap-2">
                      <span className="text-gray-500">🏭</span>
                      <span className="text-gray-900 dark:text-gray-100">{customer.industry}</span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {customer.notes && (
              <div>
                <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                  Notes
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 whitespace-pre-wrap">
                  {customer.notes}
                </p>
              </div>
            )}
          </div>
        )}

        {activeTab === "activity" && (
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
              Recent SMS Activity
            </h4>
            {smsHistory && smsHistory.length > 0 ? (
              <div className="space-y-2">
                {smsHistory.slice(0, 10).map((msg) => (
                  <div key={msg._id} className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="flex justify-between items-start mb-1">
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {formatDate(msg.sentAt)}
                      </span>
                      <span className={`text-xs px-2 py-0.5 rounded-full ${
                        msg.status === 'delivered' ? 'bg-green-100 text-green-800' :
                        msg.status === 'sent' ? 'bg-blue-100 text-blue-800' :
                        msg.status === 'failed' ? 'bg-red-100 text-red-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {msg.status}
                      </span>
                    </div>
                    <p className="text-sm text-gray-900 dark:text-gray-100 line-clamp-2">
                      {msg.message}
                    </p>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-gray-500 dark:text-gray-400">
                No SMS activity yet
              </p>
            )}
          </div>
        )}

        {activeTab === "jobs" && (
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
              Recent Jobs
            </h4>
            {customerJobs && customerJobs.length > 0 ? (
              <div className="space-y-2">
                {customerJobs.slice(0, 5).map((job) => (
                  <div key={job._id} className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="flex justify-between items-start mb-2">
                      <h5 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {job.title}
                      </h5>
                      <span className={`text-xs px-2 py-0.5 rounded-full ${getJobStatusColor(job.status)}`}>
                        {job.status}
                      </span>
                    </div>
                    <p className="text-xs text-gray-600 dark:text-gray-400 line-clamp-2">
                      {job.description}
                    </p>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-gray-500 dark:text-gray-400">
                No jobs yet
              </p>
            )}
          </div>
        )}

        {activeTab === "invoices" && (
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
              Recent Invoices
            </h4>
            {customerInvoices && customerInvoices.length > 0 ? (
              <div className="space-y-2">
                {customerInvoices.slice(0, 5).map((invoice) => (
                  <div key={invoice._id} className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="flex justify-between items-start mb-2">
                      <h5 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {invoice.invoiceNumber}
                      </h5>
                      <span className={`text-xs px-2 py-0.5 rounded-full ${getInvoiceStatusColor(invoice.status)}`}>
                        {invoice.status}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-gray-600 dark:text-gray-400">
                        {formatDate(invoice.issueDate)}
                      </span>
                      <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        ${invoice.total.toFixed(2)}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-gray-500 dark:text-gray-400">
                No invoices yet
              </p>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

import React, { useState, useEffect } from "react";
import { useQuery, useAction } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { toast } from "sonner";

interface SMSComposerProps {
  customerId: string;
  onSent?: () => void;
  onCancel?: () => void;
  className?: string;
}

export function SMSComposer({ customerId, onSent, onCancel, className = "" }: SMSComposerProps) {
  const [message, setMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [showTemplates, setShowTemplates] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<any>(null);
  const [templateVariables, setTemplateVariables] = useState<Record<string, string>>({});

  const customer = useQuery(api.customers.get, { id: customerId });
  const templates = useQuery(api.smsTemplates.list, { activeOnly: true });
  const sendSMS = useAction(api.sms.sendToCustomer);
  const sendWithTemplate = useAction(api.sms.sendWithTemplate);
  const user = useQuery(api.auth.loggedInUser);

  const isAdmin = user?.role === 'admin';

  // Set up template variables when customer data is available
  useEffect(() => {
    if (customer) {
      setTemplateVariables({
        customerName: customer.name || "",
        customerPhone: customer.phone || "",
        customerEmail: customer.email || "",
        customerCompany: customer.company || "",
      });
    }
  }, [customer]);

  const handleTemplateSelect = (template: any) => {
    setSelectedTemplate(template);
    
    // Replace variables in template content
    let content = template.content;
    Object.entries(templateVariables).forEach(([key, value]) => {
      const placeholder = `{${key}}`;
      content = content.replace(new RegExp(placeholder, 'g'), value || '');
    });
    
    setMessage(content);
    setShowTemplates(false);
  };

  const handleSend = async () => {
    if (!isAdmin) {
      toast.error("SMS sending is only available to administrators");
      return;
    }

    if (!message.trim()) {
      toast.error("Please enter a message");
      return;
    }

    if (!customer?.phone) {
      toast.error("Customer phone number is required");
      return;
    }

    setIsLoading(true);
    try {
      if (selectedTemplate) {
        await sendWithTemplate({
          customerId,
          templateId: selectedTemplate._id,
          variables: templateVariables,
        });
      } else {
        await sendSMS({
          customerId,
          message: message.trim(),
        });
      }

      toast.success("SMS sent successfully!");
      setMessage("");
      setSelectedTemplate(null);
      if (onSent) onSent();
    } catch (error: any) {
      toast.error(`Failed to send SMS: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  if (!customer) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading customer...</p>
        </div>
      </div>
    );
  }

  if (!isAdmin) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <div className="text-center">
          <div className="text-4xl mb-4">🔒</div>
          <h3 className="text-lg font-medium text-charcoal dark:text-gray-100 mb-2">
            SMS Access Restricted
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            SMS sending is only available to administrators.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`flex flex-col ${className}`}>
      {/* Customer Info Header */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-medium text-charcoal dark:text-gray-100">
              Sending SMS to {customer.name}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {customer.phone}
            </p>
          </div>
          {onCancel && (
            <button
              onClick={onCancel}
              className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          )}
        </div>
      </div>

      {/* Template Selection */}
      {templates && templates.length > 0 && (
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <button
            onClick={() => setShowTemplates(!showTemplates)}
            className="flex items-center gap-2 text-sm text-primary hover:text-primary/80 font-medium"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Use Template
            <svg className={`w-4 h-4 transition-transform ${showTemplates ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </button>

          {showTemplates && (
            <div className="mt-3 space-y-2 max-h-40 overflow-y-auto">
              {templates.map((template: any) => (
                <button
                  key={template._id}
                  onClick={() => handleTemplateSelect(template)}
                  className="w-full p-3 text-left border border-gray-200 dark:border-gray-600 rounded-lg hover:border-primary hover:bg-primary/5 dark:hover:bg-primary/10 transition-colors"
                >
                  <div className="font-medium text-sm text-charcoal dark:text-gray-100 mb-1">
                    {template.name}
                  </div>
                  <div className="text-xs text-gray-600 dark:text-gray-400 line-clamp-2">
                    {template.content}
                  </div>
                </button>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Message Composer */}
      <div className="flex-1 p-4">
        <div className="space-y-3">
          <div>
            <label className="block text-sm font-medium text-charcoal dark:text-gray-100 mb-2">
              Message
            </label>
            <textarea
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg
                       bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100
                       focus:ring-2 focus:ring-primary focus:border-transparent
                       resize-none"
              placeholder="Type your SMS message here..."
              rows={4}
              maxLength={1600}
              disabled={isLoading}
            />
            <div className="flex justify-between items-center mt-2">
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {message.length}/1600 characters
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                ~{Math.ceil(message.length / 160)} SMS segment{Math.ceil(message.length / 160) !== 1 ? 's' : ''}
              </p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            {onCancel && (
              <button
                onClick={onCancel}
                className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                Cancel
              </button>
            )}
            <button
              onClick={handleSend}
              disabled={isLoading || !message.trim() || !customer?.phone}
              className="flex-1 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isLoading ? (
                <div className="flex items-center justify-center gap-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  <span>Sending...</span>
                </div>
              ) : (
                <div className="flex items-center justify-center gap-2">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                  </svg>
                  <span>Send SMS</span>
                </div>
              )}
            </button>
          </div>

          {/* Warning for missing phone */}
          {customer && !customer.phone && (
            <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
              <p className="text-sm text-yellow-800 dark:text-yellow-300">
                ⚠️ This customer doesn't have a phone number. Please add one before sending SMS.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

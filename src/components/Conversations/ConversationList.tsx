import React, { useState, useMemo } from "react";
import { useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";

interface ConversationListProps {
  selectedCustomerId: string | null;
  onSelectCustomer: (customerId: string) => void;
}

interface ConversationPreview {
  customerId: string;
  customerName: string;
  customerPhone: string;
  lastMessage: string;
  lastMessageTime: number;
  unreadCount: number;
  status: 'sent' | 'delivered' | 'failed' | 'pending';
}

export function ConversationList({ selectedCustomerId, onSelectCustomer }: ConversationListProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");

  const user = useQuery(api.auth.loggedInUser);
  const isAdmin = user?.role === 'admin';

  // Keyboard shortcuts
  React.useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ctrl/Cmd + K to focus search
      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        const searchInput = document.querySelector('input[placeholder*="Search"]') as HTMLInputElement;
        searchInput?.focus();
      }
      // Escape to clear search
      if (e.key === 'Escape' && searchQuery) {
        setSearchQuery("");
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [searchQuery]);

  // Get SMS history - use getAllHistory for admin, getMyHistory for regular users
  const adminSmsHistory = useQuery(
    api.sms.getAllHistory,
    isAdmin ? { limit: 1000 } : "skip"
  );
  const userSmsHistory = useQuery(
    api.sms.getMyHistory,
    !isAdmin ? { limit: 1000 } : "skip"
  );

  const smsHistory = isAdmin ? adminSmsHistory : userSmsHistory;
  const customers = useQuery(api.customers.list);

  // Build conversation previews from SMS history
  const conversations = useMemo(() => {
    if (!smsHistory || !customers) return [];

    const conversationMap = new Map<string, ConversationPreview>();

    // Group messages by customer
    smsHistory.forEach((message) => {
      const customer = customers.find(c => c._id === message.customerId);
      if (!customer) return;

      const existing = conversationMap.get(message.customerId);
      
      if (!existing || message.sentAt > existing.lastMessageTime) {
        conversationMap.set(message.customerId, {
          customerId: message.customerId,
          customerName: customer.name,
          customerPhone: customer.phone,
          lastMessage: message.message,
          lastMessageTime: message.sentAt,
          unreadCount: 0, // TODO: Implement unread tracking
          status: message.status as any,
        });
      }
    });

    return Array.from(conversationMap.values())
      .sort((a, b) => b.lastMessageTime - a.lastMessageTime);
  }, [smsHistory, customers]);

  // Filter conversations based on search and status
  const filteredConversations = useMemo(() => {
    return conversations.filter(conv => {
      const matchesSearch = searchQuery === "" || 
        conv.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        conv.customerPhone.includes(searchQuery) ||
        conv.lastMessage.toLowerCase().includes(searchQuery.toLowerCase());

      const matchesStatus = statusFilter === "all" || conv.status === statusFilter;

      return matchesSearch && matchesStatus;
    });
  }, [conversations, searchQuery, statusFilter]);

  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 168) { // 7 days
      return date.toLocaleDateString([], { weekday: 'short' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'delivered': return 'text-green-500';
      case 'sent': return 'text-blue-500';
      case 'failed': return 'text-red-500';
      case 'pending': return 'text-yellow-500';
      default: return 'text-gray-500';
    }
  };

  const truncateMessage = (message: string, maxLength: number = 50) => {
    return message.length > maxLength ? message.substring(0, maxLength) + '...' : message;
  };

  return (
    <div className="flex flex-col h-full">
      {/* Search and Filters */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700 space-y-3">
        {/* Search Input */}
        <div className="relative">
          <input
            type="text"
            placeholder="Search conversations... (Ctrl+K)"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-16 py-2 border border-gray-300 dark:border-gray-600 rounded-lg
                     bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100
                     focus:ring-2 focus:ring-primary focus:border-transparent"
          />
          <svg
            className="absolute left-3 top-2.5 h-5 w-5 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
          {searchQuery && (
            <button
              onClick={() => setSearchQuery("")}
              className="absolute right-3 top-2.5 h-5 w-5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          )}
        </div>

        {/* Status Filter */}
        <div className="flex gap-2">
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg
                     bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100
                     focus:ring-2 focus:ring-primary focus:border-transparent"
          >
            <option value="all">All Status</option>
            <option value="delivered">Delivered</option>
            <option value="sent">Sent</option>
            <option value="failed">Failed</option>
            <option value="pending">Pending</option>
          </select>

          {/* Clear Filters Button */}
          {(searchQuery || statusFilter !== "all") && (
            <button
              onClick={() => {
                setSearchQuery("");
                setStatusFilter("all");
              }}
              className="px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400
                       rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              title="Clear filters"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          )}
        </div>

        {/* Results Summary */}
        <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mt-2">
          <span>
            {filteredConversations.length} of {conversations.length} conversations
          </span>
          {(searchQuery || statusFilter !== "all") && (
            <span className="text-primary">Filtered</span>
          )}
        </div>
      </div>

      {/* Conversation List */}
      <div className="flex-1 overflow-y-auto">
        {filteredConversations.length === 0 ? (
          <div className="p-6 text-center">
            <div className="text-gray-400 text-4xl mb-3">
              {conversations.length === 0 ? "📱" : "🔍"}
            </div>
            <h4 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
              {conversations.length === 0
                ? "No conversations yet"
                : "No conversations found"}
            </h4>
            <p className="text-xs text-gray-500 dark:text-gray-500">
              {conversations.length === 0
                ? "Start a conversation by sending an SMS to a customer"
                : searchQuery || statusFilter !== "all"
                  ? "Try adjusting your search or filters"
                  : "No conversations match your criteria"}
            </p>
            {(searchQuery || statusFilter !== "all") && (
              <button
                onClick={() => {
                  setSearchQuery("");
                  setStatusFilter("all");
                }}
                className="mt-3 px-3 py-1.5 text-xs bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors"
              >
                Clear filters
              </button>
            )}
          </div>
        ) : (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {filteredConversations.map((conversation) => (
              <button
                key={conversation.customerId}
                onClick={() => onSelectCustomer(conversation.customerId)}
                className={`w-full p-4 text-left hover:bg-gray-50 dark:hover:bg-gray-700 
                          transition-colors focus:outline-none focus:bg-gray-50 dark:focus:bg-gray-700
                          ${selectedCustomerId === conversation.customerId 
                            ? 'bg-primary/5 border-r-2 border-primary' 
                            : ''}`}
              >
                <div className="flex items-start justify-between mb-2">
                  <div className="flex-1 min-w-0">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                      {conversation.customerName}
                    </h4>
                    <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                      {conversation.customerPhone}
                    </p>
                  </div>
                  <div className="flex flex-col items-end gap-1">
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {formatTime(conversation.lastMessageTime)}
                    </span>
                    <div className={`w-2 h-2 rounded-full ${getStatusColor(conversation.status)}`} />
                  </div>
                </div>
                
                <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-2">
                  {truncateMessage(conversation.lastMessage)}
                </p>
                
                {conversation.unreadCount > 0 && (
                  <div className="mt-2 flex justify-end">
                    <span className="inline-flex items-center justify-center w-5 h-5 text-xs font-medium 
                                   text-white bg-primary rounded-full">
                      {conversation.unreadCount}
                    </span>
                  </div>
                )}
              </button>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

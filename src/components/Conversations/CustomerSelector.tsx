import React, { useState, useMemo } from "react";
import { useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";

interface CustomerSelectorProps {
  onCustomerSelect: (customerId: string) => void;
  onClose?: () => void;
  selectedCustomerId?: string | null;
}

interface Customer {
  _id: string;
  name: string;
  phone: string;
  email: string;
  company?: string;
  city?: string;
  state?: string;
}

export function CustomerSelector({ onCustomerSelect, onClose, selectedCustomerId }: CustomerSelectorProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const customers = useQuery(api.customers.list);

  // Filter customers based on search term
  const filteredCustomers = useMemo(() => {
    if (!customers) return [];
    
    if (!searchTerm.trim()) return customers;
    
    const term = searchTerm.toLowerCase();
    return customers.filter((customer: Customer) => 
      customer.name.toLowerCase().includes(term) ||
      customer.phone.toLowerCase().includes(term) ||
      customer.email.toLowerCase().includes(term) ||
      (customer.company && customer.company.toLowerCase().includes(term))
    );
  }, [customers, searchTerm]);

  // Filter customers with phone numbers for SMS
  const smsEligibleCustomers = useMemo(() => {
    return filteredCustomers.filter((customer: Customer) => customer.phone && customer.phone.trim() !== "");
  }, [filteredCustomers]);

  const handleCustomerClick = (customerId: string) => {
    onCustomerSelect(customerId);
  };

  const formatPhoneNumber = (phone: string) => {
    // Simple phone number formatting
    const cleaned = phone.replace(/\D/g, '');
    if (cleaned.length === 10) {
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
    }
    return phone;
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <div>
          <h2 className="text-lg font-semibold text-charcoal dark:text-gray-100">
            Select Customer for SMS
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Choose a customer to start a conversation
          </p>
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        )}
      </div>

      {/* Search */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg
                     bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100
                     focus:ring-2 focus:ring-primary focus:border-transparent
                     placeholder-gray-500 dark:placeholder-gray-400"
            placeholder="Search customers by name, phone, email, or company..."
          />
        </div>
      </div>

      {/* Customer List */}
      <div className="flex-1 overflow-y-auto">
        {smsEligibleCustomers.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full p-8 text-center">
            {searchTerm ? (
              <>
                <div className="text-4xl mb-4">🔍</div>
                <h3 className="text-lg font-medium text-charcoal dark:text-gray-100 mb-2">
                  No customers found
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Try adjusting your search terms or check if customers have phone numbers.
                </p>
              </>
            ) : (
              <>
                <div className="text-4xl mb-4">📱</div>
                <h3 className="text-lg font-medium text-charcoal dark:text-gray-100 mb-2">
                  No SMS-eligible customers
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Add phone numbers to customer records to enable SMS conversations.
                </p>
              </>
            )}
          </div>
        ) : (
          <div className="p-4 space-y-2">
            {smsEligibleCustomers.map((customer: Customer) => (
              <button
                key={customer._id}
                onClick={() => handleCustomerClick(customer._id)}
                className={`w-full p-4 rounded-lg border text-left transition-all duration-200 hover:shadow-md ${
                  selectedCustomerId === customer._id
                    ? "border-primary bg-primary/5 dark:bg-primary/10"
                    : "border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 hover:border-gray-300 dark:hover:border-gray-600"
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="font-medium text-charcoal dark:text-gray-100 truncate">
                        {customer.name}
                      </h3>
                      {selectedCustomerId === customer._id && (
                        <div className="w-2 h-2 bg-primary rounded-full flex-shrink-0"></div>
                      )}
                    </div>
                    
                    <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-1">
                      <svg className="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                      </svg>
                      <span className="font-mono">{formatPhoneNumber(customer.phone)}</span>
                    </div>
                    
                    {customer.email && (
                      <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-1">
                        <svg className="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                        </svg>
                        <span className="truncate">{customer.email}</span>
                      </div>
                    )}
                    
                    {customer.company && (
                      <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                        <svg className="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                        <span className="truncate">{customer.company}</span>
                      </div>
                    )}
                    
                    {customer.city && customer.state && (
                      <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mt-1">
                        <svg className="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                        <span className="truncate">{customer.city}, {customer.state}</span>
                      </div>
                    )}
                  </div>
                  
                  <div className="ml-4 flex-shrink-0">
                    <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Footer with count */}
      {smsEligibleCustomers.length > 0 && (
        <div className="p-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
          <p className="text-sm text-gray-600 dark:text-gray-400 text-center">
            {smsEligibleCustomers.length} customer{smsEligibleCustomers.length !== 1 ? 's' : ''} available for SMS
            {searchTerm && ` (filtered from ${customers?.length || 0} total)`}
          </p>
        </div>
      )}
    </div>
  );
}

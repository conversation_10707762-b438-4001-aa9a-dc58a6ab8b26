import React, { useState } from "react";
import { useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { ConversationList } from "./ConversationList";
import { ConversationThread } from "./ConversationThread";
import { CustomerInfoPanel } from "./CustomerInfoPanel";
import { CustomerSelector } from "./CustomerSelector";
import { SMSComposer } from "./SMSComposer";

export function Conversations() {
  const [selectedCustomerId, setSelectedCustomerId] = useState<string | null>(null);
  const [showCustomerInfo, setShowCustomerInfo] = useState(true);
  const [showConversationList, setShowConversationList] = useState(true);
  const [showCustomerSelector, setShowCustomerSelector] = useState(false);
  const [showSMSComposer, setShowSMSComposer] = useState(false);

  const user = useQuery(api.auth.loggedInUser);
  const configStatus = useQuery(api.twilioConfig.getConfigStatus);

  const isAdmin = user?.role === 'admin' || user?.role === 'master';

  // Show configuration message if SMS is not set up and user is admin
  if (isAdmin && !configStatus?.isConfigured) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center py-12">
          <div className="text-6xl mb-4">💬</div>
          <h2 className="text-2xl font-semibold text-charcoal dark:text-gray-100 mb-4">
            SMS Service Not Configured
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto">
            To start conversations with your customers, you need to configure your Twilio SMS credentials first.
          </p>
          <button
            onClick={() => {
              // Navigate to SMS settings
              window.location.hash = '#sms';
            }}
            className="btn-primary"
          >
            Configure SMS Service
          </button>
        </div>
      </div>
    );
  }

  // Handle customer selection for new SMS conversation
  const handleCustomerSelect = (customerId: string) => {
    setSelectedCustomerId(customerId);
    setShowCustomerSelector(false);
    setShowSMSComposer(true);
    setShowConversationList(false);
  };

  // Handle starting a new SMS conversation
  const handleNewSMSConversation = () => {
    if (!isAdmin) {
      return; // SMS is admin-only
    }
    setShowCustomerSelector(true);
    setShowConversationList(false);
    setSelectedCustomerId(null);
    setShowSMSComposer(false);
  };

  // Handle SMS sent successfully
  const handleSMSSent = () => {
    setShowSMSComposer(false);
    setShowConversationList(true);
    // Keep the customer selected to show their conversation thread
  };

  // Handle canceling SMS composition
  const handleCancelSMS = () => {
    setShowSMSComposer(false);
    setShowConversationList(true);
    setSelectedCustomerId(null);
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center gap-3">
          <h1 className="text-2xl font-bold text-charcoal dark:text-gray-100">
            Conversations
          </h1>
          <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
            <span className="w-2 h-2 bg-green-500 rounded-full"></span>
            <span>SMS Ready</span>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          {/* New SMS Conversation Button - Admin Only */}
          {isAdmin && (
            <button
              onClick={handleNewSMSConversation}
              className="flex items-center gap-2 px-3 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors"
              title="Start new SMS conversation"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              <span className="hidden sm:inline">New SMS</span>
            </button>
          )}

          {/* Toggle Customer Info Panel */}
          <button
            onClick={() => setShowCustomerInfo(!showCustomerInfo)}
            className={`p-2 rounded-lg transition-colors ${
              showCustomerInfo
                ? 'bg-primary/10 text-primary'
                : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
            }`}
            title={showCustomerInfo ? 'Hide customer info' : 'Show customer info'}
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex min-h-0 relative">
        {/* Conversation List Sidebar - Desktop */}
        <div className={`hidden md:flex w-80 border-r border-gray-200 dark:border-gray-700 flex-col ${
          (selectedCustomerId && !showConversationList) || showCustomerSelector ? 'md:hidden' : ''
        }`}>
          <ConversationList
            selectedCustomerId={selectedCustomerId}
            onSelectCustomer={(customerId) => {
              setSelectedCustomerId(customerId);
              setShowConversationList(false); // Hide list on mobile when conversation is selected
              setShowSMSComposer(false);
            }}
          />
        </div>

        {/* Customer Selector - Desktop */}
        {showCustomerSelector && (
          <div className="hidden md:flex w-80 border-r border-gray-200 dark:border-gray-700 flex-col">
            <CustomerSelector
              selectedCustomerId={selectedCustomerId}
              onCustomerSelect={handleCustomerSelect}
              onClose={() => {
                setShowCustomerSelector(false);
                setShowConversationList(true);
              }}
            />
          </div>
        )}

        {/* Mobile Conversation List Overlay */}
        {showConversationList && (
          <div className="md:hidden absolute inset-0 z-20 bg-white dark:bg-gray-800">
            <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                Conversations
              </h2>
              <div className="flex items-center gap-2">
                {/* New SMS Button - Mobile */}
                {isAdmin && (
                  <button
                    onClick={handleNewSMSConversation}
                    className="p-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors"
                    title="Start new SMS conversation"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                  </button>
                )}
                {selectedCustomerId && (
                  <button
                    onClick={() => setShowConversationList(false)}
                    className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                )}
              </div>
            </div>
            <ConversationList
              selectedCustomerId={selectedCustomerId}
              onSelectCustomer={(customerId) => {
                setSelectedCustomerId(customerId);
                setShowConversationList(false);
                setShowSMSComposer(false);
              }}
            />
          </div>
        )}

        {/* Mobile Customer Selector Overlay */}
        {showCustomerSelector && (
          <div className="md:hidden absolute inset-0 z-20 bg-white dark:bg-gray-800">
            <CustomerSelector
              selectedCustomerId={selectedCustomerId}
              onCustomerSelect={handleCustomerSelect}
              onClose={() => {
                setShowCustomerSelector(false);
                setShowConversationList(true);
              }}
            />
          </div>
        )}

        {/* Mobile SMS Composer Overlay */}
        {showSMSComposer && selectedCustomerId && (
          <div className="md:hidden absolute inset-0 z-20 bg-white dark:bg-gray-800">
            <SMSComposer
              customerId={selectedCustomerId}
              onSent={handleSMSSent}
              onCancel={handleCancelSMS}
              className="h-full"
            />
          </div>
        )}

        {/* Conversation Thread */}
        <div className={`flex-1 flex flex-col ${showCustomerInfo && selectedCustomerId && !showSMSComposer ? 'md:mr-80' : ''}`}>
          {showSMSComposer && selectedCustomerId ? (
            /* Desktop SMS Composer */
            <div className="hidden md:flex flex-col h-full">
              <SMSComposer
                customerId={selectedCustomerId}
                onSent={handleSMSSent}
                onCancel={handleCancelSMS}
                className="h-full"
              />
            </div>
          ) : selectedCustomerId ? (
            <div className="flex flex-col h-full">
              {/* Mobile Header with Back Button */}
              <div className="md:hidden flex items-center gap-3 p-4 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
                <button
                  onClick={() => {
                    setShowConversationList(true);
                    setShowSMSComposer(false);
                  }}
                  className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                </button>
                <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  Back to Conversations
                </h2>
              </div>
              <ConversationThread customerId={selectedCustomerId} />
            </div>
          ) : (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <div className="text-6xl mb-4 text-gray-300 dark:text-gray-600">💬</div>
                <h3 className="text-lg font-medium text-gray-600 dark:text-gray-400 mb-2">
                  Select a conversation
                </h3>
                <p className="text-gray-500 dark:text-gray-500 mb-4">
                  Choose a customer from the list to view your conversation history
                </p>
                {isAdmin && (
                  <button
                    onClick={handleNewSMSConversation}
                    className="inline-flex items-center gap-2 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    Start New SMS Conversation
                  </button>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Customer Info Panel - Desktop Only */}
        {showCustomerInfo && selectedCustomerId && !showSMSComposer && (
          <div className="hidden md:block w-80 border-l border-gray-200 dark:border-gray-700 absolute right-0 top-0 bottom-0 bg-white dark:bg-gray-800">
            <CustomerInfoPanel customerId={selectedCustomerId} />
          </div>
        )}
      </div>
    </div>
  );
}

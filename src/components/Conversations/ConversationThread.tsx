import React, { useState, useEffect, useRef } from "react";
import { useQuery, useAction } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { toast } from "sonner";

interface ConversationThreadProps {
  customerId: string;
}

export function ConversationThread({ customerId }: ConversationThreadProps) {
  const [message, setMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [showTemplates, setShowTemplates] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<any>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const customer = useQuery(api.customers.get, { id: customerId });
  const messages = useQuery(api.sms.getCustomerHistory, { customerId });
  const templates = useQuery(api.smsTemplates.list, { activeOnly: true });
  const sendSMS = useAction(api.sms.sendToCustomer);
  const sendWithTemplate = useAction(api.sms.sendWithTemplate);
  const user = useQuery(api.auth.loggedInUser);

  const isAdmin = user?.role === 'admin';

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  const handleSendMessage = async () => {
    if (!message.trim()) {
      toast.error("Please enter a message");
      return;
    }

    if (!customer?.phone) {
      toast.error("Customer phone number is required");
      return;
    }

    setIsLoading(true);
    try {
      if (selectedTemplate) {
        // Send with template
        await sendWithTemplate({
          customerId,
          templateId: selectedTemplate._id,
          variables: {
            customerName: customer.name,
            customerPhone: customer.phone,
            customerEmail: customer.email,
          },
        });
      } else {
        // Send regular message
        await sendSMS({
          customerId,
          message: message.trim(),
        });
      }

      toast.success("Message sent successfully!");
      setMessage("");
      setSelectedTemplate(null);
      setShowTemplates(false);
    } catch (error: any) {
      toast.error(`Failed to send message: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleTemplateSelect = (template: any) => {
    setSelectedTemplate(template);
    setMessage(template.content);
    setShowTemplates(false);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString([], { 
        month: 'short', 
        day: 'numeric',
        hour: '2-digit', 
        minute: '2-digit' 
      });
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'delivered':
        return <span className="text-green-500">✓✓</span>;
      case 'sent':
        return <span className="text-blue-500">✓</span>;
      case 'failed':
        return <span className="text-red-500">✗</span>;
      case 'pending':
        return <span className="text-yellow-500">⏳</span>;
      default:
        return null;
    }
  };

  if (!customer) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <div className="text-4xl mb-2">⏳</div>
          <p className="text-gray-500">Loading conversation...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Conversation Header */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              {customer.name}
            </h2>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {customer.phone} • {customer.email}
            </p>
          </div>
          
          <div className="flex items-center gap-2">
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                           bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
              Active
            </span>
          </div>
        </div>
      </div>

      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50 dark:bg-gray-900">
        {!messages || messages.length === 0 ? (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <div className="text-4xl mb-2 text-gray-300 dark:text-gray-600">💬</div>
              <h3 className="text-lg font-medium text-gray-600 dark:text-gray-400 mb-1">
                No messages yet
              </h3>
              <p className="text-gray-500 dark:text-gray-500">
                Start the conversation by sending a message below
              </p>
            </div>
          </div>
        ) : (
          messages.map((msg) => (
            <div key={msg._id} className="flex justify-end">
              {/* Outgoing message (from business) */}
              <div className="max-w-xs lg:max-w-md">
                <div className="bg-primary text-white rounded-lg px-4 py-2 shadow-sm">
                  <p className="text-sm whitespace-pre-wrap">{msg.message}</p>
                </div>
                <div className="flex items-center justify-end gap-1 mt-1">
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {formatTime(msg.sentAt)}
                  </span>
                  {getStatusIcon(msg.status)}
                </div>
              </div>
            </div>
          ))
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Message Composer */}
      <div className="p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
        {/* Template Selection */}
        {selectedTemplate && (
          <div className="mb-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <div className="flex items-center justify-between">
              <div>
                <span className="text-sm font-medium text-blue-900 dark:text-blue-100">
                  Using template: {selectedTemplate.name}
                </span>
                <p className="text-xs text-blue-700 dark:text-blue-300 mt-1">
                  {selectedTemplate.description}
                </p>
              </div>
              <button
                onClick={() => {
                  setSelectedTemplate(null);
                  setMessage("");
                }}
                className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>
        )}

        {isAdmin ? (
          <div className="flex items-end gap-3">
            <div className="flex-1">
              {/* Template Button */}
              <div className="flex items-center gap-2 mb-2">
                <button
                  onClick={() => setShowTemplates(!showTemplates)}
                  className="flex items-center gap-2 px-3 py-1.5 text-sm bg-gray-100 dark:bg-gray-700
                           text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600
                           transition-colors"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  Templates
                </button>
              </div>

              {/* Template Dropdown */}
              {showTemplates && templates && templates.length > 0 && (
                <div className="mb-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600">
                  <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                    Select a template:
                  </h4>
                  <div className="space-y-2 max-h-40 overflow-y-auto">
                    {templates.map((template) => (
                      <button
                        key={template._id}
                        onClick={() => handleTemplateSelect(template)}
                        className="w-full text-left p-2 bg-white dark:bg-gray-600 rounded border
                                 border-gray-200 dark:border-gray-500 hover:bg-gray-50 dark:hover:bg-gray-500
                                 transition-colors"
                      >
                        <div className="font-medium text-sm text-gray-900 dark:text-gray-100">
                          {template.name}
                        </div>
                        <div className="text-xs text-gray-600 dark:text-gray-400 mt-1 line-clamp-2">
                          {template.content}
                        </div>
                      </button>
                    ))}
                  </div>
                </div>
              )}

              <textarea
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Type your message..."
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg
                         bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100
                         focus:ring-2 focus:ring-primary focus:border-transparent
                         resize-none"
                rows={3}
                maxLength={1600}
                disabled={isLoading}
              />
              <div className="flex justify-between items-center mt-2">
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {message.length}/1600 characters
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  ~{Math.ceil(message.length / 160)} SMS segment{Math.ceil(message.length / 160) !== 1 ? 's' : ''}
                </p>
              </div>
            </div>

            <button
              onClick={handleSendMessage}
              disabled={isLoading || !message.trim()}
              className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90
                       disabled:opacity-50 disabled:cursor-not-allowed
                       focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2
                       transition-colors"
            >
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  <span>Sending...</span>
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                  </svg>
                  <span>Send</span>
                </div>
              )}
            </button>
          </div>
        ) : (
          /* Staff users - read-only view */
          <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600">
            <div className="flex items-center gap-3 text-gray-600 dark:text-gray-400">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <div>
                <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  View Only Access
                </p>
                <p className="text-xs">
                  SMS sending is restricted to administrators. You can view conversation history but cannot send new messages.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

import React, { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { toast } from "sonner";
import { RoleBadge } from "../UserProfile/RoleBadge";
import { Id } from "../../../convex/_generated/dataModel";

export function AdminApprovalDashboard() {
  const [selectedRequest, setSelectedRequest] = useState<any>(null);
  const [showReviewModal, setShowReviewModal] = useState(false);

  const user = useQuery(api.auth.loggedInUser);
  const pendingRequests = useQuery(api.roleManagement.listPendingRoleRequests);
  const roleStats = useQuery(api.roleManagement.getRoleStatistics);

  const isMaster = user?.role === "master";

  // Check if user has master role
  if (user && !isMaster) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="text-center py-12">
          <div className="text-6xl mb-4">🔒</div>
          <h2 className="text-2xl font-semibold text-charcoal dark:text-gray-100 mb-4">
            Access Restricted
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto">
            Admin approval functionality is only available to Master users.
          </p>
        </div>
      </div>
    );
  }

  if (!pendingRequests || !roleStats) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  const handleReviewRequest = (request: any) => {
    setSelectedRequest(request);
    setShowReviewModal(true);
  };

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl md:text-3xl font-bold text-charcoal dark:text-gray-100 mb-2">
          Admin Role Approvals
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Review and approve admin role requests from users
        </p>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="card p-6">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg flex items-center justify-center">
              <span className="text-2xl">⏳</span>
            </div>
            <div>
              <h3 className="text-2xl font-bold text-charcoal dark:text-gray-100">
                {pendingRequests.length}
              </h3>
              <p className="text-gray-600 dark:text-gray-400">Pending Requests</p>
            </div>
          </div>
        </div>

        <div className="card p-6">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center">
              <span className="text-2xl">👑</span>
            </div>
            <div>
              <h3 className="text-2xl font-bold text-charcoal dark:text-gray-100">
                {roleStats.roleDistribution.admin || 0}
              </h3>
              <p className="text-gray-600 dark:text-gray-400">Current Admins</p>
            </div>
          </div>
        </div>

        <div className="card p-6">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
              <span className="text-2xl">👥</span>
            </div>
            <div>
              <h3 className="text-2xl font-bold text-charcoal dark:text-gray-100">
                {roleStats.totalUsers}
              </h3>
              <p className="text-gray-600 dark:text-gray-400">Total Users</p>
            </div>
          </div>
        </div>
      </div>

      {/* Pending Requests */}
      <div className="card">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-charcoal dark:text-gray-100">
            Pending Admin Role Requests
          </h2>
        </div>

        <div className="p-6">
          {pendingRequests.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-4xl mb-4">✅</div>
              <h3 className="text-lg font-semibold text-charcoal dark:text-gray-100 mb-2">
                No Pending Requests
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                All admin role requests have been reviewed.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {pendingRequests.map((request) => (
                <div
                  key={request._id}
                  className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:shadow-md transition-shadow"
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="font-semibold text-charcoal dark:text-gray-100">
                          {request.user.name}
                        </h3>
                        <span className="text-sm text-gray-500">→</span>
                        <RoleBadge role={request.requestedRole} />
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                        {request.user.email}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Current role: <span className="capitalize">{request.currentRole}</span>
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Requested by: {request.requestedByUser.name}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {new Date(request.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                  </div>

                  {request.reason && (
                    <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <p className="text-sm text-gray-700 dark:text-gray-300">
                        <strong>Reason:</strong> {request.reason}
                      </p>
                    </div>
                  )}

                  <div className="flex gap-3">
                    <button
                      onClick={() => handleReviewRequest(request)}
                      className="btn-primary flex-1"
                    >
                      Review Request
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Review Modal */}
      {showReviewModal && selectedRequest && (
        <ReviewRequestModal
          request={selectedRequest}
          onClose={() => {
            setShowReviewModal(false);
            setSelectedRequest(null);
          }}
          onSuccess={() => {
            setShowReviewModal(false);
            setSelectedRequest(null);
            toast.success("Request reviewed successfully");
          }}
        />
      )}
    </div>
  );
}

// Review Request Modal Component
function ReviewRequestModal({
  request,
  onClose,
  onSuccess,
}: {
  request: any;
  onClose: () => void;
  onSuccess: () => void;
}) {
  const [action, setAction] = useState<"approve" | "reject" | null>(null);
  const [reviewNotes, setReviewNotes] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const reviewRequest = useMutation(api.roleManagement.reviewRoleRequest);

  const handleSubmit = async (selectedAction: "approve" | "reject") => {
    if (!reviewNotes.trim()) {
      toast.error("Please provide review notes");
      return;
    }

    try {
      setIsSubmitting(true);
      await reviewRequest({
        requestId: request._id,
        action: selectedAction,
        reviewNotes: reviewNotes.trim(),
      });
      
      toast.success(`Request ${selectedAction}d successfully`);
      onSuccess();
    } catch (error) {
      toast.error(`Failed to ${selectedAction} request`);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-charcoal dark:text-gray-100">
              Review Admin Role Request
            </h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              ✕
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Request Details */}
          <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div className="flex items-center justify-between mb-3">
              <div>
                <h4 className="font-semibold text-charcoal dark:text-gray-100">
                  {request.user.name}
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {request.user.email}
                </p>
              </div>
              <div className="text-right">
                <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">
                  Requesting Role Change
                </p>
                <div className="flex items-center gap-2">
                  <RoleBadge role={request.currentRole} />
                  <span className="text-gray-400">→</span>
                  <RoleBadge role={request.requestedRole} />
                </div>
              </div>
            </div>
            
            <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">
              Requested by: <span className="font-medium">{request.requestedByUser.name}</span>
            </div>
            
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Date: {new Date(request.createdAt).toLocaleDateString()}
            </div>

            {request.reason && (
              <div className="mt-3 p-3 bg-white dark:bg-gray-600 rounded">
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  <strong>Reason:</strong> {request.reason}
                </p>
              </div>
            )}
          </div>

          {/* Review Notes */}
          <div className="mb-6">
            <label className="form-label">
              Review Notes <span className="text-red-500">*</span>
            </label>
            <textarea
              value={reviewNotes}
              onChange={(e) => setReviewNotes(e.target.value)}
              placeholder="Provide your review notes and reasoning for this decision..."
              className="form-input h-24 resize-none"
              required
            />
          </div>

          {/* Actions */}
          <div className="flex gap-3">
            <button
              onClick={() => handleSubmit("reject")}
              disabled={isSubmitting || !reviewNotes.trim()}
              className="btn-secondary flex-1"
            >
              {isSubmitting ? "Processing..." : "Reject Request"}
            </button>
            <button
              onClick={() => handleSubmit("approve")}
              disabled={isSubmitting || !reviewNotes.trim()}
              className="btn-primary flex-1"
            >
              {isSubmitting ? "Processing..." : "Approve Request"}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

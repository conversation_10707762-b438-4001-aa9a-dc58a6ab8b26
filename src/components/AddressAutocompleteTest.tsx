import React, { useState } from 'react';
import { AddressAutocomplete, AddressFormData } from './common/AddressAutocomplete';
import { ParsedAddress } from '../services/addressService';

export function AddressAutocompleteTest() {
  const [formData, setFormData] = useState({
    address: '',
    city: '',
    state: '',
    zipCode: '',
  });

  const [selectedAddress, setSelectedAddress] = useState<ParsedAddress | null>(null);
  const [testResults, setTestResults] = useState<string[]>([]);

  const handleAddressChange = (address: string) => {
    setFormData(prev => ({ ...prev, address }));
    addTestResult(`Address input changed: "${address}"`);
  };

  const handleFormDataChange = (addressData: Partial<AddressFormData>) => {
    setFormData(prev => ({ ...prev, ...addressData }));
    addTestResult(`Form data auto-populated: ${JSON.stringify(addressData)}`);
  };

  const handleAddressSelect = (address: ParsedAddress) => {
    setSelectedAddress(address);
    addTestResult(`Address selected: ${address.street}, ${address.city}, ${address.state} ${address.zipCode}`);
  };

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
  };

  const clearResults = () => {
    setTestResults([]);
    setSelectedAddress(null);
  };

  const testScenarios = [
    {
      name: "Residential Address",
      testAddress: "123 Main Street, Anytown, CA",
      description: "Test with a typical residential address"
    },
    {
      name: "Commercial Address", 
      testAddress: "1600 Amphitheatre Parkway, Mountain View, CA",
      description: "Test with a well-known commercial address"
    },
    {
      name: "PO Box",
      testAddress: "PO Box 123, New York, NY",
      description: "Test with a PO Box address"
    },
    {
      name: "Partial Address",
      testAddress: "Times Square",
      description: "Test with a partial/landmark address"
    }
  ];

  const runTestScenario = (testAddress: string) => {
    setFormData(prev => ({ ...prev, address: testAddress }));
    addTestResult(`Running test with: "${testAddress}"`);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <h1 className="text-2xl font-bold mb-4">Address Autocomplete Test</h1>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          Test the address autocomplete functionality with various scenarios.
        </p>

        {/* Test Form */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div className="space-y-4">
            <h2 className="text-lg font-semibold">Test Form</h2>
            
            <AddressAutocomplete
              value={formData.address}
              onChange={handleAddressChange}
              onFormDataChange={handleFormDataChange}
              onAddressSelect={handleAddressSelect}
              label="Street Address"
              placeholder="Enter an address to test..."
              className="w-full"
            />

            <div>
              <label className="block text-sm font-medium mb-1">City</label>
              <input
                type="text"
                value={formData.city}
                onChange={(e) => setFormData(prev => ({ ...prev, city: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                placeholder="Auto-filled from address"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">State</label>
                <input
                  type="text"
                  value={formData.state}
                  onChange={(e) => setFormData(prev => ({ ...prev, state: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  placeholder="Auto-filled"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">ZIP Code</label>
                <input
                  type="text"
                  value={formData.zipCode}
                  onChange={(e) => setFormData(prev => ({ ...prev, zipCode: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  placeholder="Auto-filled"
                />
              </div>
            </div>
          </div>

          {/* Test Scenarios */}
          <div className="space-y-4">
            <h2 className="text-lg font-semibold">Test Scenarios</h2>
            
            {testScenarios.map((scenario, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <h3 className="font-medium">{scenario.name}</h3>
                <p className="text-sm text-gray-600 mb-2">{scenario.description}</p>
                <p className="text-sm font-mono bg-gray-100 p-2 rounded mb-2">
                  {scenario.testAddress}
                </p>
                <button
                  onClick={() => runTestScenario(scenario.testAddress)}
                  className="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600"
                >
                  Test This
                </button>
              </div>
            ))}
          </div>
        </div>

        {/* Current Form State */}
        <div className="mt-8 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <h3 className="font-semibold mb-2">Current Form State</h3>
          <pre className="text-sm overflow-x-auto">
            {JSON.stringify(formData, null, 2)}
          </pre>
        </div>

        {/* Selected Address Details */}
        {selectedAddress && (
          <div className="mt-4 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <h3 className="font-semibold mb-2">Last Selected Address</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div><strong>Street:</strong> {selectedAddress.street}</div>
              <div><strong>City:</strong> {selectedAddress.city}</div>
              <div><strong>State:</strong> {selectedAddress.state}</div>
              <div><strong>ZIP:</strong> {selectedAddress.zipCode}</div>
              <div><strong>Country:</strong> {selectedAddress.country}</div>
              {selectedAddress.coordinates && (
                <div className="col-span-2">
                  <strong>Coordinates:</strong> {selectedAddress.coordinates[1]}, {selectedAddress.coordinates[0]}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Test Results Log */}
        <div className="mt-8">
          <div className="flex justify-between items-center mb-4">
            <h3 className="font-semibold">Test Results Log</h3>
            <button
              onClick={clearResults}
              className="bg-gray-500 text-white px-3 py-1 rounded text-sm hover:bg-gray-600"
            >
              Clear Log
            </button>
          </div>
          <div className="bg-black text-green-400 p-4 rounded-lg font-mono text-sm max-h-64 overflow-y-auto">
            {testResults.length === 0 ? (
              <div className="text-gray-500">No test results yet. Start typing in the address field above.</div>
            ) : (
              testResults.map((result, index) => (
                <div key={index} className="mb-1">
                  {result}
                </div>
              ))
            )}
          </div>
        </div>

        {/* API Status */}
        <div className="mt-8 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <h3 className="font-semibold mb-2">API Configuration Status</h3>
          <div className="text-sm space-y-1">
            <div>
              <strong>Mapbox API Key:</strong> {
                import.meta.env.VITE_MAPBOX_API_KEY 
                  ? `Configured (${import.meta.env.VITE_MAPBOX_API_KEY.substring(0, 10)}...)` 
                  : 'Not configured'
              }
            </div>
            <div>
              <strong>Environment:</strong> {import.meta.env.MODE}
            </div>
          </div>
        </div>

        {/* Instructions */}
        <div className="mt-8 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
          <h3 className="font-semibold mb-2">Testing Instructions</h3>
          <ul className="text-sm space-y-1 list-disc list-inside">
            <li>Type at least 2 characters in the address field to see suggestions</li>
            <li>Use arrow keys to navigate suggestions, Enter to select</li>
            <li>Try the test scenarios on the right to test different address types</li>
            <li>Watch the test log to see how the component responds</li>
            <li>Verify that city, state, and ZIP are auto-populated when you select an address</li>
            <li>Test error handling by disconnecting your internet</li>
          </ul>
        </div>
      </div>
    </div>
  );
}

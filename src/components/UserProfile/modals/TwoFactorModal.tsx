import React, { useState } from "react";
import { toast } from "sonner";

interface TwoFactorModalProps {
  enabled: boolean;
  onClose: () => void;
  onToggle: (enabled: boolean) => void;
}

export function TwoFactorModal({ enabled, onClose, onToggle }: TwoFactorModalProps) {
  const [step, setStep] = useState(1);
  const [qrCode, setQrCode] = useState("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iI2Y5ZmFmYiIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBkb21pbmFudC1iYXNlbGluZT0ibWlkZGxlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmb250LWZhbWlseT0ibW9ub3NwYWNlIiBmb250LXNpemU9IjE0cHgiIGZpbGw9IiM2YjcyODAiPlFSIENvZGU8L3RleHQ+PC9zdmc+");
  const [verificationCode, setVerificationCode] = useState("");

  const handleToggle = () => {
    if (enabled) {
      // Disable 2FA
      onToggle(false);
      onClose();
    } else {
      // Enable 2FA - show setup process
      setStep(1);
    }
  };

  const handleVerify = () => {
    if (verificationCode.length === 6) {
      onToggle(true);
      onClose();
    } else {
      toast.error("Please enter a valid 6-digit code");
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-4 md:p-6 w-full max-w-md">
        <div className="flex justify-between items-center mb-4 md:mb-6">
          <h2 className="text-lg md:text-xl font-semibold text-charcoal dark:text-gray-100">
            {enabled ? "Disable" : "Enable"} Two-Factor Authentication
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 text-xl p-2 touch-manipulation"
          >
            ✕
          </button>
        </div>
        
        {enabled ? (
          <div className="space-y-4">
            <p className="text-gray-600 dark:text-gray-400">
              Are you sure you want to disable two-factor authentication? This will make your account less secure.
            </p>
            <div className="flex flex-col md:flex-row gap-3">
              <button
                onClick={handleToggle}
                className="btn-danger flex-1 touch-manipulation"
              >
                Disable 2FA
              </button>
              <button
                onClick={onClose}
                className="btn-secondary flex-1 touch-manipulation"
              >
                Cancel
              </button>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {step === 1 && (
              <>
                <div className="text-center">
                  <img src={qrCode} alt="QR Code" className="mx-auto mb-4 rounded-lg" />
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                    Scan this QR code with your authenticator app
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400 font-mono bg-gray-100 dark:bg-gray-700 p-2 rounded">
                    JBSWY3DPEHPK3PXP
                  </p>
                </div>
                <button
                  onClick={() => setStep(2)}
                  className="btn-primary w-full touch-manipulation"
                >
                  I've Added the Account
                </button>
              </>
            )}
            
            {step === 2 && (
              <>
                <div>
                  <label className="form-label">Enter Verification Code</label>
                  <input
                    type="text"
                    value={verificationCode}
                    onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                    className="form-input text-center text-lg tracking-widest"
                    placeholder="000000"
                    maxLength={6}
                  />
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Enter the 6-digit code from your authenticator app
                  </p>
                </div>
                <div className="flex flex-col md:flex-row gap-3">
                  <button
                    onClick={handleVerify}
                    disabled={verificationCode.length !== 6}
                    className="btn-primary flex-1 touch-manipulation disabled:opacity-50"
                  >
                    Verify & Enable
                  </button>
                  <button
                    onClick={() => setStep(1)}
                    className="btn-secondary flex-1 touch-manipulation"
                  >
                    Back
                  </button>
                </div>
              </>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
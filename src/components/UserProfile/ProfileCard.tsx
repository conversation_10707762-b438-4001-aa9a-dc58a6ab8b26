import React from "react";
import { RoleBadge } from "./RoleBadge";

interface ProfileCardProps {
  user: any;
  userProfile: any;
  setIsEditing: (value: boolean) => void;
}

export function ProfileCard({ user, userProfile, setIsEditing }: ProfileCardProps) {
  return (
    <div className="card overflow-hidden">
      <div className="relative h-24 md:h-32 bg-gradient-to-r from-primary to-accent"></div>
      
      <div className="relative px-4 md:px-8 pb-6 md:pb-8">
        {/* Profile Photo */}
        <div className="absolute -top-12 md:-top-16 left-4 md:left-8">
          <div className="relative">
            <div className="w-24 h-24 md:w-32 md:h-32 rounded-full bg-white dark:bg-gray-800 p-1 shadow-lg">
              <div className="w-full h-full rounded-full bg-gradient-to-br from-primary to-accent flex items-center justify-center">
                <span className="text-2xl md:text-4xl font-bold text-white">
                  {user.name ? user.name.charAt(0).toUpperCase() : "U"}
                </span>
              </div>
            </div>
            <button className="absolute bottom-1 md:bottom-2 right-1 md:right-2 w-6 h-6 md:w-8 md:h-8 bg-white dark:bg-gray-800 rounded-full shadow-md flex items-center justify-center hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors touch-manipulation">
              <span className="text-xs md:text-sm">📷</span>
            </button>
          </div>
        </div>

        {/* Profile Info */}
        <div className="pt-16 md:pt-20">
          <div className="flex flex-col md:flex-row md:items-start md:justify-between gap-4">
            <div className="flex-1">
              <div className="flex flex-col md:flex-row md:items-center gap-2 md:gap-3 mb-2">
                <h2 className="text-xl md:text-2xl font-bold text-charcoal dark:text-gray-100">
                  {user.name || "User"}
                </h2>
                {userProfile.role && <RoleBadge role={userProfile.role} />}
              </div>
              <p className="text-gray-600 dark:text-gray-400 mb-1 text-sm md:text-base">{user.email}</p>
              <p className="text-gray-600 dark:text-gray-400 text-sm md:text-base">{userProfile.phone || "No phone number"}</p>
              
              {/* Activity Summary */}
              <div className="grid grid-cols-3 gap-4 md:gap-6 mt-4">
                <div className="text-center">
                  <div className="text-lg md:text-xl font-bold text-primary">{userProfile.stats.totalJobs}</div>
                  <div className="text-xs md:text-sm text-gray-600 dark:text-gray-400">Jobs</div>
                </div>
                <div className="text-center">
                  <div className="text-lg md:text-xl font-bold text-accent">{userProfile.stats.totalCustomers}</div>
                  <div className="text-xs md:text-sm text-gray-600 dark:text-gray-400">Customers</div>
                </div>
                <div className="text-center">
                  <div className="text-lg md:text-xl font-bold text-primary">${userProfile.stats.totalRevenue.toLocaleString()}</div>
                  <div className="text-xs md:text-sm text-gray-600 dark:text-gray-400">Revenue</div>
                </div>
              </div>
            </div>

            <button
              onClick={() => setIsEditing(true)}
              className="btn-primary w-full md:w-auto touch-manipulation"
            >
              <span className="mr-2">✏️</span>
              Edit Profile
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
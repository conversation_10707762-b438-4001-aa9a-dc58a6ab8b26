import React from "react";

interface MetricCardProps {
  title: string;
  value: string | number;
  icon: string;
  trend: string;
  trendUp: boolean;
}

export function MetricCard({ title, value, icon, trend, trendUp }: MetricCardProps) {
  return (
    <div className="p-3 md:p-4 bg-neutral dark:bg-gray-700 rounded-lg border border-gray-100 dark:border-gray-600">
      <div className="flex items-center justify-between mb-2">
        <span className="text-lg md:text-2xl">{icon}</span>
        <span className={`text-xs md:text-sm font-medium ${trendUp ? "text-green-600 dark:text-green-400" : "text-red-600 dark:text-red-400"}`}>
          {trend}
        </span>
      </div>
      <div className="text-lg md:text-2xl font-bold text-charcoal dark:text-gray-100 mb-1">{value}</div>
      <div className="text-xs md:text-sm text-gray-600 dark:text-gray-400">{title}</div>
    </div>
  );
}
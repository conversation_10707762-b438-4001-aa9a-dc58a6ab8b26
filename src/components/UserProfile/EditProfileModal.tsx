import React, { useState } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { toast } from "sonner";

interface EditProfileModalProps {
  user: any;
  userProfile: any;
  onClose: () => void;
  onSuccess: () => void;
}

export function EditProfileModal({ user, userProfile, onClose, onSuccess }: EditProfileModalProps) {
  const updateProfile = useMutation(api.users.updateProfile);
  const currentUser = useQuery(api.auth.loggedInUser);

  const [formData, setFormData] = useState({
    name: user.name || "",
    phone: userProfile.phone || "",
    bio: userProfile.bio || "",
    department: userProfile.department || "",
    role: userProfile.role || "",
  });

  // Determine if current user can edit roles
  const canEditRoles = currentUser?.role === "master";
  const isEditingSelf = currentUser?._id === user._id;

  // Determine available roles based on current user's permissions
  const getAvailableRoles = () => {
    if (!canEditRoles) return [];

    // Master can assign any role
    if (currentUser?.role === "master") {
      return [
        { value: "staff", label: "Staff Member" },
        { value: "admin", label: "Administrator" },
        { value: "master", label: "Master" },
      ];
    }

    return [];
  };

  const availableRoles = getAvailableRoles();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await updateProfile(formData);
      onSuccess();
    } catch (error) {
      toast.error("Failed to update profile");
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-4 md:p-6 w-full max-w-md max-h-[95vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4 md:mb-6">
          <h2 className="text-lg md:text-xl font-semibold text-charcoal dark:text-gray-100">Edit Profile</h2>
          <button
            onClick={onClose}
            className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 text-xl p-2 touch-manipulation"
          >
            ✕
          </button>
        </div>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="form-label">Full Name</label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="form-input"
            />
          </div>

          <div>
            <label className="form-label">Phone Number</label>
            <input
              type="tel"
              value={formData.phone}
              onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
              className="form-input"
            />
          </div>

          <div>
            <label className="form-label">Role</label>
            {canEditRoles ? (
              <select
                value={formData.role}
                onChange={(e) => setFormData({ ...formData, role: e.target.value })}
                className="form-input"
              >
                <option value="">Select Role</option>
                {availableRoles.map((role) => (
                  <option key={role.value} value={role.value}>
                    {role.label}
                  </option>
                ))}
              </select>
            ) : (
              <div className="form-input bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed">
                {userProfile.role ? (
                  <span className="capitalize">{userProfile.role}</span>
                ) : (
                  "No role assigned"
                )}
              </div>
            )}
            {!canEditRoles && (
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Only Master users can modify roles
              </p>
            )}
          </div>

          <div>
            <label className="form-label">Department</label>
            <select
              value={formData.department}
              onChange={(e) => setFormData({ ...formData, department: e.target.value })}
              className="form-input"
            >
              <option value="">Select Department</option>
              <option value="installation">Installation</option>
              <option value="maintenance">Maintenance</option>
              <option value="sales">Sales</option>
              <option value="management">Management</option>
            </select>
          </div>

          <div>
            <label className="form-label">Bio</label>
            <textarea
              value={formData.bio}
              onChange={(e) => { setFormData({ ...formData, bio: e.target.value }); }}
              rows={3}
              className="form-input"
              placeholder="Tell us about yourself..."
            />
          </div>

          <div className="flex flex-col md:flex-row gap-3 pt-4">
            <button
              type="submit"
              className="btn-primary flex-1 touch-manipulation"
            >
              Save Changes
            </button>
            <button
              type="button"
              onClick={onClose}
              className="btn-secondary flex-1 touch-manipulation"
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
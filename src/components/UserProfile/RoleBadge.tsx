import React from "react";

interface RoleBadgeProps {
  role: string;
}

export function RoleBadge({ role }: RoleBadgeProps) {
  const roleConfig = {
    master: {
      label: "Master",
      icon: "⭐",
      className: "bg-gradient-to-r from-yellow-100 to-orange-100 dark:from-yellow-900/30 dark:to-orange-900/30 text-yellow-800 dark:text-yellow-300 border border-yellow-200 dark:border-yellow-700 shadow-md"
    },
    admin: {
      label: "Administrator",
      icon: "👑",
      className: "bg-gradient-to-r from-purple-100 to-pink-100 dark:from-purple-900/30 dark:to-pink-900/30 text-purple-800 dark:text-purple-300 border border-purple-200 dark:border-purple-700"
    },
    staff: {
      label: "Staff Member",
      icon: "👤",
      className: "bg-gradient-to-r from-blue-100 to-teal-100 dark:from-blue-900/30 dark:to-teal-900/30 text-blue-800 dark:text-blue-300 border border-blue-200 dark:border-blue-700"
    }
  };

  const config = roleConfig[role as keyof typeof roleConfig] || roleConfig.staff;

  return (
    <span className={`inline-flex items-center gap-1 px-2 md:px-3 py-1 rounded-full text-xs md:text-sm font-medium ${config.className}`}>
      <span>{config.icon}</span>
      <span className="hidden sm:inline">{config.label}</span>
    </span>
  );
}
import React from "react";
import { OverviewTab } from "./tabs/OverviewTab";
import { SettingsTab } from "./tabs/SettingsTab";
import { SecurityTab } from "./tabs/SecurityTab";
import { AdminTab } from "./tabs/AdminTab";

interface TabContentProps {
  activeTab: string;
  userProfile: any;
}

export function TabContent({ activeTab, userProfile }: TabContentProps) {
  switch (activeTab) {
    case "overview":
      return <OverviewTab userProfile={userProfile} />;
    case "settings":
      return <SettingsTab userProfile={userProfile} />;
    case "security":
      return <SecurityTab />;
    case "admin":
      return (userProfile.role === "admin" || userProfile.role === "master") ? <AdminTab /> : null;
    default:
      return <OverviewTab userProfile={userProfile} />;
  }
}
import React, { useState } from "react";
import { toast } from "sonner";
import { PasswordChangeModal } from "../modals/PasswordChangeModal";
import { TwoFactorModal } from "../modals/TwoFactorModal";

export function SecurityTab() {
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [showTwoFactorModal, setShowTwoFactorModal] = useState(false);
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(true);
  const [sessions, setSessions] = useState([
    { id: 1, device: "Chrome on Windows", location: "New York, NY", time: "2 hours ago", current: true },
    { id: 2, device: "Safari on iPhone", location: "New York, NY", time: "1 day ago", current: false },
    { id: 3, device: "Chrome on Windows", location: "New York, NY", time: "3 days ago", current: false }
  ]);

  const handleRevokeSession = (sessionId: number) => {
    setSessions(sessions.filter(session => session.id !== sessionId));
    toast.success("Session revoked successfully");
  };

  const handleToggleTwoFactor = () => {
    setTwoFactorEnabled(!twoFactorEnabled);
    setShowTwoFactorModal(true);
  };

  return (
    <div className="space-y-4 md:space-y-6">
      <div>
        <h3 className="text-base md:text-lg font-semibold text-charcoal dark:text-gray-100 mb-3 md:mb-4">Password & Security</h3>
        <div className="space-y-3 md:space-y-4">
          <button 
            onClick={() => setShowPasswordModal(true)}
            className="w-full flex items-center justify-between p-3 md:p-4 bg-neutral dark:bg-gray-700 rounded-lg border border-gray-100 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors touch-manipulation"
          >
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 md:w-10 md:h-10 bg-primary/10 dark:bg-primary/20 rounded-full flex items-center justify-center">
                <span className="text-sm md:text-base">🔑</span>
              </div>
              <div className="text-left">
                <h4 className="font-medium text-charcoal dark:text-gray-100 text-sm md:text-base">Change Password</h4>
                <p className="text-xs md:text-sm text-gray-600 dark:text-gray-400">Update your account password</p>
              </div>
            </div>
            <span className="text-gray-400">→</span>
          </button>

          <button 
            onClick={handleToggleTwoFactor}
            className="w-full flex items-center justify-between p-3 md:p-4 bg-neutral dark:bg-gray-700 rounded-lg border border-gray-100 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors touch-manipulation"
          >
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 md:w-10 md:h-10 bg-accent/10 dark:bg-accent/20 rounded-full flex items-center justify-center">
                <span className="text-sm md:text-base">📱</span>
              </div>
              <div className="text-left">
                <h4 className="font-medium text-charcoal dark:text-gray-100 text-sm md:text-base">Two-Factor Authentication</h4>
                <p className="text-xs md:text-sm text-gray-600 dark:text-gray-400">Add an extra layer of security</p>
              </div>
            </div>
            <span className={`status-badge ${twoFactorEnabled ? 'status-active' : 'status-inactive'}`}>
              {twoFactorEnabled ? 'Enabled' : 'Disabled'}
            </span>
          </button>
        </div>
      </div>

      <div>
        <h3 className="text-base md:text-lg font-semibold text-charcoal dark:text-gray-100 mb-3 md:mb-4">Login Activity</h3>
        <div className="space-y-3">
          {sessions.map((session) => (
            <div key={session.id} className="flex items-center justify-between p-3 md:p-4 bg-neutral dark:bg-gray-700 rounded-lg border border-gray-100 dark:border-gray-600">
              <div className="flex items-center gap-3 flex-1 min-w-0">
                <div className="w-8 h-8 md:w-10 md:h-10 bg-primary/10 dark:bg-primary/20 rounded-full flex items-center justify-center flex-shrink-0">
                  <span className="text-sm md:text-base">💻</span>
                </div>
                <div className="min-w-0 flex-1">
                  <h4 className="font-medium text-charcoal dark:text-gray-100 text-sm md:text-base truncate">{session.device}</h4>
                  <p className="text-xs md:text-sm text-gray-600 dark:text-gray-400 truncate">{session.location} • {session.time}</p>
                </div>
              </div>
              {session.current ? (
                <span className="status-badge status-active flex-shrink-0">Current</span>
              ) : (
                <button 
                  onClick={() => handleRevokeSession(session.id)}
                  className="text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 text-xs md:text-sm flex-shrink-0 touch-manipulation"
                >
                  Revoke
                </button>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Password Change Modal */}
      {showPasswordModal && (
        <PasswordChangeModal onClose={() => setShowPasswordModal(false)} />
      )}

      {/* Two-Factor Authentication Modal */}
      {showTwoFactorModal && (
        <TwoFactorModal 
          enabled={twoFactorEnabled}
          onClose={() => setShowTwoFactorModal(false)}
          onToggle={(enabled) => {
            setTwoFactorEnabled(enabled);
            toast.success(`Two-factor authentication ${enabled ? 'enabled' : 'disabled'}`);
          }}
        />
      )}
    </div>
  );
}
import React from "react";
import { MetricCard } from "../MetricCard";

interface OverviewTabProps {
  userProfile: any;
}

export function OverviewTab({ userProfile }: OverviewTabProps) {
  return (
    <div className="space-y-4 md:space-y-6">
      <div>
        <h3 className="text-base md:text-lg font-semibold text-charcoal dark:text-gray-100 mb-3 md:mb-4">Recent Activity</h3>
        <div className="space-y-3">
          {userProfile.recentActivity?.map((activity: any, index: number) => (
            <div key={index} className="flex items-center gap-3 p-3 md:p-4 bg-neutral dark:bg-gray-700 rounded-lg border border-gray-100 dark:border-gray-600">
              <div className="w-8 h-8 md:w-10 md:h-10 bg-primary/10 dark:bg-primary/20 rounded-full flex items-center justify-center flex-shrink-0">
                <span className="text-sm md:text-base">{activity.type === "job" ? "🔧" : "💰"}</span>
              </div>
              <div className="flex-1 min-w-0">
                <p className="font-medium text-charcoal dark:text-gray-100 text-sm md:text-base truncate">{activity.title}</p>
                <p className="text-xs md:text-sm text-gray-600 dark:text-gray-400 truncate">{activity.description}</p>
              </div>
              <span className="text-xs md:text-sm text-gray-500 dark:text-gray-400 flex-shrink-0">
                {new Date(activity.timestamp).toLocaleDateString()}
              </span>
            </div>
          )) || (
            <p className="text-gray-500 dark:text-gray-400 text-center py-8 text-sm md:text-base">No recent activity</p>
          )}
        </div>
      </div>

      <div>
        <h3 className="text-base md:text-lg font-semibold text-charcoal dark:text-gray-100 mb-3 md:mb-4">Performance Metrics</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3 md:gap-4">
          <MetricCard
            title="Jobs Completed"
            value={userProfile.stats.completedJobs}
            icon="✅"
            trend="+12%"
            trendUp={true}
          />
          <MetricCard
            title="Customer Satisfaction"
            value="4.8/5"
            icon="⭐"
            trend="+0.2"
            trendUp={true}
          />
          <MetricCard
            title="Response Time"
            value="2.3h"
            icon="⏱️"
            trend="-15min"
            trendUp={true}
          />
        </div>
      </div>
    </div>
  );
}
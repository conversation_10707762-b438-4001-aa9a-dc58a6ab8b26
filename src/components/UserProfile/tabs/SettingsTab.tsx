import React, { useState } from "react";
import { useMutation } from "convex/react";
import { api } from "../../../../convex/_generated/api";
import { toast } from "sonner";
import { ThemeSelector } from "../../ThemeToggle";

interface SettingsTabProps {
  userProfile: any;
}

export function SettingsTab({ userProfile }: SettingsTabProps) {
  const updateSettings = useMutation(api.users.updateSettings);
  const [notifications, setNotifications] = useState(userProfile.settings?.notifications || {
    email: true,
    push: true,
    sms: false
  });
  const [language, setLanguage] = useState(userProfile.settings?.language || "en");
  const [timezone, setTimezone] = useState(userProfile.settings?.timezone || "America/New_York");
  const [autoSave, setAutoSave] = useState(userProfile.settings?.autoSave || true);

  const handleNotificationChange = async (key: string, value: boolean) => {
    const newNotifications = { ...notifications, [key]: value };
    setNotifications(newNotifications);
    
    try {
      await updateSettings({ notifications: newNotifications });
      toast.success("Notification settings updated");
    } catch (error) {
      toast.error("Failed to update notification settings");
      // Revert on error
      setNotifications(notifications);
    }
  };

  const handleLanguageChange = async (newLanguage: string) => {
    setLanguage(newLanguage);
    try {
      await updateSettings({ language: newLanguage });
      toast.success("Language preference updated");
    } catch (error) {
      toast.error("Failed to update language preference");
      setLanguage(language);
    }
  };

  const handleTimezoneChange = async (newTimezone: string) => {
    setTimezone(newTimezone);
    try {
      await updateSettings({ timezone: newTimezone });
      toast.success("Timezone updated");
    } catch (error) {
      toast.error("Failed to update timezone");
      setTimezone(timezone);
    }
  };

  const handleAutoSaveChange = async (value: boolean) => {
    setAutoSave(value);
    try {
      await updateSettings({ autoSave: value });
      toast.success("Auto-save preference updated");
    } catch (error) {
      toast.error("Failed to update auto-save preference");
      setAutoSave(autoSave);
    }
  };

  return (
    <div className="space-y-4 md:space-y-6">
      <div>
        <h3 className="text-base md:text-lg font-semibold text-charcoal dark:text-gray-100 mb-3 md:mb-4">Theme Preferences</h3>
        <ThemeSelector />
      </div>

      <div>
        <h3 className="text-base md:text-lg font-semibold text-charcoal dark:text-gray-100 mb-3 md:mb-4">Language & Region</h3>
        <div className="space-y-3 md:space-y-4">
          <div className="p-3 md:p-4 bg-neutral dark:bg-gray-700 rounded-lg border border-gray-100 dark:border-gray-600">
            <label className="form-label">Language</label>
            <select
              value={language}
              onChange={(e) => handleLanguageChange(e.target.value)}
              className="form-input"
            >
              <option value="en">English</option>
              <option value="es">Spanish</option>
              <option value="fr">French</option>
            </select>
          </div>
          
          <div className="p-3 md:p-4 bg-neutral dark:bg-gray-700 rounded-lg border border-gray-100 dark:border-gray-600">
            <label className="form-label">Timezone</label>
            <select
              value={timezone}
              onChange={(e) => handleTimezoneChange(e.target.value)}
              className="form-input"
            >
              <option value="America/New_York">Eastern Time (ET)</option>
              <option value="America/Chicago">Central Time (CT)</option>
              <option value="America/Denver">Mountain Time (MT)</option>
              <option value="America/Los_Angeles">Pacific Time (PT)</option>
            </select>
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-base md:text-lg font-semibold text-charcoal dark:text-gray-100 mb-3 md:mb-4">Notification Preferences</h3>
        <div className="space-y-3 md:space-y-4">
          {[
            { key: "email", label: "Email Notifications", description: "Receive updates via email" },
            { key: "push", label: "Push Notifications", description: "Browser push notifications" },
            { key: "sms", label: "SMS Notifications", description: "Text message alerts" }
          ].map((setting) => (
            <div key={setting.key} className="flex items-center justify-between p-3 md:p-4 bg-neutral dark:bg-gray-700 rounded-lg border border-gray-100 dark:border-gray-600">
              <div className="flex-1 min-w-0 mr-4">
                <h4 className="font-medium text-charcoal dark:text-gray-100 text-sm md:text-base">{setting.label}</h4>
                <p className="text-xs md:text-sm text-gray-600 dark:text-gray-400">{setting.description}</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer touch-manipulation">
                <input
                  type="checkbox"
                  checked={notifications[setting.key as keyof typeof notifications]}
                  onChange={(e) => handleNotificationChange(setting.key, e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 dark:bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
              </label>
            </div>
          ))}
        </div>
      </div>

      <div>
        <h3 className="text-base md:text-lg font-semibold text-charcoal dark:text-gray-100 mb-3 md:mb-4">Application Preferences</h3>
        <div className="space-y-3 md:space-y-4">
          <div className="flex items-center justify-between p-3 md:p-4 bg-neutral dark:bg-gray-700 rounded-lg border border-gray-100 dark:border-gray-600">
            <div className="flex-1 min-w-0 mr-4">
              <h4 className="font-medium text-charcoal dark:text-gray-100 text-sm md:text-base">Auto-save</h4>
              <p className="text-xs md:text-sm text-gray-600 dark:text-gray-400">Automatically save changes as you work</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer touch-manipulation">
              <input
                type="checkbox"
                checked={autoSave}
                onChange={(e) => handleAutoSaveChange(e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 dark:bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
            </label>
          </div>
        </div>
      </div>
    </div>
  );
}
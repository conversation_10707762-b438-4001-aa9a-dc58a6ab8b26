import React, { useState, useRef, useEffect } from "react";
import { useTheme } from "../../hooks/useTheme";

interface StatusDropdownProps {
  currentStatus: string;
  onStatusChange: (newStatus: string) => void;
  disabled?: boolean;
  size?: "sm" | "md" | "lg";
}

const STATUS_CONFIG = {
  draft: {
    label: "Draft",
    color: "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200",
    icon: "📝",
    allowedTransitions: ["sent", "cancelled"]
  },
  sent: {
    label: "Sent",
    color: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
    icon: "📧",
    allowedTransitions: ["paid", "overdue", "cancelled"]
  },
  paid: {
    label: "Paid",
    color: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
    icon: "✅",
    allowedTransitions: [] // Paid invoices cannot be changed
  },
  overdue: {
    label: "Overdue",
    color: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
    icon: "⚠️",
    allowedTransitions: ["paid", "cancelled"]
  },
  cancelled: {
    label: "Cancelled",
    color: "bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400",
    icon: "❌",
    allowedTransitions: ["draft"] // Can reopen cancelled invoices
  }
};

export function StatusDropdown({
  currentStatus,
  onStatusChange,
  disabled = false,
  size = "md"
}: StatusDropdownProps) {
  const { isDark } = useTheme();
  const [isOpen, setIsOpen] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0 });
  const buttonRef = useRef<HTMLButtonElement>(null);

  const currentConfig = STATUS_CONFIG[currentStatus as keyof typeof STATUS_CONFIG];
  const availableTransitions = currentConfig?.allowedTransitions || [];

  const sizeClasses = {
    sm: "px-2 py-1 text-xs",
    md: "px-3 py-2 text-sm",
    lg: "px-4 py-3 text-base"
  };

  // Calculate dropdown position when opening
  useEffect(() => {
    if (isOpen && buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      setDropdownPosition({
        top: rect.bottom + window.scrollY + 4, // 4px gap
        left: rect.left + window.scrollX
      });
    }
  }, [isOpen]);

  const handleStatusChange = async (newStatus: string) => {
    if (disabled || isUpdating) return;
    
    setIsUpdating(true);
    try {
      await onStatusChange(newStatus);
      setIsOpen(false);
    } catch (error) {
      console.error("Error updating status:", error);
    } finally {
      setIsUpdating(false);
    }
  };

  if (!currentConfig) {
    return (
      <span className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">
        Unknown Status
      </span>
    );
  }

  // If no transitions are allowed or disabled, show as read-only badge
  if (availableTransitions.length === 0 || disabled) {
    return (
      <span className={`inline-flex items-center gap-1 rounded-full font-medium ${currentConfig.color} ${sizeClasses[size]}`}>
        <span>{currentConfig.icon}</span>
        <span>{currentConfig.label}</span>
      </span>
    );
  }

  return (
    <div className="relative">
      <button
        ref={buttonRef}
        onClick={() => setIsOpen(!isOpen)}
        disabled={disabled || isUpdating}
        className={`inline-flex items-center gap-1 rounded-full font-medium transition-all duration-200 hover:opacity-80 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 ${currentConfig.color} ${sizeClasses[size]} ${
          disabled || isUpdating ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'
        }`}
      >
        <span>{isUpdating ? "⏳" : currentConfig.icon}</span>
        <span>{isUpdating ? "Updating..." : currentConfig.label}</span>
        {!disabled && !isUpdating && (
          <span className="ml-1 text-xs">▼</span>
        )}
      </button>

      {isOpen && !disabled && !isUpdating && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 z-[9998]"
            onClick={() => setIsOpen(false)}
          />

          {/* Dropdown menu - using fixed positioning to escape stacking context */}
          <div
            className={`fixed z-[9999] min-w-[120px] rounded-md shadow-lg border ${
              isDark
                ? 'bg-gray-800 border-gray-600'
                : 'bg-white border-gray-200'
            }`}
            style={{
              top: `${dropdownPosition.top}px`,
              left: `${dropdownPosition.left}px`
            }}
          >
            <div className="py-1">
              {availableTransitions.map((status) => {
                const statusConfig = STATUS_CONFIG[status as keyof typeof STATUS_CONFIG];
                return (
                  <button
                    key={status}
                    onClick={() => handleStatusChange(status)}
                    className={`w-full text-left px-3 py-2 text-sm flex items-center gap-2 transition-colors ${
                      isDark
                        ? 'text-gray-200 hover:bg-gray-700'
                        : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    <span>{statusConfig.icon}</span>
                    <span>Mark as {statusConfig.label}</span>
                  </button>
                );
              })}
            </div>
          </div>
        </>
      )}
    </div>
  );
}

// Helper function to get status display info
export function getStatusInfo(status: string) {
  return STATUS_CONFIG[status as keyof typeof STATUS_CONFIG] || {
    label: "Unknown",
    color: "bg-gray-100 text-gray-600",
    icon: "❓",
    allowedTransitions: []
  };
}

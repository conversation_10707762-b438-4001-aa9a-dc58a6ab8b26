import React, { useState, useRef, useEffect, useCallback } from 'react';
import { getAddressService, AddressSuggestion, ParsedAddress, AddressServiceError } from '../../services/addressService';
import { useTheme } from '../../hooks/useTheme';

export interface AddressFormData {
  address: string;
  city: string;
  state: string;
  zipCode: string;
}

export interface AddressAutocompleteProps {
  value: string;
  onChange: (value: string) => void;
  onAddressSelect?: (address: ParsedAddress) => void;
  onFormDataChange?: (formData: Partial<AddressFormData>) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  label?: string;
  required?: boolean;
  error?: string;
  country?: string;
  proximity?: [number, number];
}

export function AddressAutocomplete({
  value,
  onChange,
  onAddressSelect,
  onFormDataChange,
  placeholder = "Enter street address...",
  disabled = false,
  className = "",
  label,
  required = false,
  error,
  country = "US",
  proximity,
}: AddressAutocompleteProps) {
  const { isDark } = useTheme();
  const [suggestions, setSuggestions] = useState<AddressSuggestion[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [apiError, setApiError] = useState<string | null>(null);
  const [debouncedValue, setDebouncedValue] = useState(value);
  
  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const addressService = getAddressService();

  // Debounce the input value
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedValue(value);
    }, 300);

    return () => clearTimeout(timer);
  }, [value]);

  // Search for addresses when debounced value changes
  useEffect(() => {
    if (debouncedValue.length >= 2 && isOpen) {
      searchAddresses(debouncedValue);
    } else {
      setSuggestions([]);
      setIsLoading(false);
    }
  }, [debouncedValue, isOpen, country, proximity]);

  const searchAddresses = useCallback(async (query: string) => {
    if (!query || query.length < 2) {
      setSuggestions([]);
      return;
    }

    setIsLoading(true);
    setApiError(null);

    try {
      const results = await addressService.searchAddresses(query, {
        limit: 5,
        country,
        proximity,
        types: ['address', 'poi'],
      });

      setSuggestions(results);
      setSelectedIndex(-1);
    } catch (error) {
      console.error('Address search error:', error);
      
      if (error instanceof AddressServiceError) {
        setApiError(error.message);
      } else {
        setApiError('Failed to search addresses. Please try again.');
      }
      
      setSuggestions([]);
    } finally {
      setIsLoading(false);
    }
  }, [addressService, country, proximity]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    onChange(newValue);
    setIsOpen(true);
    setApiError(null);
  };

  const handleInputFocus = () => {
    setIsOpen(true);
    if (value.length >= 2) {
      searchAddresses(value);
    }
  };

  const handleInputBlur = () => {
    // Delay closing to allow for suggestion clicks
    setTimeout(() => {
      setIsOpen(false);
      setSelectedIndex(-1);
    }, 150);
  };

  const handleSuggestionSelect = (suggestion: AddressSuggestion) => {
    const parsedAddress = addressService.parseAddress(suggestion);
    
    // Update the input value with the full address
    onChange(suggestion.place_name);
    
    // Call the address select callback
    if (onAddressSelect) {
      onAddressSelect(parsedAddress);
    }

    // Call the form data change callback to update other fields
    if (onFormDataChange) {
      onFormDataChange({
        address: parsedAddress.street,
        city: parsedAddress.city,
        state: parsedAddress.state,
        zipCode: parsedAddress.zipCode,
      });
    }

    setIsOpen(false);
    setSelectedIndex(-1);
    setSuggestions([]);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen || suggestions.length === 0) {
      return;
    }

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < suggestions.length - 1 ? prev + 1 : 0
        );
        break;
      
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev > 0 ? prev - 1 : suggestions.length - 1
        );
        break;
      
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && selectedIndex < suggestions.length) {
          handleSuggestionSelect(suggestions[selectedIndex]);
        }
        break;
      
      case 'Escape':
        setIsOpen(false);
        setSelectedIndex(-1);
        inputRef.current?.blur();
        break;
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        !inputRef.current?.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const inputClasses = `
    w-full px-3 py-2 border rounded-md transition-all duration-200
    focus:ring-2 focus:ring-blue-500 focus:border-transparent
    ${error ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}
    ${disabled ? 'bg-gray-100 dark:bg-gray-700 cursor-not-allowed' : 'bg-white dark:bg-gray-800'}
    text-gray-900 dark:text-gray-100
    placeholder-gray-500 dark:placeholder-gray-400
    ${className}
  `.trim();

  return (
    <div className="relative">
      {label && (
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-1">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      <div className="relative">
        <input
          ref={inputRef}
          type="text"
          value={value}
          onChange={handleInputChange}
          onFocus={handleInputFocus}
          onBlur={handleInputBlur}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          disabled={disabled}
          className={inputClasses}
          autoComplete="off"
          role="combobox"
          aria-expanded={isOpen}
          aria-haspopup="listbox"
          aria-autocomplete="list"
        />

        {/* Loading indicator */}
        {isLoading && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-500 border-t-transparent"></div>
          </div>
        )}
      </div>

      {/* Error message */}
      {(error || apiError) && (
        <p className="mt-1 text-sm text-red-600 dark:text-red-400">
          {error || apiError}
        </p>
      )}

      {/* Suggestions dropdown */}
      {isOpen && (suggestions.length > 0 || isLoading || apiError) && (
        <div
          ref={dropdownRef}
          className={`
            absolute top-full left-0 right-0 mt-1 z-50
            bg-white dark:bg-gray-800 
            border border-gray-200 dark:border-gray-700 
            rounded-lg shadow-lg 
            max-h-60 overflow-y-auto
          `}
        >
          {isLoading ? (
            <div className="px-4 py-3 text-center">
              <div className="flex items-center justify-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-500 border-t-transparent"></div>
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  Searching addresses...
                </span>
              </div>
            </div>
          ) : apiError ? (
            <div className="px-4 py-3 text-center">
              <p className="text-sm text-red-600 dark:text-red-400">
                {apiError}
              </p>
            </div>
          ) : suggestions.length > 0 ? (
            <div className="py-1">
              {suggestions.map((suggestion, index) => (
                <button
                  key={suggestion.id}
                  onClick={() => handleSuggestionSelect(suggestion)}
                  className={`
                    w-full text-left px-4 py-2 text-sm
                    transition-colors duration-150
                    ${index === selectedIndex
                      ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                      : 'text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700'
                    }
                  `}
                  role="option"
                  aria-selected={index === selectedIndex}
                >
                  <div className="flex items-start gap-2">
                    <span className="text-gray-400 mt-0.5">📍</span>
                    <div className="flex-1 min-w-0">
                      <div className="font-medium truncate">
                        {suggestion.text}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400 truncate">
                        {suggestion.place_name}
                      </div>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          ) : null}
        </div>
      )}
    </div>
  );
}

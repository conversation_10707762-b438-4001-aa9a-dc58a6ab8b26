import React from 'react';
import { ColumnDefinition } from './types';

interface ColumnSelectorProps {
  columns: ColumnDefinition[];
  visibleColumns: string[];
  onToggleColumn: (columnId: string) => void;
}

export const ColumnSelector: React.FC<ColumnSelectorProps> = ({
  columns,
  visibleColumns,
  onToggleColumn,
}) => {
  return (
    <div className="absolute right-0 mt-2 w-56 sm:w-64 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 z-50 max-h-[80vh] overflow-y-auto">
      <div className="py-1">
        <div className="px-3 py-2 text-sm font-semibold text-gray-700 dark:text-gray-200 border-b sticky top-0 bg-white dark:bg-gray-800">
          Show/Hide Columns
        </div>
        {columns.map(column => (
          <label
            key={column.id}
            className="flex items-center px-3 py-3 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
          >
            <input
              type="checkbox"
              checked={visibleColumns.includes(column.id)}
              onChange={() => onToggleColumn(column.id)}
              className="mr-3 h-4 w-4" // Larger checkboxes for better touch targets
            />
            <span className="text-gray-700 dark:text-gray-200">{column.header}</span>
          </label>
        ))}
      </div>
    </div>
  );
};
import React, { useState, useMemo, useCallback } from 'react';
import { useLocalStorage } from 'usehooks-ts';
import { DynamicTableProps, SortDirection, ColumnFilter } from './types';
import { TableHeader } from './TableHeader';
import { TableBody } from './TableBody';
import { Pagination } from './Pagination';
import { TableToolbar } from './TableToolbar';
import { FilterPanel } from './FilterPanel';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';

export function DynamicTable({
  data,
  columns,
  rowKey,
  onRowClick,
  selectedId,
  isLoading = false,
  tableId,
  className = '',
  searchable = false,
  pageSize = 10,
  emptyMessage = 'No data available',
  filterable = false,
  reorderable = false,
  onDelete,
  showDeleteButton = false,
  deleteConfirmMessage,
}: DynamicTableProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [sortColumn, setSortColumn] = useState<string>();
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');
  const [showFilters, setShowFilters] = useState(false);
  const [activeFilters, setActiveFilters] = useState<ColumnFilter[]>([]);
  
  // Initialize visible columns from localStorage, defaulting to all columns
  const [visibleColumns, setVisibleColumns] = useLocalStorage<string[]>(
    `table-${tableId}-columns`,
    columns.map(col => col.id) // Show ALL columns regardless of defaultVisible
  );
  
  // Initialize column order from localStorage, defaulting to original order
  const [columnOrder, setColumnOrder] = useLocalStorage<string[]>(
    `table-${tableId}-order`,
    columns.map(col => col.id)
  );

  // Initialize filters from localStorage
  const [savedFilters, setSavedFilters] = useLocalStorage<ColumnFilter[]>(
    `table-${tableId}-filters`,
    []
  );

  // Use saved filters on initial load
  React.useEffect(() => {
    if (savedFilters.length > 0) {
      setActiveFilters(savedFilters);
    }
  }, []);

  // Save filters when they change
  React.useEffect(() => {
    setSavedFilters(activeFilters);
  }, [activeFilters]);

  // Toggle column visibility
  const toggleColumn = (columnId: string) => {
    setVisibleColumns(prev => {
      // Don't allow hiding all columns
      if (prev.includes(columnId)) {
        if (prev.length === 1) return prev;
        return prev.filter(id => id !== columnId);
      }
      return [...prev, columnId];
    });
  };

  // Move a column to a new position
  const moveColumn = useCallback((dragIndex: number, hoverIndex: number) => {
    const dragId = columnOrder[dragIndex];
    const newColumnOrder = [...columnOrder];
    newColumnOrder.splice(dragIndex, 1);
    newColumnOrder.splice(hoverIndex, 0, dragId);
    setColumnOrder(newColumnOrder);
  }, [columnOrder, setColumnOrder]);

  // Apply column ordering and visibility filtering
  const orderedVisibleColumns = useMemo(() => {
    // Start with the ordered column IDs
    return columnOrder
      // Filter to only include visible columns
      .filter(id => visibleColumns.includes(id))
      // Map back to the full column objects
      .map(id => columns.find(col => col.id === id))
      // Filter out any undefined results (shouldn't happen)
      .filter(Boolean) as typeof columns;
  }, [columns, visibleColumns, columnOrder]);

  // Filter and sort data
  const filteredData = useMemo(() => {
    let filtered = [...data];
    
    // Apply column-specific filters
    if (activeFilters.length > 0) {
      filtered = filtered.filter(row => {
        return activeFilters.every(filter => {
          const column = columns.find(col => col.id === filter.id);
          if (!column || !column.getFilterValue) return true;
          
          const value = column.getFilterValue(row);
          if (value === null || value === undefined) return false;
          
          const filterValue = filter.value.toLowerCase();
          return String(value).toLowerCase().includes(filterValue);
        });
      });
    }
    
    // Apply global search
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(row =>
        Object.values(row).some(value => {
          if (value === null || value === undefined) return false;
          return String(value).toLowerCase().includes(term);
        })
      );
    }

    // Apply sorting
    if (sortColumn) {
      filtered.sort((a, b) => {
        const valueA = a[sortColumn];
        const valueB = b[sortColumn];
        
        // Handle null values
        if (valueA === null || valueA === undefined) return 1;
        if (valueB === null || valueB === undefined) return -1;
        if (valueA === valueB) return 0;
        
        let comparison;
        if (typeof valueA === 'string' && typeof valueB === 'string') {
          comparison = valueA.localeCompare(valueB);
        } else {
          comparison = valueA < valueB ? -1 : 1;
        }
        
        return sortDirection === 'asc' ? comparison : -comparison;
      });
    }

    return filtered;
  }, [data, searchTerm, sortColumn, sortDirection, activeFilters, columns]);

  // Paginate data
  const paginatedData = useMemo(() => {
    const start = (currentPage - 1) * pageSize;
    return filteredData.slice(start, start + pageSize);
  }, [filteredData, currentPage, pageSize]);

  // Handle sort column change
  const handleSort = (columnId: string) => {
    setSortDirection(prev => 
      sortColumn === columnId && prev === 'asc' ? 'desc' : 'asc'
    );
    setSortColumn(columnId);
  };

  // Add or update a filter
  const handleFilterChange = (columnId: string, value: string) => {
    setActiveFilters(prev => {
      const existing = prev.findIndex(f => f.id === columnId);
      if (existing >= 0) {
        // Update existing filter
        if (!value) {
          // Remove filter if value is empty
          return prev.filter(f => f.id !== columnId);
        }
        const newFilters = [...prev];
        newFilters[existing] = { id: columnId, value };
        return newFilters;
      } else {
        // Add new filter if value is not empty
        if (!value) return prev;
        return [...prev, { id: columnId, value }];
      }
    });
    setCurrentPage(1); // Reset to first page when filter changes
  };

  // Reset all filters
  const handleResetFilters = () => {
    setActiveFilters([]);
  };

  // Reset page when search term changes
  React.useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  const filterableColumns = columns.filter(col => col.filterable);

  return (
    <DndProvider backend={HTML5Backend}>
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow ${className}`}>
        <TableToolbar
          searchable={searchable}
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          columns={columns}
          visibleColumns={visibleColumns}
          onToggleColumn={toggleColumn}
          filterable={filterable && filterableColumns.length > 0}
          onToggleFilters={() => setShowFilters(!showFilters)}
          activeFilters={activeFilters.length}
          onResetFilters={handleResetFilters}
        />

        {filterable && showFilters && (
          <FilterPanel
            columns={filterableColumns}
            activeFilters={activeFilters}
            onFilterChange={handleFilterChange}
            onClose={() => setShowFilters(false)}
          />
        )}

        <div className="overflow-x-auto overflow-y-visible">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <TableHeader
              columns={orderedVisibleColumns}
              sortColumn={sortColumn}
              sortDirection={sortDirection}
              onSort={handleSort}
              reorderable={reorderable}
              onMoveColumn={moveColumn}
              showDeleteButton={showDeleteButton && !!onDelete}
            />
            <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
              <TableBody
                data={paginatedData}
                columns={orderedVisibleColumns}
                rowKey={rowKey}
                selectedId={selectedId}
                onRowClick={onRowClick}
                isLoading={isLoading}
                emptyMessage={emptyMessage}
                onDelete={onDelete}
                showDeleteButton={showDeleteButton}
                deleteConfirmMessage={deleteConfirmMessage}
              />
            </tbody>
          </table>
        </div>

        {filteredData.length > pageSize && (
          <Pagination
            currentPage={currentPage}
            totalPages={Math.ceil(filteredData.length / pageSize)}
            totalItems={filteredData.length}
            pageSize={pageSize}
            onPageChange={setCurrentPage}
          />
        )}
      </div>
    </DndProvider>
  );
}
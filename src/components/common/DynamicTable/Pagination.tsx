import React from 'react';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  pageSize: number;
  onPageChange: (page: number) => void;
}

export const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  totalItems,
  pageSize,
  onPageChange,
}) => {
  // Function to generate visible page numbers with ellipsis for long ranges
  const getPageNumbers = () => {
    // For mobile, show fewer page numbers
    const isMobile = window.innerWidth < 640;
    const maxVisiblePages = isMobile ? 3 : 5;
    
    if (totalPages <= maxVisiblePages) {
      return Array.from({ length: totalPages }, (_, i) => i + 1);
    }

    const pages = [];
    const halfVisible = Math.floor(maxVisiblePages / 2);
    
    // Always show first page
    pages.push(1);
    
    // Calculate range around current page
    const startPage = Math.max(2, currentPage - halfVisible);
    const endPage = Math.min(totalPages - 1, currentPage + halfVisible);
    
    // Add ellipsis after first page if needed
    if (startPage > 2) {
      pages.push('ellipsis-start');
    }
    
    // Add pages around current page
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    
    // Add ellipsis before last page if needed
    if (endPage < totalPages - 1) {
      pages.push('ellipsis-end');
    }
    
    // Always show last page
    if (totalPages > 1) {
      pages.push(totalPages);
    }
    
    return pages;
  };

  return (
    <div className="px-2 sm:px-4 py-3 flex flex-col sm:flex-row gap-2 items-center justify-between border-t border-gray-200">
      <div className="text-xs sm:text-sm text-gray-700 dark:text-gray-300">
        Showing {(currentPage - 1) * pageSize + 1} to {Math.min(currentPage * pageSize, totalItems)} of {totalItems}
      </div>
      
      <nav className="flex gap-1">
        <button
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage === 1}
          className="btn-secondary px-3 py-2 rounded disabled:opacity-50 text-sm touch-manipulation"
          aria-label="Previous page"
        >
          Prev
        </button>
        
        <div className="hidden sm:flex gap-1">
          {getPageNumbers().map((page, index) => 
            typeof page === 'number' ? (
              <button
                key={`page-${page}`}
                onClick={() => onPageChange(page)}
                className={`btn-secondary px-3 py-2 rounded touch-manipulation
                  ${currentPage === page ? 'bg-primary text-white' : ''}`}
              >
                {page}
              </button>
            ) : (
              <span key={`ellipsis-${index}`} className="px-1 self-center">...</span>
            )
          )}
        </div>
        
        <div className="sm:hidden flex items-center px-2">
          <span className="text-sm">{currentPage} / {totalPages}</span>
        </div>
        
        <button
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          className="btn-secondary px-3 py-2 rounded disabled:opacity-50 text-sm touch-manipulation"
          aria-label="Next page"
        >
          Next
        </button>
      </nav>
    </div>
  );
};

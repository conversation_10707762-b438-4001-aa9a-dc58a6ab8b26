import React, { useRef } from 'react';
import { ColumnDefinition, SortDirection } from './types';
import { useDrag, useDrop, DropTargetMonitor } from 'react-dnd';

interface TableHeaderProps {
  columns: ColumnDefinition[];
  sortColumn?: string;
  sortDirection?: SortDirection;
  onSort: (columnId: string) => void;
  reorderable?: boolean;
  onMoveColumn?: (dragIndex: number, hoverIndex: number) => void;
  showDeleteButton?: boolean;
}

interface DragItem {
  index: number;
  id: string;
  type: string;
}

const COLUMN_TYPE = 'column';

const DraggableHeaderCell: React.FC<{
  column: ColumnDefinition;
  index: number;
  isSorted: boolean;
  sortDirection?: SortDirection;
  onSort: () => void;
  reorderable: boolean;
  onMoveColumn: (dragIndex: number, hoverIndex: number) => void;
}> = ({ column, index, isSorted, sortDirection, onSort, reorderable, onMoveColumn }) => {
  const ref = useRef<HTMLTableCellElement>(null);

  const [{ isDragging }, drag] = useDrag({
    type: COLUMN_TYPE,
    item: { index, id: column.id, type: COLUMN_TYPE },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
    canDrag: () => reorderable,
  });

  const [{ isOver }, drop] = useDrop<DragItem, void, { isOver: boolean }>({
    accept: COLUMN_TYPE,
    collect: (monitor) => ({
      isOver: monitor.isOver(),
    }),
    hover(item: DragItem, monitor) {
      if (!ref.current) {
        return;
      }

      const dragIndex = item.index;
      const hoverIndex = index;

      // Don't replace items with themselves
      if (dragIndex === hoverIndex) {
        return;
      }

      // Move the column
      onMoveColumn(dragIndex, hoverIndex);
      
      // Note: we're mutating the monitor item here!
      // Generally it's better to avoid mutations,
      // but it's good here for the sake of performance
      // to avoid expensive index searches.
      item.index = hoverIndex;
    },
    canDrop: () => reorderable,
  });

  // Set up drag and drop refs
  if (reorderable) {
    drag(drop(ref));
  }

  return (
    <th
      ref={ref}
      onClick={() => column.sortable && onSort()}
      className={`px-6 py-3 text-left text-xs font-medium text-gray-500 
        dark:text-gray-400 uppercase tracking-wider select-none
        ${column.sortable ? 'cursor-pointer' : ''}
        ${reorderable ? 'cursor-move' : ''}
        ${isDragging ? 'opacity-50' : ''}
        ${isOver ? 'bg-blue-100 dark:bg-blue-900' : ''}`}
      style={{ width: column.width }}
    >
      <div className="flex items-center gap-2">
        {reorderable && <span className="text-gray-400">⋮⋮</span>}
        {column.header}
        {column.sortable && isSorted && (
          <span>{sortDirection === 'asc' ? '▲' : '▼'}</span>
        )}
      </div>
    </th>
  );
};

export const TableHeader: React.FC<TableHeaderProps> = ({
  columns,
  sortColumn,
  sortDirection,
  onSort,
  reorderable = false,
  onMoveColumn = () => {},
  showDeleteButton = false,
}) => (
  <thead className="bg-bg-surface">
    <tr>
      {columns.map((column, index) => (
        <DraggableHeaderCell
          key={column.id}
          column={column}
          index={index}
          isSorted={sortColumn === column.id}
          sortDirection={sortDirection}
          onSort={() => onSort(column.id)}
          reorderable={reorderable}
          onMoveColumn={onMoveColumn}
        />
      ))}
      {showDeleteButton && (
        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
          Actions
        </th>
      )}
    </tr>
  </thead>
);
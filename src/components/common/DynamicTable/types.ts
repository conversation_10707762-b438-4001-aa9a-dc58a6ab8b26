export interface ColumnDefinition {
  id: string;
  header: string;
  accessor: (row: any) => React.ReactNode;
  sortable?: boolean;
  width?: string;
  defaultVisible?: boolean;  // Controls initial visibility
  filterable?: boolean;      // Whether this column can be filtered
  getFilterValue?: (row: any) => string | number | boolean | null; // Extract value for filtering
}

export interface ColumnFilter {
  id: string;
  value: string;
}

export interface DynamicTableProps {
  data: any[];
  columns: ColumnDefinition[];
  rowKey: string;
  onRowClick?: (row: any) => void;
  selectedId?: string;
  isLoading?: boolean;
  tableId: string;
  className?: string;
  searchable?: boolean;
  pageSize?: number;
  emptyMessage?: string;
  filterable?: boolean;  // Whether table supports filtering
  reorderable?: boolean; // Whether columns can be reordered
  onDelete?: (row: any) => void; // Function to handle row deletion
  showDeleteButton?: boolean; // Whether to show delete buttons
  deleteConfirmMessage?: string; // Optional confirmation message before deletion
}

export type SortDirection = 'asc' | 'desc';
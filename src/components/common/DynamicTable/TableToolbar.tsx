import React, { useState, useRef, useEffect } from 'react';
import { ColumnDefinition } from './types';
import { ColumnSelector } from './ColumnSelector';

interface TableToolbarProps {
  searchable?: boolean;
  searchTerm: string;
  onSearchChange: (term: string) => void;
  columns: ColumnDefinition[];
  visibleColumns: string[];
  onToggleColumn: (columnId: string) => void;
  filterable?: boolean;
  onToggleFilters?: () => void;
  activeFilters?: number;
  onResetFilters?: () => void;
}

export const TableToolbar: React.FC<TableToolbarProps> = ({
  searchable,
  searchTerm,
  onSearchChange,
  columns,
  visibleColumns,
  onToggleColumn,
  filterable = false,
  onToggleFilters = () => {},
  activeFilters = 0,
  onResetFilters = () => {},
}) => {
  const [showColumnSelector, setShowColumnSelector] = useState(false);
  const selectorRef = useRef<HTMLDivElement>(null);

  // Close the dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (selectorRef.current && !selectorRef.current.contains(event.target as Node)) {
        setShowColumnSelector(false);
      }
    }

    if (showColumnSelector) {
      document.addEventListener('mousedown', handleClickOutside);
    }
    
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showColumnSelector]);

  return (
    <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex flex-wrap gap-2 justify-between items-center">
      {searchable && (
        <input
          type="text"
          placeholder="Search..."
          className="px-4 py-2 rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 flex-grow max-w-sm"
          value={searchTerm}
          onChange={e => onSearchChange(e.target.value)}
        />
      )}
      
      <div className="flex gap-2">
        {filterable && (
          <div className="flex items-center">
            <button
              className={`px-4 py-2 text-sm font-medium rounded-md flex items-center gap-1
                ${activeFilters > 0 
                  ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200' 
                  : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 border border-gray-300 dark:border-gray-600'}`}
              onClick={onToggleFilters}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"/>
              </svg>
              Filters
              {activeFilters > 0 && (
                <span className="ml-1 px-2 py-0.5 text-xs rounded-full bg-blue-500 dark:bg-blue-700 text-white">
                  {activeFilters}
                </span>
              )}
            </button>
            
            {activeFilters > 0 && (
              <button
                className="p-2 text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200"
                onClick={onResetFilters}
                title="Reset filters"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <circle cx="12" cy="12" r="10"/>
                  <line x1="15" y1="9" x2="9" y2="15"/>
                  <line x1="9" y1="9" x2="15" y2="15"/>
                </svg>
              </button>
            )}
          </div>
        )}
        
        <div className="relative" ref={selectorRef}>
          <button
            className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center gap-1"
            onClick={() => setShowColumnSelector(!showColumnSelector)}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <line x1="3" y1="12" x2="21" y2="12"/>
              <line x1="3" y1="6" x2="21" y2="6"/>
              <line x1="3" y1="18" x2="21" y2="18"/>
            </svg>
            Columns
          </button>
          
          {showColumnSelector && (
            <ColumnSelector
              columns={columns}
              visibleColumns={visibleColumns}
              onToggleColumn={onToggleColumn}
            />
          )}
        </div>
      </div>
    </div>
  );
};
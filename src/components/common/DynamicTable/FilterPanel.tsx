import React from 'react';
import { ColumnDefinition, ColumnFilter } from './types';

interface FilterPanelProps {
  columns: ColumnDefinition[];
  activeFilters: ColumnFilter[];
  onFilterChange: (columnId: string, value: string) => void;
  onClose: () => void;
}

export const FilterPanel: React.FC<FilterPanelProps> = ({
  columns,
  activeFilters,
  onFilterChange,
  onClose,
}) => {
  // Get current filter value for a column
  const getFilterValue = (columnId: string): string => {
    const filter = activeFilters.find(f => f.id === columnId);
    return filter ? filter.value : '';
  };

  return (
    <div className="border-b border-gray-200 dark:border-gray-700 p-4 bg-gray-50 dark:bg-gray-850">
      <div className="flex justify-between items-center mb-3">
        <h3 className="font-medium text-gray-700 dark:text-gray-300">Table Filters</h3>
        <button
          onClick={onClose}
          className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"/>
            <line x1="6" y1="6" x2="18" y2="18"/>
          </svg>
        </button>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {columns.map(column => (
          <div key={column.id} className="flex flex-col">
            <label htmlFor={`filter-${column.id}`} className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {column.header}
            </label>
            <input
              id={`filter-${column.id}`}
              type="text"
              className="w-full px-3 py-2 rounded-md border border-gray-300 dark:border-gray-600 
                bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100
                focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder={`Filter by ${column.header.toLowerCase()}...`}
              value={getFilterValue(column.id)}
              onChange={(e) => onFilterChange(column.id, e.target.value)}
            />
          </div>
        ))}
      </div>
    </div>
  );
};
import React from 'react';
import { ColumnDefinition } from './types';

interface TableBodyProps {
  data: any[];
  columns: ColumnDefinition[];
  rowKey: string;
  selectedId?: string;
  onRowClick?: (row: any) => void;
  isLoading?: boolean;
  emptyMessage?: string;
  onDelete?: (row: any) => void;
  showDeleteButton?: boolean;
  deleteConfirmMessage?: string;
}

export const TableBody: React.FC<TableBodyProps> = ({
  data,
  columns,
  rowKey,
  selectedId,
  onRowClick,
  isLoading,
  emptyMessage = 'No data available',
  onDelete,
  showDeleteButton = false,
  deleteConfirmMessage = 'Are you sure you want to delete this item?',
}) => {
  if (isLoading) {
    return (
      <tr>
        <td colSpan={columns.length + (showDeleteButton ? 1 : 0)} className="px-6 py-8 text-center">
          <div className="inline-block animate-pulse bg-gray-200 dark:bg-gray-700 h-6 w-24 rounded" />
        </td>
      </tr>
    );
  }

  if (!data.length) {
    return (
      <tr>
        <td colSpan={columns.length + (showDeleteButton ? 1 : 0)} className="px-6 py-8 text-center text-gray-500">
          {emptyMessage}
        </td>
      </tr>
    );
  }

  const handleDelete = (e: React.MouseEvent, row: any) => {
    e.stopPropagation(); // Prevent row click event
    if (window.confirm(deleteConfirmMessage)) {
      onDelete?.(row);
    }
  };

  return (
    <>
      {data.map(row => (
        <tr
          key={row[rowKey]}
          onClick={() => onRowClick?.(row)}
          className={`hover:bg-bg-surface/80 
            ${onRowClick ? 'cursor-pointer' : ''}
            ${selectedId === row[rowKey] ? 'bg-bg-surface' : ''}`}
        >
          {columns.map(column => (
            <td
              key={`${row[rowKey]}-${column.id}`}
              className={`px-3 py-3 md:px-6 md:py-4 text-sm text-ellipsis ${
                column.id === 'status' ? 'overflow-visible' : 'overflow-hidden'
              }`}
              style={{
                maxWidth: column.width || '150px',
                minWidth: '50px',
                whiteSpace: 'normal', // Allow text wrapping on small screens
              }}
            >
              <div className="break-words">
                {column.accessor(row)}
              </div>
            </td>
          ))}
          {showDeleteButton && onDelete && (
            <td className="px-3 py-3 md:px-6 md:py-4 text-sm text-right">
              <button
                onClick={(e) => handleDelete(e, row)}
                className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 focus:outline-none"
                aria-label="Delete"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </button>
            </td>
          )}
        </tr>
      ))}
    </>
  );
};
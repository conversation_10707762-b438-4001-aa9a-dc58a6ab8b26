# DynamicTable Component

A flexible, feature-rich table component with column selection, sorting, filtering, pagination, and row deletion.

## Features

- Column visibility toggle
- Sorting by column
- Pagination
- Search filtering
- Row deletion
- Responsive design
- Dark mode support
- Persistent column preferences with localStorage

## Usage Example

```tsx
import React from 'react';
import { DynamicTable } from './components/common/DynamicTable';

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  lastLogin: string;
  status: 'active' | 'inactive';
}

const UsersTable: React.FC = () => {
  const users: User[] = [
    { 
      id: '1', 
      name: '<PERSON>', 
      email: '<EMAIL>', 
      role: 'Admin',
      lastLogin: '2023-01-15',
      status: 'active'
    },
    // ...more users
  ];

  const columns = [
    { 
      id: 'name', 
      header: 'Name', 
      accessor: (row: User) => row.name,
      sortable: true,
      defaultVisible: true
    },
    { 
      id: 'email', 
      header: 'Email', 
      accessor: (row: User) => row.email,
      defaultVisible: true
    },
    { 
      id: 'role', 
      header: 'Role', 
      accessor: (row: User) => row.role,
      defaultVisible: true
    },
    { 
      id: 'lastLogin', 
      header: 'Last Login', 
      accessor: (row: User) => new Date(row.lastLogin).toLocaleDateString(),
      defaultVisible: false
    },
    { 
      id: 'status', 
      header: 'Status', 
      accessor: (row: User) => (
        <span className={row.status === 'active' ? 'text-green-500' : 'text-red-500'}>
          {row.status}
        </span>
      ),
      defaultVisible: true
    }
  ];

  const handleDelete = (user: User) => {
    console.log('Deleting user:', user);
    // Call your API to delete the user
    // Then update your state
  };

  return (
    <DynamicTable
      data={users}
      columns={columns}
      rowKey="id"
      tableId="users-table"
      searchable
      pageSize={10}
      onRowClick={row => console.log('User clicked:', row)}
      onDelete={handleDelete}
      showDeleteButton={true}
      deleteConfirmMessage="Are you sure you want to delete this user?"
    />
  );
};

export default UsersTable;
```

## Props

| Prop | Type | Description |
|------|------|-------------|
| data | any[] | Array of data to display in the table |
| columns | ColumnDefinition[] | Column definitions for the table |
| rowKey | string | Property name to use as unique key for rows |
| tableId | string | Unique ID for the table (used for localStorage) |
| onRowClick | function | Optional callback when a row is clicked |
| selectedId | string | Optional ID of selected row |
| isLoading | boolean | Show loading state |
| className | string | Additional CSS classes |
| searchable | boolean | Enable search functionality |
| pageSize | number | Number of rows per page |
| emptyMessage | string | Message to display when no data is available |
| onDelete | function | Optional callback to handle row deletion |
| showDeleteButton | boolean | Whether to show delete buttons (default: false) |
| deleteConfirmMessage | string | Confirmation message before deletion |

## Column Definition

| Property | Type | Description |
|----------|------|-------------|
| id | string | Unique identifier for the column |
| header | string | Display name in the column header |
| accessor | function | Function to get the cell value from a row |
| sortable | boolean | Whether the column is sortable |
| width | string | Optional CSS width for the column |
| defaultVisible | boolean | Whether column is visible by default |
import { useState, useEffect } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { toast } from "sonner";
import { useTheme } from "../hooks/useTheme";
import { DynamicTable, ColumnDefinition } from "./common/DynamicTable/DynamicTable";

// Helper function to get category color
const getCategoryColor = (category: string) => {
  switch (category) {
    case "equipment": return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
    case "service": return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
    case "parts": return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";
    default: return "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300";
  }
};

// Product management component with CRUD operations
export function Products({ selectedItemId }: { selectedItemId?: string | null }) {
  const products = useQuery(api.products.list);
  const deleteProduct = useMutation(api.products.remove);
  const [selectedProduct, setSelectedProduct] = useState<any>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [categoryFilter, setCategoryFilter] = useState("all");

  // Auto-select product when selectedItemId is provided from search navigation
  useEffect(() => {
    if (selectedItemId && products) {
      const productToSelect = products.find(product => product._id === selectedItemId);
      if (productToSelect) {
        setSelectedProduct(productToSelect);
        setIsEditing(false); // Show detail view, not edit form
      }
    }
  }, [selectedItemId, products]);

  // Function to handle product deletion
  const handleRowDeletion = async (product: any) => {
    try {
      await deleteProduct({ id: product._id });
      toast.success(`${product.name} has been deleted`);
      // If the deleted product was selected, clear the selection
      if (selectedProduct && selectedProduct._id === product._id) {
        setSelectedProduct(null);
        setIsEditing(false);
      }
    } catch (error) {
      toast.error("Failed to delete product");
      console.error("Delete error:", error);
    }
  };

  // Define columns for DynamicTable
  const columns: ColumnDefinition[] = [
    {
      id: "name",
      header: "Product Name",
      accessor: (product) => (
        <div>
          <div className="text-sm font-medium dark-text">{product.name}</div>
          {product.sku && (
            <div className="text-sm dark-text-muted">SKU: {product.sku}</div>
          )}
        </div>
      ),
      sortable: true,
      filterable: true,
      getFilterValue: (product) => product.name,
      defaultVisible: true
    },
    {
      id: "category",
      header: "Category",
      accessor: (product) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getCategoryColor(product.category)}`}>
          {product.category}
        </span>
      ),
      sortable: true,
      filterable: true,
      getFilterValue: (product) => product.category,
      defaultVisible: true
    },
    {
      id: "price",
      header: "Price",
      accessor: (product) => (
        <div className="text-sm font-bold text-accent">${product.price.toFixed(2)}</div>
      ),
      sortable: true,
      defaultVisible: true
    },
    {
      id: "description",
      header: "Description",
      accessor: (product) => (
        <div className="text-sm dark-text-muted truncate max-w-xs">{product.description}</div>
      ),
      sortable: true,
      filterable: true,
      getFilterValue: (product) => product.description,
      defaultVisible: true
    },
    {
      id: "status",
      header: "Status",
      accessor: (product) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
          product.isActive 
            ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200" 
            : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
        }`}>
          {product.isActive ? "Active" : "Inactive"}
        </span>
      ),
      sortable: true,
      filterable: true,
      getFilterValue: (product) => product.isActive ? "Active" : "Inactive",
      defaultVisible: true
    }
  ];

  // Filter products based on category filter
  const filteredProducts = products?.filter(product => {
    return categoryFilter === "all" || product.category === categoryFilter;
  }) || [];

  const handleEdit = (product: any) => {
    setSelectedProduct(product);
    setIsEditing(true);
  };

  const handleAdd = () => {
    setSelectedProduct(null);
    setIsEditing(true);
  };

  if (!products) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-[1fr_400px] gap-6 h-full">
      {/* Left Column - Products List */}
      <div className="space-y-4">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold dark-text">Products & Services</h1>
            <p className="dark-text-muted text-sm md:text-base">Manage your HVAC products and services catalog</p>
          </div>
          <button
            onClick={handleAdd}
            className="btn-accent w-full sm:w-auto touch-manipulation"
          >
            <span className="mr-2">➕</span>
            Add Product
          </button>
        </div>

        {/* Category Filter */}
        <div className="card p-4">
          <div className="flex gap-2 flex-wrap">
            {["all", "equipment", "service", "parts"].map((category) => (
              <button
                key={category}
                onClick={() => setCategoryFilter(category)}
                className={`px-3 md:px-4 py-2 rounded-lg capitalize transition-colors text-sm md:text-base touch-manipulation ${
                  categoryFilter === category
                    ? "bg-primary text-white"
                    : "bg-neutral text-charcoal hover:bg-gray-200"
                }`}
              >
                {category === "all" ? "All Categories" : category}
              </button>
            ))}
          </div>
        </div>

        {/* Products Table using DynamicTable */}
        <div className="card overflow-hidden">
          <DynamicTable
            data={filteredProducts}
            columns={columns}
            rowKey="_id"
            tableId="products-table"
            onRowClick={(product) => {
              setSelectedProduct(product);
              setIsEditing(false);
            }}
            selectedId={selectedProduct?._id}
            searchable={true}
            pageSize={10}
            filterable={true}
            emptyMessage="No products found"
            className="w-full"
            reorderable={true}
            onDelete={handleRowDeletion}
            showDeleteButton={true}
            deleteConfirmMessage="Are you sure you want to delete this product?"
          />
        </div>
      </div>

      {/* Right Column - Preview/Edit Panel */}
      <div className="card p-6 h-full">
        {isEditing ? (
          <ProductForm
            product={selectedProduct}
            onClose={() => {
              setSelectedProduct(null);
              setIsEditing(false);
            }}
            onSuccess={() => {
              setSelectedProduct(null);
              setIsEditing(false);
            }}
          />
        ) : selectedProduct ? (
          <ProductPreview product={selectedProduct} onEdit={() => setIsEditing(true)} />
        ) : (
          <div className="flex flex-col items-center justify-center h-full text-center">
            <h3 className="text-xl font-semibold dark-text mb-2">
              Select a product to view details
            </h3>
            <p className="dark-text-muted">
              Click on a product in the list to see more information or edit it
            </p>
          </div>
        )}
      </div>
    </div>
  );
}

// Product preview component
function ProductPreview({ product, onEdit }: { product: any; onEdit: () => void }) {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold dark-text">{product.name}</h2>
        <button
          onClick={onEdit}
          className="btn-primary text-sm px-4 py-2"
        >
          Edit
        </button>
      </div>
      
      <div className="space-y-4">
        <div>
          <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">Category</h3>
          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getCategoryColor(product.category)}`}>
            {product.category}
          </span>
        </div>
        
        <div>
          <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">Price</h3>
          <p className="text-2xl font-bold text-accent">${product.price.toFixed(2)}</p>
        </div>
        
        {product.sku && (
          <div>
            <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">SKU</h3>
            <p className="dark-text">{product.sku}</p>
          </div>
        )}
        
        <div>
          <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">Description</h3>
          <p className="dark-text whitespace-pre-line">{product.description}</p>
        </div>
        
        <div>
          <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">Status</h3>
          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
            product.isActive 
              ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200" 
              : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
          }`}>
            {product.isActive ? "Active" : "Inactive"}
          </span>
        </div>
      </div>
    </div>
  );
}

// Product form component for create/edit operations
function ProductForm({ product, onClose, onSuccess }: {
  product: any;
  onClose: () => void;
  onSuccess: () => void;
}) {
  const createProduct = useMutation(api.products.create);
  const updateProduct = useMutation(api.products.update);
  const { isDark } = useTheme();
  const [formData, setFormData] = useState({
    name: product?.name || "",
    description: product?.description || "",
    price: product?.price || "",
    category: product?.category || "equipment",
    sku: product?.sku || "",
    isActive: product?.isActive ?? true,
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Basic validation
    if (!formData.name.trim() || !formData.description.trim()) {
      toast.error("Please fill in all required fields");
      return;
    }
    
    try {
      const submitData = {
        ...formData,
        price: Number(formData.price),
      };

      if (product) {
        await updateProduct({ id: product._id, ...submitData });
        toast.success("Product updated successfully");
      } else {
        await createProduct(submitData);
        toast.success("Product created successfully");
      }
      onSuccess();
    } catch (error) {
      toast.error("Failed to save product");
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold dark-text">
          {product ? "Edit Product" : "Add Product"}
        </h2>
        <button
          onClick={onClose}
          className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 text-xl p-2 touch-manipulation transition-colors duration-300"
        >
          ✕
        </button>
      </div>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Product Name *
          </label>
          <input
            type="text"
            required
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Description *
          </label>
          <textarea
            required
            value={formData.description}
            onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Price *
          </label>
          <input
            type="number"
            step="0.01"
            min="0"
            required
            value={formData.price}
            onChange={(e) => setFormData({ ...formData, price: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Category *
          </label>
          <select
            required
            value={formData.category}
            onChange={(e) => setFormData({ ...formData, category: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="equipment">Equipment</option>
            <option value="service">Service</option>
            <option value="parts">Parts</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            SKU
          </label>
          <input
            type="text"
            value={formData.sku}
            onChange={(e) => setFormData({ ...formData, sku: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
        </div>

        {product && (
          <div className="flex items-center">
            <input
              type="checkbox"
              id="isActive"
              checked={formData.isActive}
              onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
              className="mr-2 w-4 h-4 text-blue-600 bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 rounded focus:ring-blue-500 focus:ring-2"
            />
            <label htmlFor="isActive" className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Active
            </label>
          </div>
        )}

        <div className="flex gap-3 pt-4">
          <button
            type="submit"
            className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
          >
            {product ? "Update" : "Create"}
          </button>
          <button
            type="button"
            onClick={onClose}
            className={`flex-1 ${isDark ? 'bg-gray-600 text-white hover:bg-gray-700' : 'bg-gray-300 text-gray-700 hover:bg-gray-400'} py-2 px-4 rounded-md transition-colors`}
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  );
}

import { useState } from "react";
import { toast } from "../utils/toast";

interface QuickActionButtonProps {
  icon: string;
  label: string;
  description: string;
  action: "customer" | "job" | "invoice" | "product";
}

function QuickActionButton({ icon, label, description, action }: QuickActionButtonProps) {
  const [isPressed, setIsPressed] = useState(false);
  const [showModal, setShowModal] = useState(false);

  const handleClick = () => {
    setIsPressed(true);
    setTimeout(() => setIsPressed(false), 150);
    toast.success(`Opening ${label.toLowerCase()} form...`);
    setShowModal(true);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      handleClick();
    }
  };

  return (
    <>
      <button 
        onClick={handleClick}
        onKeyDown={handleKeyDown}
        className={`
          flex flex-col items-center gap-2 p-3 md:p-4 
          bg-neutral dark:bg-gray-700 rounded-lg border border-gray-100 dark:border-gray-600
          hover:bg-gray-50 dark:hover:bg-gray-600 hover-lift 
          transition-all duration-200 touch-manipulation
          focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-gray-800
          active:scale-95
          ${isPressed ? 'scale-95 bg-gray-100 dark:bg-gray-600' : ''}
        `}
        aria-label={`${label} - ${description}`}
      >
        <div className={`
          w-8 h-8 md:w-12 md:h-12 bg-primary/10 dark:bg-primary/20 rounded-lg 
          flex items-center justify-center transition-all duration-200
          ${isPressed ? 'bg-primary/20 dark:bg-primary/30 scale-110' : ''}
        `}>
          <span className={`text-lg md:text-2xl transition-transform duration-200 ${isPressed ? 'scale-110' : ''}`}>
            {icon}
          </span>
        </div>
        <div className="text-center">
          <div className="font-medium text-charcoal dark:text-gray-100 text-xs md:text-sm">
            {label}
          </div>
          <div className="text-xs text-gray-600 dark:text-gray-400">
            {description}
          </div>
        </div>
      </button>

      {showModal && (
        <QuickActionModal 
          action={action}
          onClose={() => setShowModal(false)}
        />
      )}
    </>
  );
}

interface QuickActionModalProps {
  action: "customer" | "job" | "invoice" | "product";
  onClose: () => void;
}

function QuickActionModal({ action, onClose }: QuickActionModalProps) {
  const getModalContent = () => {
    switch (action) {
      case "customer":
        return <CustomerQuickForm onClose={onClose} />;
      case "job":
        return <JobQuickForm onClose={onClose} />;
      case "invoice":
        return <InvoiceQuickForm onClose={onClose} />;
      case "product":
        return <ProductQuickForm onClose={onClose} />;
      default:
        return null;
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" onClick={onClose}>
      <div 
        className="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-4 md:p-6 w-full max-w-md max-h-[95vh] overflow-y-auto"
        onClick={(e) => e.stopPropagation()}
      >
        {getModalContent()}
      </div>
    </div>
  );
}

// Placeholder quick form components - these would be implemented separately
function CustomerQuickForm({ onClose }: { onClose: () => void }) {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-charcoal dark:text-gray-100">Add Customer</h3>
        <button
          onClick={onClose}
          className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          aria-label="Close"
        >
          ✕
        </button>
      </div>
      <div className="text-gray-600 dark:text-gray-400">Customer form coming soon...</div>
      <div className="flex justify-end gap-2">
        <button
          onClick={onClose}
          className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
        >
          Cancel
        </button>
      </div>
    </div>
  );
}

function JobQuickForm({ onClose }: { onClose: () => void }) {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-charcoal dark:text-gray-100">Create Job</h3>
        <button
          onClick={onClose}
          className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          aria-label="Close"
        >
          ✕
        </button>
      </div>
      <div className="text-gray-600 dark:text-gray-400">Job form coming soon...</div>
      <div className="flex justify-end gap-2">
        <button
          onClick={onClose}
          className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
        >
          Cancel
        </button>
      </div>
    </div>
  );
}

function InvoiceQuickForm({ onClose }: { onClose: () => void }) {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-charcoal dark:text-gray-100">New Invoice</h3>
        <button
          onClick={onClose}
          className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          aria-label="Close"
        >
          ✕
        </button>
      </div>
      <div className="text-gray-600 dark:text-gray-400">Invoice form coming soon...</div>
      <div className="flex justify-end gap-2">
        <button
          onClick={onClose}
          className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
        >
          Cancel
        </button>
      </div>
    </div>
  );
}

function ProductQuickForm({ onClose }: { onClose: () => void }) {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-charcoal dark:text-gray-100">Add Product</h3>
        <button
          onClick={onClose}
          className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          aria-label="Close"
        >
          ✕
        </button>
      </div>
      <div className="text-gray-600 dark:text-gray-400">Product form coming soon...</div>
      <div className="flex justify-end gap-2">
        <button
          onClick={onClose}
          className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
        >
          Cancel
        </button>
      </div>
    </div>
  );
}

export function QuickActionsSection() {
  return (
    <div className="card p-4 md:p-6">
      <h2 className="text-lg md:text-xl font-semibold text-charcoal mb-4">Quick Actions</h2>
      <div className="grid grid-cols-2 md:grid-cols-4 gap-3 md:gap-4">
        <QuickActionButton
          icon="👥"
          label="Add Customer"
          description="New customer"
          action="customer"
        />
        <QuickActionButton
          icon="🔧"
          label="Create Job"
          description="Schedule work"
          action="job"
        />
        <QuickActionButton
          icon="💰"
          label="New Invoice"
          description="Bill customer"
          action="invoice"
        />
        <QuickActionButton
          icon="📦"
          label="Add Product"
          description="Inventory item"
          action="product"
        />
      </div>
    </div>
  );
}
interface StatCardProps {
  title: string;
  value: string | number;
  icon: string;
  color: "primary" | "accent";
  trend: string;
}

export function StatCard({ title, value, icon, color, trend }: StatCardProps) {
  return (
    <div className="card p-3 md:p-6 hover-lift">
      <div className="flex items-center justify-between mb-2 md:mb-3">
        <div className={`w-8 h-8 md:w-12 md:h-12 rounded-lg flex items-center justify-center ${
          color === "primary" ? "bg-primary/10" : "bg-accent/10"
        }`}>
          <span className="text-lg md:text-2xl">{icon}</span>
        </div>
        <span className="text-xs md:text-sm font-medium text-green-600">{trend}</span>
      </div>
      <div className="text-xl md:text-2xl font-bold text-charcoal mb-1">{value}</div>
      <div className="text-xs md:text-sm text-gray-600">{title}</div>
    </div>
  );
}
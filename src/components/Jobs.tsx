import { useState, useEffect } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { toast } from "../utils/toast";
import { useTheme } from "../hooks/useTheme";
import { DynamicTable, ColumnDefinition } from "../components/common/DynamicTable";

const getStatusColor = (status: string) => {
  switch (status) {
    case "scheduled": return "bg-blue-100 text-blue-800";
    case "in-progress": return "bg-yellow-100 text-yellow-800";
    case "completed": return "bg-green-100 text-green-800";
    case "cancelled": return "bg-red-100 text-red-800";
    default: return "bg-gray-100 text-gray-800";
  }
};

// Job management component with CRUD operations
export function Jobs({ selectedItemId }: { selectedItemId?: string | null }) {
  const jobs = useQuery(api.jobs.list);
  const customers = useQuery(api.customers.list);
  const deleteJob = useMutation(api.jobs.remove);
  const loggedInUser = useQuery(api.auth.loggedInUser);
  const [selectedJob, setSelectedJob] = useState<any>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [statusFilter, setStatusFilter] = useState("all");

  // Auto-select job when selectedItemId is provided from search navigation
  useEffect(() => {
    if (selectedItemId && jobs) {
      const jobToSelect = jobs.find(job => job._id === selectedItemId);
      if (jobToSelect) {
        setSelectedJob(jobToSelect);
        setIsEditing(false); // Show detail view, not edit form
      }
    }
  }, [selectedItemId, jobs]);

  // Function to handle job deletion
  const handleRowDeletion = async (job: any) => {
    try {
      await deleteJob({ id: job._id });
      toast.success(`${job.title} has been deleted`);
      // If the deleted job was selected, clear the selection
      if (selectedJob && selectedJob._id === job._id) { 
        setSelectedJob(null);
        setIsEditing(false);
      }
    } catch (error) {
      toast.error("Failed to delete job");
      console.error("Delete error:", error);
    }
  };

  // Define columns for DynamicTable
  const columns: ColumnDefinition[] = [
    {
      id: "title",
      header: "Job",
      accessor: (job) => (
        <div>
          <div className="text-sm font-medium text-charcoal">{job.title}</div>
        <div className="text-sm text-gray-600 truncate max-w-xs">{job.description}</div>
        </div>
      ),
      sortable: true,
      defaultVisible: true
    },
    {
      id: "customer",
      header: "Customer",
      accessor: (job) => <div className="text-sm text-charcoal">{job.customer?.name || "Unknown"}</div>,
      sortable: true,
      defaultVisible: true
    },
    {
      id: "status",
      header: "Status",
      accessor: (job) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(job.status)}`}>
          {job.status.replace("-", " ")}
        </span>
      ),
      sortable: true,
      defaultVisible: true,
      filterable: true,
      getFilterValue: (job) => job.status
    },
    {
      id: "scheduledDate",
      header: "Scheduled",
      accessor: (job) => (
        <span className="text-sm text-charcoal">
        {job.scheduledDate ? new Date(job.scheduledDate).toLocaleDateString() : "Not scheduled"}
        </span>
      ),
      sortable: true,
      defaultVisible: true
    }
  ];

  // Filter jobs based on status filter
  const filteredJobs = jobs?.filter(job => {
    return statusFilter === "all" || job.status === statusFilter;
  }) || [];

  const handleEdit = (job: any) => {
    setSelectedJob(job);
    setIsEditing(true);
  };

  const handleAdd = () => {
    setSelectedJob(null);
    setIsEditing(true);
  };

  if (!jobs || !customers || !loggedInUser) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-[1fr_400px] gap-6 h-full">
      {/* Left Column - Jobs List */}
      <div className="space-y-4">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold text-charcoal">Jobs</h1>
            <p className="text-gray-600 text-sm md:text-base">Manage your HVAC service jobs</p>
          </div>
          <button
            onClick={handleAdd}
            className="btn-primary w-full sm:w-auto touch-manipulation"
          >
            <span className="mr-2">➕</span>
            Add Job
          </button>
        </div>

        {/* Status Filter */}
        <div className="card p-4">
          <div className="flex gap-2 flex-wrap">
            {["all", "scheduled", "in-progress", "completed", "cancelled"].map((status) => (
              <button
                key={status}
                onClick={() => setStatusFilter(status)}
                className={`px-3 md:px-4 py-2 rounded-lg capitalize transition-colors text-sm md:text-base touch-manipulation ${
                  statusFilter === status
                    ? "bg-primary text-white"
                    : "bg-neutral text-charcoal hover:bg-gray-200"
                }`}
              >
                {status === "all" ? "All Jobs" : status.replace("-", " ")}
              </button>
            ))}
          </div>
        </div>

        {/* Jobs Table using DynamicTable */}
        <div className="card overflow-hidden">
          <DynamicTable
            data={filteredJobs}
            columns={columns}
            rowKey="_id"
            tableId="jobs-table"
            onRowClick={(job) => {
              setSelectedJob(job);
              setIsEditing(false);
            }}
            selectedId={selectedJob?._id}
            searchable={true}
            pageSize={10}
            filterable={true}
            emptyMessage="No jobs found"
            className="w-full"
            reorderable={true}
            onDelete={handleRowDeletion}
            showDeleteButton={true}
            deleteConfirmMessage="Are you sure you want to delete this job?"
          />
        </div>

        {/* Jobs List - Mobile Cards */}
        <div className="md:hidden space-y-3">
          {filteredJobs.length > 0 ? (
            filteredJobs.map((job) => (
              <JobCard
                key={job._id}
                job={job}   
                onEdit={handleEdit}
                onDelete={handleRowDeletion}
              />
            ))
          ) : (
            <div className="card p-8 text-center">
              <p className="text-gray-500">No jobs found</p>
            </div>
          )}
        </div>
    </div>

      {/* Right Column - Preview/Edit Panel */}
      <div className="card p-6 h-full">
        {isEditing ? (
          <JobForm
            job={selectedJob}
            customers={customers}
            currentUserId={loggedInUser._id}
            onClose={() => {
              setSelectedJob(null);
              setIsEditing(false);
            }}
            onSuccess={() => {
              setSelectedJob(null);
              setIsEditing(false);
            }}
          />
        ) : selectedJob ? (
          <JobPreview job={selectedJob} onEdit={() => setIsEditing(true)} />
        ) : (
          <div className="flex flex-col items-center justify-center h-full text-center">
            <h3 className="text-xl font-semibold text-charcoal mb-2">
              Select a job to view details
            </h3>
            <p className="text-gray-600">
              Click on a job in the list to see more information or edit it
            </p>
          </div>
        )}
      </div>

      {/* Job Form Modal */}
      {isEditing && (
        <JobForm
          job={selectedJob}
          customers={customers}
          currentUserId={loggedInUser._id}
          onClose={() => {
            setSelectedJob(null);
            setIsEditing(false);
          }}
          onSuccess={() => {
            setSelectedJob(null);
            setIsEditing(false);
          }}
        />
      )}
    </div>
  );
}

// Mobile job card component
function JobCard({ job, onEdit, onDelete }: { job: any; onEdit: (job: any) => void; onDelete: (job: any) => void }) {
  const { isDark } = useTheme();

  return (
    <div className="card p-4">
      <h3 className="font-semibold text-charcoal">{job.title}</h3>
      <p className="text-sm text-gray-600">{job.customer?.name || "Unknown"}</p>
      <div className="flex gap-2 mt-3">
        <button onClick={() => onEdit(job)} className="flex-1 btn-primary text-sm py-2">Edit</button>
        <button onClick={() => onDelete(job)} className="flex-1 btn-secondary text-sm py-2">Delete</button>
      </div>
    </div>
  );
}

function JobPreview({ job, onEdit }: { job: any; onEdit: () => void }) {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-charcoal">{job.title}</h2>
        <button
          onClick={onEdit}
          className="btn-primary text-sm px-4 py-2"
        >
          Edit
        </button>
      </div>
      
      <div className="space-y-4">
        <div>
          <h3 className="text-sm font-medium text-gray-600 mb-1">Customer</h3>
          <p className="text-charcoal">{job.customer?.name || "Unknown"}</p>
        </div>
        
        <div>
          <h3 className="text-sm font-medium text-gray-600 mb-1">Status</h3>
          <p className="text-charcoal capitalize">{job.status.replace("-", " ")}</p>
        </div>
        
        <div>
          <h3 className="text-sm font-medium text-gray-600 mb-1">Scheduled</h3>
          <p className="text-charcoal">
            {job.scheduledDate ? new Date(job.scheduledDate).toLocaleString() : "Not scheduled"}
          </p>
        </div>
        
        <div>
          <h3 className="text-sm font-medium text-gray-600 mb-1">Description</h3>
          <p className="text-charcoal whitespace-pre-line">{job.description}</p>
        </div>
        
        {job.notes && (
          <div>
            <h3 className="text-sm font-medium text-gray-600 mb-1">Notes</h3>
            <p className="text-charcoal whitespace-pre-line">{job.notes}</p>
          </div>
        )}
      </div>
    </div>
  );
}



// Job form component for create/edit operations
function JobForm({ job, customers, currentUserId, onClose, onSuccess }: {
  job: any;
  customers: any[];
  currentUserId: string;
  onClose: () => void;
  onSuccess: () => void;
}) {
  const createJob = useMutation(api.jobs.create);
  const updateJob = useMutation(api.jobs.update);
  const { isDark } = useTheme();
  const [formData, setFormData] = useState({
    customerId: job?.customerId || "",
    title: job?.title || "",
    description: job?.description || "",
    status: job?.status || "scheduled",
    priority: job?.priority || "medium",
    scheduledDate: job?.scheduledDate ? new Date(job.scheduledDate).toISOString() : "",
    scheduledTime: job?.scheduledDate ? new Date(job.scheduledDate).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) : "09:00",
    completedDate: job?.completedDate ? new Date(job.completedDate).toISOString() : "",
    estimatedHours: job?.estimatedHours || "",
    notes: job?.notes || "",
    assignedTo: job?.assignedTo || currentUserId,
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Basic validation
    if (!formData.customerId || !formData.title.trim() || !formData.description.trim()) {
      toast.error("Please fill in all required fields");
      return;
    }
    
    try {
      // Combine date and time for scheduled date
      let scheduledDateTime;
      if (formData.scheduledDate) {
        const date = new Date(formData.scheduledDate);
        const [hours, minutes] = formData.scheduledTime.split(':').map(Number);
        date.setHours(hours, minutes);
        scheduledDateTime = date.getTime();
      }

      const submitData = {
        customerId: formData.customerId,
        title: formData.title,
        description: formData.description,
        status: formData.status,
        priority: formData.priority,
        scheduledDate: scheduledDateTime,
        completedDate: formData.completedDate ? new Date(formData.completedDate).getTime() : undefined,
        estimatedHours: formData.estimatedHours ? Number(formData.estimatedHours) : undefined,
        notes: formData.notes,
        assignedTo: formData.assignedTo,
      };

      if (job) {
        await updateJob({ id: job._id, ...submitData });
        toast.success("Job updated successfully");
      } else {
        await createJob(submitData);
        toast.success("Job created successfully");
      }
      onSuccess();
    } catch (error) {
      toast.error("Failed to save job");
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className={`${isDark ? 'bg-gray-800 text-white' : 'bg-white text-gray-900'} rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto`}>
        <h2 className="text-xl font-semibold mb-4">
          {job ? "Edit Job" : "Add Job"}
        </h2>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Customer *
            </label>
            <select
              required
              value={formData.customerId}
              onChange={(e) => setFormData({ ...formData, customerId: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Select a customer</option>
              {customers.map((customer) => (
                <option key={customer._id} value={customer._id}>
                  {customer.name} - {customer.email}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Job Title *
            </label>
            <input
              type="text"
              required
              value={formData.title}
              onChange={(e) => setFormData({ ...formData, title: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description *
            </label>
            <textarea
              required
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Status *
              </label>
              <select
                required
                value={formData.status}
                onChange={(e) => setFormData({ ...formData, status: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="scheduled">Scheduled</option>
                <option value="in-progress">In Progress</option>
                <option value="completed">Completed</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Priority *
              </label>
              <select
                required
                value={formData.priority}
                onChange={(e) => setFormData({ ...formData, priority: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="low">Low</option>
                <option value="medium">Medium</option>
                <option value="high">High</option>
                <option value="emergency">Emergency</option>
              </select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className={`block text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
                Scheduled Date
              </label>
              <div className="flex gap-2">
                <input
                  type="date"
                  value={formData.scheduledDate ? formData.scheduledDate.split('T')[0] : ''}
                  onChange={(e) => setFormData({ ...formData, scheduledDate: e.target.value })}
                  className={`flex-1 px-3 py-2 border ${isDark ? 'bg-gray-700 border-gray-600 text-white' : 'border-gray-300'} rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
                />
                <input
                  type="time"
                  value={formData.scheduledTime}
                  onChange={(e) => setFormData({ ...formData, scheduledTime: e.target.value })}
                  className={`flex-1 px-3 py-2 border ${isDark ? 'bg-gray-700 border-gray-600 text-white' : 'border-gray-300'} rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
                />
              </div>
            </div>
            <div>
              <label className={`block text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
                Completed Date
              </label>
              <input
                type="date"
                value={formData.completedDate ? formData.completedDate.split('T')[0] : ''}
                onChange={(e) => setFormData({ ...formData, completedDate: e.target.value })}
                className={`w-full px-3 py-2 border ${isDark ? 'bg-dark-surface border-dark-border text-dark-text' : 'border-gray-300 bg-white'} rounded-md focus:ring-2 focus:ring-primary focus:border-transparent transition-colors`}
              />
            </div>
          </div>

          <div>
            <label className={`block text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
              Estimated Hours
            </label>
            <input
              type="number"
              step="0.5"
              value={formData.estimatedHours}
              onChange={(e) => setFormData({ ...formData, estimatedHours: e.target.value })}
              className={`w-full px-3 py-2 border ${isDark ? 'bg-gray-700 border-gray-600 text-white' : 'border-gray-300'} rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
            />
          </div>

          <div>
            <label className={`block text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
              Notes
            </label>
            <textarea
              value={formData.notes}
              onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
              rows={3}
              className={`w-full px-3 py-2 border ${isDark ? 'bg-gray-700 border-gray-600 text-white' : 'border-gray-300'} rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
            />
          </div>

          <div className="flex gap-3 pt-4">
            <button
              type="submit"
              className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
            >
              {job ? "Update" : "Create"}
            </button>
            <button
              type="button"
              onClick={onClose}
              className={`flex-1 ${isDark ? 'bg-gray-600 text-white hover:bg-gray-700' : 'bg-gray-300 text-gray-700 hover:bg-gray-400'} py-2 px-4 rounded-md transition-colors`}
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}


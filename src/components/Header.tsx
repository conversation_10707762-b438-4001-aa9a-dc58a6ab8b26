import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { SignOutButton } from "../SignOutButton";
import { ThemeToggle } from "./ThemeToggle";
import { HeaderSearch } from "./HeaderSearch";
import { MobileSearchOverlay } from "./MobileSearchOverlay";
import { useStorageUrl } from "../hooks/useStorageUrl";
import { useState } from "react";

export function Header() {
  const user = useQuery(api.auth.loggedInUser);
  const settings = useQuery(api.settings.get);
  const logoUrl = useStorageUrl(settings?.logoUrl);
  const [isMobileSearchOpen, setIsMobileSearchOpen] = useState(false);

  // Default company name and logo initial
  const companyName = settings?.companyName || "HVAC CRM";
  const logoInitial = companyName ? companyName.charAt(0).toUpperCase() : "H";

  const handleNavigate = (type: string, id: string) => {
    // Enhanced navigation using custom events with item selection
    let viewName = type;
    if (type === 'customer') viewName = 'customers';
    else if (type === 'invoice') viewName = 'invoices';
    else if (type === 'job') viewName = 'jobs';
    else if (type === 'product') viewName = 'products';

    const event = new CustomEvent('changeView', {
      detail: {
        page: viewName,
        itemId: id
      }
    });
    window.dispatchEvent(event);
  };

  return (
    <>
      <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 shadow-sm transition-colors">
        <div className="mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-14 md:h-16 py-2 md:py-3">
            {/* Left Section: Logo and Company Branding */}
            <div className="flex items-center gap-2 md:gap-3 flex-shrink-0">
              {logoUrl ? (
                <div className="w-8 h-8 md:w-10 md:h-10 lg:w-12 lg:h-12 rounded-lg flex items-center justify-center overflow-hidden bg-white dark:bg-gray-700 shadow-md ring-1 ring-gray-100 dark:ring-gray-600">
                  <img
                    src={logoUrl}
                    alt={`${companyName} logo`}
                    className="w-full h-full object-contain p-0.5"
                  />
                </div>
              ) : (
                <div className="w-8 h-8 md:w-10 md:h-10 lg:w-12 lg:h-12 bg-gradient-to-br from-primary to-accent rounded-lg flex items-center justify-center shadow-md ring-1 ring-white dark:ring-gray-800">
                  <span className="text-white font-bold text-sm md:text-base lg:text-lg">{logoInitial}</span>
                </div>
              )}

              {/* Company Name and Tagline - Compact version */}
              <div className="hidden md:block">
                <h1 className="text-base md:text-lg lg:text-xl font-bold text-charcoal dark:text-white tracking-tight leading-tight">
                  {companyName}
                </h1>
                <p className="text-xs text-gray-500 dark:text-gray-400 font-medium leading-tight">
                  Professional Service Management
                </p>
              </div>
            </div>

            {/* Center Section: Search Bar - Compact and Balanced */}
            <div className="hidden lg:flex flex-1 justify-center max-w-sm mx-8">
              <div className="w-full max-w-xs">
                <HeaderSearch onNavigate={handleNavigate} />
              </div>
            </div>

            {/* Right Section: User Actions and Controls */}
            <div className="flex items-center gap-1 md:gap-2 flex-shrink-0">
              {/* Mobile Search Button */}
              <button
                onClick={() => setIsMobileSearchOpen(true)}
                className="md:hidden p-1.5 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors"
                aria-label="Search"
              >
                <svg
                  className="h-4 w-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
              </button>

              <ThemeToggle />

              {/* User Profile */}
              {user && (
                <div className="flex items-center gap-1 md:gap-2">
                  <div className="hidden lg:block text-right">
                    <p className="text-xs font-semibold text-charcoal dark:text-gray-100">{user.name}</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">{user.email}</p>
                  </div>

                  <div className="w-7 h-7 md:w-8 md:h-8 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center shadow-sm ring-1 ring-white dark:ring-gray-800">
                    <span className="text-white font-semibold text-xs md:text-sm">
                      {user.name ? user.name.charAt(0).toUpperCase() : "U"}
                    </span>
                  </div>
                </div>
              )}

              <SignOutButton />
            </div>
          </div>
        </div>
      </header>

      {/* Mobile Search Overlay */}
      <MobileSearchOverlay
        isOpen={isMobileSearchOpen}
        onClose={() => setIsMobileSearchOpen(false)}
        onNavigate={handleNavigate}
      />
    </>
  );
}
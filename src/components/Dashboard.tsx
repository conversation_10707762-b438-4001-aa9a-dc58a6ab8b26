import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { useState } from "react";
import { BrandingSettings } from "./BrandingSettings";
import { StatCard } from "./StatCard";
import { JobCard } from "./JobCard.tsx";
import { InvoiceCard } from "./InvoiceCard.tsx";
import { QuickActionsSection } from "./QuickActionsSection.tsx";
import { JobsMap } from "./JobsMap.tsx";

// Dashboard component with overview metrics and recent activity
export function Dashboard() {
  const user = useQuery(api.auth.loggedInUser);
  const dashboardStats = useQuery(api.dashboard.getStats);
  const recentJobs = useQuery(api.jobs.getRecent);
  const recentInvoices = useQuery(api.invoices.list);
  const [activeTab, setActiveTab] = useState('dashboard');

  if (!dashboardStats || !recentJobs || !recentInvoices) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <div className="spinner h-8 w-8"></div>
      </div>
    );
  }

  const limitedRecentInvoices = recentInvoices.slice(0, 5);

  return (
    <div className="space-y-3 md:space-y-4 lg:space-y-6 pb-16 md:pb-6">
      {/* Tab Navigation */}
      <div className="flex border-b border-gray-200 mb-6">
        <button
          onClick={() => setActiveTab('dashboard')}
          className={`px-4 py-2 font-medium text-sm ${activeTab === 'dashboard' ? 'text-primary border-b-2 border-primary' : 'text-gray-500 hover:text-gray-700'}`}
        >
          Dashboard
        </button>
      </div>

      {activeTab === 'dashboard' ? (
        <>
          {/* Welcome Header */}
          <div className="fade-in">
            <h1 className="text-2xl md:text-3xl font-bold text-charcoal mb-2">
              Welcome back, {user?.name || "User"}! 👋
            </h1>
            <p className="text-gray-600 text-sm md:text-base">
              Here's what's happening with your Bernie's heating today.
            </p>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 md:gap-6">
            <StatCard
              title="Total Jobs"
              value={dashboardStats.totalJobs}
              icon="🔧"
              color="primary"
              trend="+12%"
            />
            <StatCard
              title="Active Jobs"
              value={dashboardStats.activeJobs}
              icon="⚡"
              color="accent"
              trend="+5%"
            />
            <StatCard
              title="Total Revenue"
              value={`$${dashboardStats.totalRevenue.toLocaleString()}`}
              icon="💰"
              color="primary"
              trend="+18%"
            />
            <StatCard
              title="Customers"
              value={dashboardStats.totalCustomers}
              icon="👥"
              color="accent"
              trend="+8%"
            />
          </div>

          {/* Jobs Map - Fixed Height */}
          <div className="w-full">
            <JobsMap className="fade-in" />
          </div>

          {/* Recent Activity Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6">
            {/* Recent Jobs */}
            <div className="card p-4 md:p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg md:text-xl font-semibold text-charcoal">Recent Jobs</h2>
                <span className="text-xs md:text-sm text-gray-500">Last 5 jobs</span>
              </div>
              <div className="space-y-3">
                {recentJobs.length > 0 ? (
                  recentJobs.slice(0, 5).map((job: any) => (
                    <JobCard key={job._id} job={job} />
                  ))
                ) : (
                  <p className="text-gray-500 text-center py-8 text-sm md:text-base">No recent jobs</p>
                )}
              </div>
            </div>

            {/* Recent Invoices */}
            <div className="card p-4 md:p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg md:text-xl font-semibold text-charcoal">Recent Invoices</h2>
                <span className="text-xs md:text-sm text-gray-500">Last 5 invoices</span>
              </div>
              <div className="space-y-3">
                {limitedRecentInvoices.length > 0 ? (
                  limitedRecentInvoices.map((invoice: any) => (
                    <InvoiceCard key={invoice._id} invoice={invoice} />
                  ))
                ) : (
                  <p className="text-gray-500 text-center py-8 text-sm md:text-base">No recent invoices</p>
                )}
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <QuickActionsSection />
        </>
      ) : (
        <BrandingSettings />
      )}
    </div>
  );
}
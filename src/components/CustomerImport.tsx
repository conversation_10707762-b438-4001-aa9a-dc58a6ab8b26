import { useState, useRef } from "react";
import { useCustomerImport, useCustomerImportCheck, parseCSV, formatCustomersForImport } from "../utilities/csv-importer";

type ImportStatus = {
  state: "idle" | "checking" | "ready" | "importing" | "complete" | "error";
  message?: string;
  missingCustomers?: any[];
  existingCount?: number;
  missingCount?: number;
  importResult?: any;
  csvColumns?: string[]; // Add this to track CSV columns
  additionalFields?: string[]; // Add this to track additional fields
};

export default function CustomerImport() {
  const [csvContent, setCsvContent] = useState("");
  const [status, setStatus] = useState<ImportStatus>({ state: "idle" });
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const { importCustomers } = useCustomerImport();
  const { checkCustomersImport } = useCustomerImportCheck();
  
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    const reader = new FileReader();
    reader.onload = (event) => {
      const content = event.target?.result as string;
      setCsvContent(content);
      
      // Extract column headers from CSV
      const parsedData = parseCSV(content);
      if (parsedData.length > 0) {
        const columns = Object.keys(parsedData[0]);
        setStatus({ 
          state: "idle", 
          csvColumns: columns,
          additionalFields: getAdditionalFields(columns)
        });
      } else {
        setStatus({ state: "idle" });
      }
    };
    reader.readAsText(file);
  };

  // Helper function to identify additional fields
  const getAdditionalFields = (columns: string[]) => {
    const standardFields = ['id', 'name', 'email', 'phone', 'address', 'city', 'state', 'zipCode', 'zip_code'];
    return columns.filter(col => !standardFields.includes(col));
  };

  const loadSampleData = async () => {
    try {
      const response = await fetch('/sample-customers.csv');
      const content = await response.text();
      setCsvContent(content);
      
      // Extract column headers from sample data
      const parsedData = parseCSV(content);
      if (parsedData.length > 0) {
        const columns = Object.keys(parsedData[0]);
        setStatus({ 
          state: "idle", 
          csvColumns: columns,
          additionalFields: getAdditionalFields(columns)
        });
      } else {
        setStatus({ state: "idle" });
      }
    } catch (error) {
      setStatus({ 
        state: "error", 
        message: "Failed to load sample data"
      });
    }
  };
  
  const handleCheck = async () => {
    if (!csvContent) return;
    
    setStatus({ state: "checking" });
    try {
      const result = await checkCustomersImport(csvContent);
      
      if ('error' in result) {
        setStatus({ 
          state: "error", 
          message: result.error as string 
        });
        return;
      }
      
      // Extract column headers from CSV
      const parsedData = parseCSV(csvContent);
      const columns = parsedData.length > 0 ? Object.keys(parsedData[0]) : [];
      const additionalFields = getAdditionalFields(columns);
      
      setStatus({
        state: "ready",
        missingCustomers: result.missingCustomers,
        existingCount: result.existingCount,
        missingCount: result.missingCount,
        csvColumns: columns,
        additionalFields: additionalFields,
        message: `Found ${result.missingCount} customers to import. ${result.existingCount} already exist. ${additionalFields.length > 0 ? `Additional fields found: ${additionalFields.join(', ')}` : ''}`
      });
    } catch (error) {
      setStatus({ 
        state: "error", 
        message: error instanceof Error ? error.message : "Unknown error during check" 
      });
    }
  };
  
  const handleImport = async () => {
    if (!csvContent || status.state !== "ready") return;
    
    setStatus({ state: "importing" });
    try {
      const result = await importCustomers(csvContent);
      
      if ('error' in result) {
        setStatus({ 
          state: "error", 
          message: result.error as string 
        });
        return;
      }
      
      setStatus({
        state: "complete",
        importResult: result,
        csvColumns: status.csvColumns,
        additionalFields: status.additionalFields,
        message: `Import complete: ${result.imported} imported, ${result.updated} updated, ${result.skipped} skipped.${result.newFields?.length > 0 ? ` Additional fields found: ${result.newFields.join(', ')}` : ''}`
      });
    } catch (error) {
      setStatus({ 
        state: "error", 
        message: error instanceof Error ? error.message : "Unknown error during import" 
      });
    }
  };
  
  const handleReset = () => {
    setCsvContent("");
    setStatus({ state: "idle" });
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };
  
  return (
    <div className="card p-6 bg-white rounded-lg shadow-lg">
      <h2 className="text-xl font-semibold mb-6">Import Customers from CSV</h2>
      
      <div className="mb-6">
        <label className="block mb-2 font-medium">
          Upload Customers CSV File
        </label>
        <div className="flex flex-col md:flex-row gap-3 mb-2">
          <input
            type="file"
            accept=".csv"
            onChange={handleFileUpload}
            ref={fileInputRef}
            className="block text-sm text-gray-500
              file:mr-4 file:py-2 file:px-4
              file:rounded-md file:border-0
              file:text-sm file:font-semibold
              file:bg-blue-50 file:text-blue-700
              hover:file:bg-blue-100"
          />
          <button 
            onClick={loadSampleData}
            className="px-4 py-2 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
          >
            Load Sample Data
          </button>
        </div>
        {csvContent && (
          <div className="mt-2 text-sm text-gray-500">
            CSV file loaded ({parseCSV(csvContent).length} total rows)
            <div className="text-xs text-blue-600 mt-1">
              All rows from the CSV will be processed during import.
              {status.csvColumns && status.csvColumns.length > 0 && (
                <div className="mt-1">
                  <span className="font-semibold">CSV Columns:</span> {status.csvColumns.join(', ')}
                </div>
              )}
              {status.additionalFields && status.additionalFields.length > 0 && (
                <div className="mt-1 text-purple-600">
                  <span className="font-semibold">Additional Fields:</span> {status.additionalFields.join(', ')}
                  <div className="text-xs mt-1">
                    These additional fields will be stored in the notes section of each customer.
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
      
      <div className="flex flex-wrap gap-4 mb-6">
        <button
          onClick={handleCheck}
          disabled={!csvContent || status.state === "checking"}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400"
        >
          {status.state === "checking" ? "Checking..." : "Check Data"}
        </button>
        
        <button
          onClick={handleImport}
          disabled={status.state !== "ready"}
          className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-400"
        >
          {status.state === "importing" ? "Importing..." : "Import Customers"}
        </button>
        
        <button
          onClick={handleReset}
          className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
        >
          Reset
        </button>
      </div>
      
      {status.message && (
        <div className={`p-4 mb-6 rounded-md ${
          status.state === "error" 
            ? "bg-red-100 text-red-700" 
            : status.state === "complete"
              ? "bg-green-100 text-green-700"
              : "bg-blue-100 text-blue-700"
        }`}>
          {status.message}
        </div>
      )}
      
      {/* Display Additional Fields Section */}
      {status.additionalFields && status.additionalFields.length > 0 && status.state !== "idle" && (
        <div className="p-4 mb-6 bg-purple-50 rounded-md">
          <h3 className="text-lg font-semibold text-purple-700">Additional Fields</h3>
          <p className="text-sm text-purple-600 mb-2">
            These fields are not in the standard customer schema but will be saved in the notes:
          </p>
          <div className="flex flex-wrap gap-2">
            {status.additionalFields.map((field, index) => (
              <span key={index} className="px-2 py-1 bg-purple-100 text-purple-800 rounded-md text-sm">
                {field}
              </span>
            ))}
          </div>
        </div>
      )}
      
      {status.state === "ready" && status.missingCustomers && status.missingCustomers.length > 0 && (
        <div className="mt-6">
          <h3 className="text-lg font-semibold mb-3">Customers to Import ({status.missingCount})</h3>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phone</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">City/State</th>
                  {status.additionalFields && status.additionalFields.length > 0 && (
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Additional Fields</th>
                  )}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {status.missingCustomers.slice(0, 20).map((customer, index) => (
                  <tr key={index}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{customer.name}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{customer.email}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{customer.phone}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{customer.city}, {customer.state}</td>
                    {status.additionalFields && status.additionalFields.length > 0 && (
                      <td className="px-6 py-4 text-sm text-gray-500 max-w-xs truncate">
                        {status.additionalFields.map(field => 
                          customer[field] ? (
                            <div key={field} className="truncate">
                              <span className="font-semibold">{field}:</span> {customer[field]}
                            </div>
                          ) : null
                        )}
                      </td>
                    )}
                  </tr>
                ))}
              </tbody>
            </table>
            {status.missingCustomers.length > 20 && (
              <div className="text-center text-sm text-gray-500 mt-2">
                Showing 20 of {status.missingCustomers.length} customers. <span className="font-semibold">All {status.missingCustomers.length} customers will be imported when you click "Import Customers".</span>
              </div>
            )}
          </div>
        </div>
      )}
      
      {status.state === "complete" && status.importResult && (
        <div className="mt-6">
          <h3 className="text-lg font-semibold mb-3">Import Summary</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-blue-50 p-4 rounded-md">
              <div className="text-3xl font-bold text-blue-600">{status.importResult.imported}</div>
              <div className="text-sm text-blue-700">New Customers</div>
            </div>
            <div className="bg-green-50 p-4 rounded-md">
              <div className="text-3xl font-bold text-green-600">{status.importResult.updated}</div>
              <div className="text-sm text-green-700">Updated</div>
            </div>
            <div className="bg-yellow-50 p-4 rounded-md">
              <div className="text-3xl font-bold text-yellow-600">{status.importResult.skipped}</div>
              <div className="text-sm text-yellow-700">Skipped</div>
            </div>
            <div className="bg-gray-50 p-4 rounded-md">
              <div className="text-3xl font-bold text-gray-600">{status.importResult.totalProcessed}</div>
              <div className="text-sm text-gray-700">Total Processed</div>
            </div>
          </div>
          
          {/* Display additional fields in the import result */}
          {status.importResult.newFields && status.importResult.newFields.length > 0 && (
            <div className="mt-4 p-4 bg-purple-50 rounded-md">
              <h4 className="font-semibold text-purple-600 mb-2">Additional Fields Imported</h4>
              <div className="flex flex-wrap gap-2">
                {status.importResult.newFields.map((field: string, i: number) => (
                  <span key={i} className="px-2 py-1 bg-purple-100 text-purple-800 rounded-md text-sm">
                    {field}
                  </span>
                ))}
              </div>
              <div className="text-sm text-purple-600 mt-2">
                These fields have been stored in the notes section of each customer record.
              </div>
            </div>
          )}
          
          {status.importResult.errors && status.importResult.errors.length > 0 && (
            <div className="mt-4">
              <h4 className="font-semibold text-red-600 mb-2">Errors</h4>
              <ul className="list-disc pl-5 text-sm text-red-600">
                {status.importResult.errors.map((error: string, i: number) => (
                  <li key={i}>{error}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

import { useAction, useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { toast } from "sonner";
import { PDFGenerationResult, PDFUrlResponse, PDFActionResult, PDFEmailDeliveryResult } from "../components/invoices/types";
import { generateInvoicePDFBlob } from "../utilities/pdfGenerator";

export function usePDFOperations(invoiceId: Id<"invoices"> | undefined) {
  const generateAndStorePDF = useAction(api.invoices.generateAndStorePDF); // Legacy - deprecated
  const storePDFBlob = useAction(api.invoices.storePDFBlob); // New action for storing client-generated PDFs
  const generateAndSendPDFEmail = useAction(api.invoicePDFEmail.generateAndSendPDFEmail); // PDF email delivery
  const generatePDFAndSendEmail = useAction(api.invoicePDFEmail.generatePDFAndSendEmail); // Combined workflow
  const getPDFUrl = useQuery(
    api.invoices.getPDFUrl,
    invoiceId ? { invoiceId } : "skip"
  );
  const hasPDF = useQuery(
    api.invoices.hasPDF,
    invoiceId ? { invoiceId } : "skip"
  );
  const deletePDF = useMutation(api.invoices.deletePDF);

  // Get invoice and settings data for PDF generation
  const invoice = useQuery(
    api.invoices.get,
    invoiceId ? { id: invoiceId } : "skip"
  );
  const settings = useQuery(api.settings.get);

  const generatePDF = async (): Promise<PDFGenerationResult> => {
    if (!invoiceId) {
      const error = "Invoice ID is required";
      toast.error(error);
      return { success: false, message: error };
    }

    if (!invoice || !settings) {
      const error = invoice === null
        ? "Invoice not found or has been deleted"
        : "Invoice or company settings not available";
      toast.error(error);
      return { success: false, message: error };
    }

    try {
      toast.loading("Generating PDF...", { id: "pdf-generation" });

      // Generate PDF on client side using jsPDF
      const pdfBlob = generateInvoicePDFBlob(invoice, settings);

      // Convert blob to ArrayBuffer for transmission to Convex
      const arrayBuffer = await pdfBlob.arrayBuffer();

      // Store the PDF blob in Convex Storage
      const result = await storePDFBlob({
        invoiceId,
        pdfBlob: arrayBuffer
      });

      if (!result || !result.success) {
        throw new Error(result?.message || "PDF storage failed");
      }

      toast.success("PDF generated and stored successfully!", { id: "pdf-generation" });
      return {
        success: true,
        message: result.message,
        storageId: result.storageId
      };

    } catch (error) {
      console.error("PDF generation error:", error);
      let errorMessage = "Failed to generate PDF";

      if (error instanceof Error) {
        errorMessage = error.message;

        // Handle specific error types
        if (error.message.includes("not found")) {
          errorMessage = "Invoice not found or access denied";
        } else if (error.message.includes("storage")) {
          errorMessage = "Storage service unavailable. Please try again later.";
        } else if (error.message.includes("permission")) {
          errorMessage = "You don't have permission to generate PDF for this invoice";
        }
      }

      toast.error(errorMessage, { id: "pdf-generation" });
      return { success: false, message: errorMessage };
    }
  };

  const downloadPDF = async (): Promise<void> => {
    if (!invoiceId) {
      toast.error("Invoice ID is required");
      return;
    }

    try {
      // First check if PDF exists
      if (!hasPDF) {
        toast.error("No PDF available for this invoice. Please generate one first.");
        return;
      }

      // Get the PDF URL
      const pdfUrl = getPDFUrl;
      if (!pdfUrl) {
        toast.error("PDF URL not available. The PDF may have been deleted or corrupted.");
        return;
      }

      // Validate URL format
      if (!pdfUrl.startsWith('http')) {
        toast.error("Invalid PDF URL format");
        return;
      }

      // Open PDF in new tab
      const newWindow = window.open(pdfUrl, '_blank');
      if (!newWindow) {
        toast.error("Failed to open PDF. Please check your popup blocker settings.");
        return;
      }

      toast.success("PDF opened in new tab");
    } catch (error) {
      console.error("PDF download error:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to download PDF";
      toast.error(errorMessage);
    }
  };

  const downloadPDFDirect = async (): Promise<void> => {
    if (!invoiceId) {
      toast.error("Invoice ID is required");
      return;
    }

    try {
      // Use the HTTP endpoint for direct download
      const downloadUrl = `/api/invoice/${invoiceId}/pdf`;
      window.open(downloadUrl, '_blank');
      toast.success("PDF download started");
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to download PDF";
      toast.error(errorMessage);
    }
  };

  const removePDF = async (): Promise<PDFActionResult> => {
    if (!invoiceId) {
      const error = "Invoice ID is required";
      toast.error(error);
      return { success: false, message: error };
    }

    // Confirm deletion
    if (!window.confirm("Are you sure you want to delete the stored PDF? This action cannot be undone.")) {
      return { success: false, message: "Deletion cancelled by user" };
    }

    try {
      toast.loading("Deleting PDF...", { id: "pdf-deletion" });

      if (!deletePDF) {
        throw new Error("PDF deletion service not available");
      }

      const result = await deletePDF({ invoiceId });

      if (!result || !result.success) {
        throw new Error(result?.message || "PDF deletion failed");
      }

      toast.success("PDF deleted successfully!", { id: "pdf-deletion" });
      return result;
    } catch (error) {
      console.error("PDF deletion error:", error);
      let errorMessage = "Failed to delete PDF";

      if (error instanceof Error) {
        errorMessage = error.message;

        // Handle specific error types
        if (error.message.includes("not found")) {
          errorMessage = "PDF not found or already deleted";
        } else if (error.message.includes("permission")) {
          errorMessage = "You don't have permission to delete this PDF";
        }
      }

      toast.error(errorMessage, { id: "pdf-deletion" });
      return { success: false, message: errorMessage };
    }
  };

  const getPDFInfo = async (): Promise<PDFUrlResponse | null> => {
    if (!invoiceId) {
      return null;
    }

    try {
      // Use the HTTP endpoint to get PDF info
      const response = await fetch(`/api/invoice/${invoiceId}/pdf-url`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to get PDF info');
      }

      return data;
    } catch (error) {
      console.error("Failed to get PDF info:", error);
      return null;
    }
  };

  // Send PDF via email (existing PDF)
  const sendPDFEmail = async (): Promise<PDFEmailDeliveryResult> => {
    if (!invoiceId) {
      toast.error("Invoice ID is required");
      return { success: false, message: "Invoice ID is required" };
    }

    try {
      toast.loading("Sending PDF email...", { id: "pdf-email" });

      const result = await generateAndSendPDFEmail({
        invoiceId,
        options: {
          generatePDF: false, // Use existing PDF
          includeAttachment: false, // Send download link instead
          includeDownloadLink: true,
          cleanupOldPDF: false
        }
      });

      if (!result || !result.success) {
        throw new Error(result?.message || "PDF email sending failed");
      }

      toast.success(`PDF email sent successfully to ${result.customerEmail}!`, { id: "pdf-email" });
      return result;

    } catch (error) {
      console.error("PDF email error:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to send PDF email";
      toast.error(errorMessage, { id: "pdf-email" });
      return { success: false, message: errorMessage };
    }
  };

  // Generate PDF and send via email (combined workflow)
  const generateAndSendPDF = async (): Promise<PDFEmailDeliveryResult> => {
    if (!invoiceId || !invoice || !settings) {
      toast.error("Invoice data is required");
      return { success: false, message: "Invoice data is required" };
    }

    try {
      toast.loading("Generating PDF and sending email...", { id: "pdf-generate-email" });

      // Generate PDF on client side using jsPDF
      const pdfBlob = generateInvoicePDFBlob(invoice, settings);

      // Convert blob to ArrayBuffer for transmission to Convex
      const arrayBuffer = await pdfBlob.arrayBuffer();

      // Generate PDF and send email in one action
      const result = await generatePDFAndSendEmail({
        invoiceId,
        pdfBlob: arrayBuffer,
        options: {
          includeAttachment: false, // Send download link instead
          includeDownloadLink: true,
          cleanupOldPDF: true
        }
      });

      if (!result || !result.success) {
        throw new Error(result?.message || "PDF generation and email sending failed");
      }

      toast.success(`PDF generated and sent successfully to ${result.customerEmail}!`, { id: "pdf-generate-email" });
      return result;

    } catch (error) {
      console.error("PDF generation and email error:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to generate PDF and send email";
      toast.error(errorMessage, { id: "pdf-generate-email" });
      return { success: false, message: errorMessage };
    }
  };

  return {
    // State
    hasPDF: Boolean(hasPDF),
    pdfUrl: getPDFUrl,

    // Actions
    generatePDF,
    downloadPDF,
    downloadPDFDirect,
    removePDF,
    getPDFInfo,

    // PDF Email Actions
    sendPDFEmail,
    generateAndSendPDF,

    // Loading states (you can extend this based on your needs)
    isGenerating: false, // You might want to add loading state management
    isDeleting: false,
  };
}

import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";

export function useStorageUrl(storageId: string | undefined | null) {
  // Always call the hook to maintain the same hooks order
  // If storageId is null or undefined, use "skip" to avoid making the query
  const url = useQuery(
    api.settings.getLogoUrl, 
    storageId && !storageId.startsWith('http') 
      ? { storageId } 
      : "skip"
  );
  
  // Return the URL directly if it's a URL already
  if (storageId && storageId.startsWith('http')) {
    return storageId;
  }
  
  return url || null;
}
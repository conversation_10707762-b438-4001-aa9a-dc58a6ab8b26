# PDF Download Button Fix - Test Plan

## Issue Identified and Fixed

### Problem
The "View & Download Invoice" button in PDF invoice emails was not functioning correctly because:
1. The `invoiceUrl` variable was being passed as `undefined` to the template
2. This resulted in empty `href` attributes in the email template
3. Recipients couldn't download PDFs when clicking the button

### Root Cause
In `convex/invoicePDFEmail.ts`, the `generateTemplateVariables` function was called with:
```typescript
generateTemplateVariables(brandedInvoice, settings, undefined, pdfOptions)
//                                                   ^^^^^^^^^ 
//                                                   This should be the PDF URL
```

### Fix Applied
1. **Fixed URL Parameter**: Changed the call to pass the PDF URL as the `invoiceUrl`:
   ```typescript
   generateTemplateVariables(brandedInvoice, settings, pdfUrl, pdfOptions)
   ```

2. **Optimized Template**: Improved the email template to avoid duplicate download buttons when the main URL already points to the PDF

3. **Enhanced Variable Logic**: Updated the `pdfDownloadButton` generation to avoid redundancy when URLs are the same

## Test Scenarios

### Scenario 1: PDF Email with Download Link
**Setup**: PDF exists in storage, `includeDownloadLink: true`, `includeAttachment: false`

**Expected Results**:
- ✅ `{invoiceUrl}` contains valid PDF download URL
- ✅ "View & Download Invoice" button has working href
- ✅ `{pdfDownloadButton}` is empty (to avoid duplication)
- ✅ `{attachmentInfo}` shows download instructions
- ✅ Recipients can click button to download PDF

### Scenario 2: PDF Email with Attachment
**Setup**: PDF exists in storage, `includeDownloadLink: false`, `includeAttachment: true`

**Expected Results**:
- ✅ `{invoiceUrl}` contains valid PDF download URL (fallback)
- ✅ "View & Download Invoice" button works as backup
- ✅ `{attachmentInfo}` shows attachment notification
- ✅ PDF is attached to email
- ✅ Recipients can download from attachment or button

### Scenario 3: PDF Email with Both Link and Attachment
**Setup**: PDF exists in storage, `includeDownloadLink: true`, `includeAttachment: true`

**Expected Results**:
- ✅ `{invoiceUrl}` contains valid PDF download URL
- ✅ "View & Download Invoice" button works
- ✅ `{attachmentInfo}` shows both options available
- ✅ PDF is attached to email
- ✅ Recipients have multiple download options

### Scenario 4: Fallback Template (No Database Template)
**Setup**: No custom email template in database

**Expected Results**:
- ✅ Falls back to hardcoded PDF template
- ✅ Download buttons still work correctly
- ✅ PDF URLs are properly embedded
- ✅ Email sends successfully

## Manual Testing Steps

### 1. Access Application
- Navigate to http://localhost:5174
- Log in as admin user
- Go to Invoices section

### 2. Create Test Invoice
- Create a new invoice with customer email
- Add line items and save invoice
- Note the invoice ID for testing

### 3. Generate PDF
- Click "Generate PDF" button on invoice
- Verify PDF is created and stored
- Check that PDF storage ID is saved to invoice

### 4. Send PDF Email
- Click "Email PDF" button
- Choose options (download link vs attachment)
- Send email to test recipient

### 5. Verify Email Content
- Check email in recipient's inbox
- Inspect email HTML source
- Verify "View & Download Invoice" button has valid href
- Test clicking the button

### 6. Test Download Functionality
- Click "View & Download Invoice" button
- Verify PDF downloads correctly
- Check PDF opens and displays invoice content
- Confirm download URL is accessible

## Expected URL Format

### Convex Storage URLs
```
https://[deployment].convex.cloud/api/storage/[storage-id]
```

### URL Characteristics
- ✅ Publicly accessible (no authentication required)
- ✅ Long expiration time (typically 7+ days)
- ✅ Direct download of PDF file
- ✅ Proper MIME type (application/pdf)

## Debugging Information

### Log Messages to Check
```
Email custom-template-used-pdf: {
  templateId: "...",
  templateName: "...",
  hasDownloadUrl: true,
  hasAttachment: false
}
```

### Template Variables to Verify
```javascript
{
  invoiceUrl: "https://[deployment].convex.cloud/api/storage/[id]",
  pdfDownloadUrl: "https://[deployment].convex.cloud/api/storage/[id]",
  hasAttachment: "false",
  downloadInstructions: "Click the download button...",
  pdfDownloadButton: "", // Empty when URLs are same
  attachmentInfo: "📄 Your PDF invoice is ready for download"
}
```

## Success Criteria

### ✅ Primary Fix Verification
1. **Button Functionality**: "View & Download Invoice" button has valid href
2. **URL Generation**: PDF storage URLs are properly generated
3. **Template Processing**: Variables are correctly replaced in email content
4. **Download Success**: Recipients can successfully download PDF files

### ✅ Edge Case Handling
1. **No PDF Available**: Proper error handling and user feedback
2. **Storage URL Failure**: Fallback behavior works correctly
3. **Template Errors**: Graceful degradation to hardcoded template
4. **Network Issues**: Appropriate error messages

### ✅ User Experience
1. **Clear Instructions**: Download instructions are helpful
2. **Professional Appearance**: Email template looks professional
3. **Multiple Options**: Both button and attachment work when available
4. **Consistent Behavior**: Same experience across different email clients

## Implementation Status

### ✅ Completed Fixes
1. Fixed `invoiceUrl` parameter in PDF email generation
2. Optimized template to avoid duplicate download buttons
3. Enhanced variable generation logic
4. Updated email template structure
5. Added proper error handling and logging

### 🔄 Testing Phase
- Manual testing of PDF email workflow
- Verification of download button functionality
- End-to-end testing from invoice creation to PDF download
- Cross-email-client compatibility testing

# Race Condition Fix Test Plan

## Issue Description
When deleting an invoice, the deletion succeeds but causes two "Invoice not found" errors to appear in the Convex logs:
1. Error in `invoices:get` query at line 48 in `convex/invoices.ts`
2. Error in `invoices:getPDFUrl` query at line 806 in `convex/invoices.ts`

## Root Cause
The race condition occurs because:
1. Invoice is successfully deleted from the database
2. React components with `useQuery` hooks for `invoices:get` and `invoices:getPDFUrl` continue to poll these queries
3. Since the invoice no longer exists, these queries throw "Invoice not found" errors

## Changes Made

### 1. Updated Convex Queries to Handle Deleted Invoices Gracefully

#### `convex/invoices.ts` - `get` query (line 39-61)
- Changed from throwing "Invoice not found" error to returning `null`
- This prevents error logs when components query deleted invoices

#### `convex/invoices.ts` - `getPDFUrl` query (line 790-814)  
- Changed from throwing "Invoice not found" error to returning `null`
- This prevents error logs when PDF operations query deleted invoices

### 2. Updated React Components to Handle Null Responses

#### `src/hooks/usePDFOperations.ts` - `generatePDF` function (line 30-43)
- Added specific error message when invoice is null (deleted)
- Provides better user feedback when trying to operate on deleted invoices

#### `src/components/Invoices.tsx` - Added cleanup effect (line 34-43)
- Added useEffect to detect when selected invoice is no longer in the list
- Automatically clears selection when invoice is deleted
- Prevents UI from showing stale data

#### `src/components/SMS/SMSComposer.tsx` - Fixed query usage (line 30-33)
- Changed from non-existent `api.invoices.getById` to `api.invoices.get`
- Component already had proper null handling with conditional rendering

## Test Scenarios

### Manual Testing Steps
1. **Setup**: Start the development server and navigate to invoices page
2. **Select Invoice**: Click on an invoice to view its details in the preview panel
3. **Delete Invoice**: While the invoice is selected, delete it using the delete button
4. **Verify No Errors**: Check Convex logs for any "Invoice not found" errors
5. **Verify UI Cleanup**: Confirm that the selection is cleared and no stale data is shown

### Expected Results
- ✅ No "Invoice not found" errors in Convex logs
- ✅ Selected invoice is automatically cleared when deleted
- ✅ PDF operations gracefully handle deleted invoices
- ✅ SMS composer handles deleted invoice references properly

### Error Scenarios to Test
1. **PDF Operations on Deleted Invoice**: Try to generate/download PDF for deleted invoice
2. **Status Update on Deleted Invoice**: Try to update status of deleted invoice
3. **Email Send on Deleted Invoice**: Try to send email for deleted invoice

## Implementation Notes

### Why Return Null Instead of Throwing Errors?
- Prevents unnecessary error logs in production
- Allows React components to handle deletion gracefully
- Maintains backward compatibility with existing error handling
- Follows React Query best practices for handling deleted resources

### Component Cleanup Strategy
- Added automatic selection clearing when invoice is deleted
- Maintained existing manual cleanup in deletion handler
- Used React useEffect for reactive cleanup based on data changes

## Monitoring
After deployment, monitor Convex logs for:
- Absence of "Invoice not found" errors during deletion operations
- Proper handling of concurrent operations on deleted invoices
- No new errors introduced by the null return values

import { v } from "convex/values";
import { query, mutation } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";
import { 
  checkMasterPermission, 
  checkAdminPermission, 
  canAssignRole, 
  canManageUser,
  getRoleLevel 
} from "./auth";
import { Id } from "./_generated/dataModel";

// Get current user helper
async function getCurrentUser(ctx: any) {
  const userId = await getAuthUserId(ctx);
  if (!userId) throw new Error("Not authenticated");
  return userId;
}

// List all users (Master only)
export const listAllUsers = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getCurrentUser(ctx);
    await checkMasterPermission(ctx, userId);

    const users = await ctx.db.query("users").collect();
    
    return users.map(user => ({
      _id: user._id,
      name: user.name || "Unknown User",
      email: user.email || "",
      role: user.role || "staff",
      department: user.department || "",
      phone: user.phone || "",
      bio: user.bio || "",
      _creationTime: user._creationTime,
    }));
  },
});

// Request role change (can be self-request or Master assigning)
export const requestRoleChange = mutation({
  args: {
    targetUserId: v.id("users"),
    requestedRole: v.string(),
    reason: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const requesterId = await getCurrentUser(ctx);
    const requester = await ctx.db.get(requesterId);
    const targetUser = await ctx.db.get(args.targetUserId);

    if (!requester || !targetUser) {
      throw new Error("User not found");
    }

    // Check if requester can assign this role
    if (!canAssignRole(requester.role || "staff", args.requestedRole)) {
      throw new Error("Unauthorized - Cannot assign this role");
    }

    // Check if requester can manage target user
    if (!canManageUser(requester.role || "staff", targetUser.role || "staff")) {
      throw new Error("Unauthorized - Cannot manage this user");
    }

    // If Master is making the request, apply immediately
    if (requester.role === "master") {
      // Record the change in history
      await ctx.db.insert("roleHistory", {
        userId: args.targetUserId,
        previousRole: targetUser.role || "staff",
        newRole: args.requestedRole,
        changedBy: requesterId,
        reason: args.reason,
        createdAt: Date.now(),
      });

      // Update the user's role
      await ctx.db.patch(args.targetUserId, { role: args.requestedRole });

      return { success: true, immediate: true };
    }

    // For non-Master users, create a role request for approval
    const existingRequest = await ctx.db
      .query("roleRequests")
      .withIndex("by_user", (q) => q.eq("userId", args.targetUserId))
      .filter((q) => q.eq(q.field("status"), "pending"))
      .first();

    if (existingRequest) {
      throw new Error("A role request is already pending for this user");
    }

    await ctx.db.insert("roleRequests", {
      userId: args.targetUserId,
      requestedRole: args.requestedRole,
      currentRole: targetUser.role || "staff",
      requestedBy: requesterId,
      status: "pending",
      reason: args.reason,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });

    return { success: true, immediate: false };
  },
});

// List pending role requests (Master only)
export const listPendingRoleRequests = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getCurrentUser(ctx);
    await checkMasterPermission(ctx, userId);

    const requests = await ctx.db
      .query("roleRequests")
      .withIndex("by_status", (q) => q.eq("status", "pending"))
      .collect();

    // Enrich with user information
    const enrichedRequests = await Promise.all(
      requests.map(async (request) => {
        const user = await ctx.db.get(request.userId);
        const requestedBy = await ctx.db.get(request.requestedBy);
        
        return {
          ...request,
          user: {
            _id: user?._id,
            name: user?.name || "Unknown User",
            email: user?.email || "",
          },
          requestedByUser: {
            _id: requestedBy?._id,
            name: requestedBy?.name || "Unknown User",
            email: requestedBy?.email || "",
          },
        };
      })
    );

    return enrichedRequests;
  },
});

// Approve or reject role request (Master only)
export const reviewRoleRequest = mutation({
  args: {
    requestId: v.id("roleRequests"),
    action: v.union(v.literal("approve"), v.literal("reject")),
    reviewNotes: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const reviewerId = await getCurrentUser(ctx);
    await checkMasterPermission(ctx, reviewerId);

    const request = await ctx.db.get(args.requestId);
    if (!request) {
      throw new Error("Role request not found");
    }

    if (request.status !== "pending") {
      throw new Error("Role request has already been reviewed");
    }

    const targetUser = await ctx.db.get(request.userId);
    if (!targetUser) {
      throw new Error("Target user not found");
    }

    // Update the request status
    await ctx.db.patch(args.requestId, {
      status: args.action === "approve" ? "approved" : "rejected",
      reviewedBy: reviewerId,
      reviewedAt: Date.now(),
      reviewNotes: args.reviewNotes,
      updatedAt: Date.now(),
    });

    // If approved, update the user's role and record in history
    if (args.action === "approve") {
      await ctx.db.insert("roleHistory", {
        userId: request.userId,
        previousRole: request.currentRole,
        newRole: request.requestedRole,
        changedBy: reviewerId,
        reason: `Approved role request: ${request.reason || "No reason provided"}`,
        requestId: args.requestId,
        createdAt: Date.now(),
      });

      await ctx.db.patch(request.userId, { role: request.requestedRole });
    }

    return { success: true };
  },
});

// Get role change history for a user (Master only)
export const getUserRoleHistory = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    const currentUserId = await getCurrentUser(ctx);
    await checkMasterPermission(ctx, currentUserId);

    const history = await ctx.db
      .query("roleHistory")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .order("desc")
      .collect();

    // Enrich with user information
    const enrichedHistory = await Promise.all(
      history.map(async (entry) => {
        const changedBy = await ctx.db.get(entry.changedBy);
        
        return {
          ...entry,
          changedByUser: {
            _id: changedBy?._id,
            name: changedBy?.name || "Unknown User",
            email: changedBy?.email || "",
          },
        };
      })
    );

    return enrichedHistory;
  },
});

// Get role statistics (Master only)
export const getRoleStatistics = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getCurrentUser(ctx);
    await checkMasterPermission(ctx, userId);

    const users = await ctx.db.query("users").collect();
    const pendingRequests = await ctx.db
      .query("roleRequests")
      .withIndex("by_status", (q) => q.eq("status", "pending"))
      .collect();

    const roleCount = users.reduce((acc, user) => {
      const role = user.role || "staff";
      acc[role] = (acc[role] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalUsers: users.length,
      roleDistribution: roleCount,
      pendingRequests: pendingRequests.length,
    };
  },
});

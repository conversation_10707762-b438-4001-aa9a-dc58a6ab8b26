// Secure encryption utilities for BH-CRM
// Uses AES-256-CBC encryption for sensitive data protection

import CryptoJS from 'crypto-js';

export interface EncryptionResult {
  encryptedData: string;
  iv: string;
  authTag: string;
  keyVersion: number;
  salt: string;
}

export interface DecryptionInput {
  encryptedData: string;
  iv: string;
  authTag: string;
  keyVersion?: number;
  salt: string;
}

export interface EncryptionConfig {
  masterKey: string;
  keyVersion: number;
  algorithm: string;
}

// Encryption configuration with key rotation support
class EncryptionManager {
  private config: EncryptionConfig;
  private keyHistory: Map<number, string> = new Map();

  constructor() {
    this.config = this.loadEncryptionConfig();
    this.initializeKeyHistory();
  }

  private loadEncryptionConfig(): EncryptionConfig {
    // Get encryption key from environment variables
    const masterKey = process.env.ENCRYPTION_MASTER_KEY;
    const keyVersion = parseInt(process.env.ENCRYPTION_KEY_VERSION || '1');
    
    if (!masterKey) {
      throw new Error('ENCRYPTION_MASTER_KEY environment variable is required');
    }

    if (masterKey.length < 32) {
      throw new Error('ENCRYPTION_MASTER_KEY must be at least 32 characters long');
    }

    return {
      masterKey,
      keyVersion,
      algorithm: 'AES-256-GCM'
    };
  }

  private initializeKeyHistory(): void {
    // Store current key
    this.keyHistory.set(this.config.keyVersion, this.config.masterKey);
    
    // Load historical keys for decryption of old data
    for (let i = 1; i < this.config.keyVersion; i++) {
      const historicalKey = process.env[`ENCRYPTION_KEY_V${i}`];
      if (historicalKey) {
        this.keyHistory.set(i, historicalKey);
      }
    }
  }

  private getKey(version?: number): string {
    const keyVersion = version || this.config.keyVersion;
    const key = this.keyHistory.get(keyVersion);
    
    if (!key) {
      throw new Error(`Encryption key version ${keyVersion} not found`);
    }
    
    return key;
  }

  private generateIV(): string {
    // Generate a random 96-bit (12-byte) IV for GCM mode
    return CryptoJS.lib.WordArray.random(12).toString();
  }

  private generateSalt(): string {
    // Generate a random 256-bit (32-byte) salt for key derivation
    return CryptoJS.lib.WordArray.random(32).toString();
  }

  private deriveKey(masterKey: string, salt: string): string {
    // Use PBKDF2 for key derivation with salt
    return CryptoJS.PBKDF2(masterKey, salt, {
      keySize: 256/32,
      iterations: 10000
    }).toString();
  }

  /**
   * Encrypt sensitive data using AES-256-GCM
   */
  async encrypt(plaintext: string): Promise<EncryptionResult> {
    try {
      if (!plaintext || plaintext.trim().length === 0) {
        throw new Error('Cannot encrypt empty or null data');
      }

      const iv = this.generateIV();
      const salt = this.generateSalt();
      const masterKey = this.getKey();

      // Derive encryption key from master key and salt
      const derivedKey = this.deriveKey(masterKey, salt);

      // Encrypt using AES-256-CBC
      const encrypted = CryptoJS.AES.encrypt(plaintext, derivedKey, {
        iv: CryptoJS.enc.Hex.parse(iv),
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
      });

      // For CBC mode, we'll use a simulated auth tag by hashing the encrypted data
      const authTag = CryptoJS.SHA256(encrypted.toString() + derivedKey + iv).toString().substring(0, 32);

      return {
        encryptedData: encrypted.toString(),
        iv: iv,
        authTag: authTag,
        salt: salt,
        keyVersion: this.config.keyVersion
      };
    } catch (error) {
      console.error('Encryption failed:', error);
      throw new Error('Failed to encrypt data');
    }
  }

  /**
   * Decrypt sensitive data using AES-256-GCM
   */
  async decrypt(input: DecryptionInput): Promise<string> {
    try {
      const { encryptedData, iv, authTag, salt, keyVersion } = input;

      if (!encryptedData || !iv || !authTag || !salt) {
        throw new Error('Missing required decryption parameters');
      }

      const masterKey = this.getKey(keyVersion);

      // Derive the same key used for encryption
      const derivedKey = this.deriveKey(masterKey, salt);

      // Verify auth tag
      const expectedAuthTag = CryptoJS.SHA256(encryptedData + derivedKey + iv).toString().substring(0, 32);
      if (authTag !== expectedAuthTag) {
        throw new Error('Authentication failed - data may have been tampered with');
      }

      // Decrypt using AES-256-CBC
      const decrypted = CryptoJS.AES.decrypt(encryptedData, derivedKey, {
        iv: CryptoJS.enc.Hex.parse(iv),
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
      });

      const decryptedText = decrypted.toString(CryptoJS.enc.Utf8);

      if (!decryptedText) {
        throw new Error('Decryption failed - invalid data or key');
      }

      return decryptedText;
    } catch (error) {
      console.error('Decryption failed:', error);
      if (error instanceof Error && error.message.includes('Authentication failed')) {
        throw error;
      }
      throw new Error('Failed to decrypt data');
    }
  }

  /**
   * Validate encryption configuration
   */
  async validateConfig(): Promise<{ isValid: boolean; error?: string }> {
    try {
      if (!this.config.masterKey) {
        return { isValid: false, error: 'Master encryption key not configured' };
      }

      if (this.config.masterKey.length < 32) {
        return { isValid: false, error: 'Encryption key too short (minimum 32 characters)' };
      }

      // Test encryption/decryption
      const testData = 'test-encryption-validation';
      const encrypted = await this.encrypt(testData);
      const decrypted = await this.decrypt(encrypted);

      if (decrypted !== testData) {
        return { isValid: false, error: 'Encryption validation test failed' };
      }

      return { isValid: true };
    } catch (error) {
      return { isValid: false, error: `Encryption configuration error: ${error}` };
    }
  }

  /**
   * Get current key version for rotation tracking
   */
  getCurrentKeyVersion(): number {
    return this.config.keyVersion;
  }

  /**
   * Check if a key version is supported
   */
  isKeyVersionSupported(version: number): boolean {
    return this.keyHistory.has(version);
  }
}

// Singleton instance
let encryptionManager: EncryptionManager | null = null;

function getEncryptionManager(): EncryptionManager {
  if (!encryptionManager) {
    encryptionManager = new EncryptionManager();
  }
  return encryptionManager;
}

// Public API functions
export async function encryptCredential(credential: string): Promise<EncryptionResult> {
  const manager = getEncryptionManager();
  return await manager.encrypt(credential);
}

export async function decryptCredential(input: DecryptionInput): Promise<string> {
  const manager = getEncryptionManager();
  return await manager.decrypt(input);
}

export async function validateEncryptionConfig(): Promise<{ isValid: boolean; error?: string }> {
  try {
    const manager = getEncryptionManager();
    return await manager.validateConfig();
  } catch (error) {
    return { isValid: false, error: `Configuration error: ${error}` };
  }
}

export function getCurrentKeyVersion(): number {
  const manager = getEncryptionManager();
  return manager.getCurrentKeyVersion();
}

export function isKeyVersionSupported(version: number): boolean {
  const manager = getEncryptionManager();
  return manager.isKeyVersionSupported(version);
}

// Legacy support functions for backward compatibility
export async function encryptCredentialLegacy(credential: string): Promise<string> {
  const result = await encryptCredential(credential);
  // Return a serialized format for storage
  return JSON.stringify(result);
}

export async function decryptCredentialLegacy(encryptedData: string): Promise<string> {
  try {
    // Try new format first
    const input = JSON.parse(encryptedData) as DecryptionInput;
    return await decryptCredential(input);
  } catch (parseError) {
    // Check if it's legacy Base64 format - SECURITY RISK: This should be migrated
    console.warn('Attempting to decrypt legacy Base64 credential - migration required');
    try {
      const decoded = Buffer.from(encryptedData, 'base64').toString('utf-8');
      // Log security event for monitoring
      console.error('SECURITY WARNING: Legacy Base64 credential accessed - immediate migration required');
      return decoded;
    } catch (error) {
      throw new Error('Failed to decrypt credential - invalid format and migration failed');
    }
  }
}

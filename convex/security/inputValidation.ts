// Comprehensive input validation and sanitization for BH-CRM
// Prevents XSS, injection attacks, and ensures data integrity

import { v } from "convex/values";

// HTML sanitization patterns
const HTML_TAGS_REGEX = /<[^>]*>/g;
const SCRIPT_REGEX = /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi;
const JAVASCRIPT_PROTOCOL_REGEX = /javascript:/gi;
const DATA_PROTOCOL_REGEX = /data:/gi;
const VBSCRIPT_PROTOCOL_REGEX = /vbscript:/gi;

// SQL injection patterns (for NoSQL context)
const SQL_INJECTION_PATTERNS = [
  /(\$where|\$regex|\$ne|\$gt|\$lt|\$gte|\$lte|\$in|\$nin)/gi,
  /(union|select|insert|update|delete|drop|create|alter|exec|execute)/gi,
  /('|(\\')|(;|\\x00|\\n|\\r|\\x1a))/gi
];

// XSS patterns
const XSS_PATTERNS = [
  /<script[^>]*>.*?<\/script>/gi,
  /javascript:/gi,
  /on\w+\s*=/gi,
  /<iframe[^>]*>.*?<\/iframe>/gi,
  /<object[^>]*>.*?<\/object>/gi,
  /<embed[^>]*>/gi,
  /<link[^>]*>/gi,
  /<meta[^>]*>/gi
];

export interface ValidationResult {
  isValid: boolean;
  sanitized?: string;
  errors: string[];
  warnings: string[];
}

export interface ValidationOptions {
  allowHTML?: boolean;
  maxLength?: number;
  minLength?: number;
  required?: boolean;
  pattern?: RegExp;
  customSanitizer?: (input: string) => string;
}

// Sanitize string input to prevent XSS and injection attacks
export function sanitizeString(input: string, options: ValidationOptions = {}): ValidationResult {
  const result: ValidationResult = {
    isValid: true,
    errors: [],
    warnings: []
  };

  if (!input && options.required) {
    result.isValid = false;
    result.errors.push("Field is required");
    return result;
  }

  if (!input) {
    result.sanitized = "";
    return result;
  }

  let sanitized = input.trim();

  // Length validation
  if (options.maxLength && sanitized.length > options.maxLength) {
    result.warnings.push(`Input truncated to ${options.maxLength} characters`);
    sanitized = sanitized.substring(0, options.maxLength);
  }

  if (options.minLength && sanitized.length < options.minLength) {
    result.isValid = false;
    result.errors.push(`Minimum length is ${options.minLength} characters`);
  }

  // Pattern validation
  if (options.pattern && !options.pattern.test(sanitized)) {
    result.isValid = false;
    result.errors.push("Input format is invalid");
  }

  // XSS prevention
  for (const pattern of XSS_PATTERNS) {
    if (pattern.test(sanitized)) {
      result.warnings.push("Potentially malicious content detected and removed");
      sanitized = sanitized.replace(pattern, '');
    }
  }

  // SQL injection prevention
  for (const pattern of SQL_INJECTION_PATTERNS) {
    if (pattern.test(sanitized)) {
      result.warnings.push("Potentially dangerous query patterns detected and removed");
      sanitized = sanitized.replace(pattern, '');
    }
  }

  // Protocol sanitization
  sanitized = sanitized.replace(JAVASCRIPT_PROTOCOL_REGEX, '');
  sanitized = sanitized.replace(VBSCRIPT_PROTOCOL_REGEX, '');
  sanitized = sanitized.replace(DATA_PROTOCOL_REGEX, '');

  // HTML sanitization
  if (!options.allowHTML) {
    if (HTML_TAGS_REGEX.test(sanitized)) {
      result.warnings.push("HTML tags detected and removed");
      sanitized = sanitized.replace(HTML_TAGS_REGEX, '');
    }
  } else {
    // If HTML is allowed, still remove dangerous scripts
    sanitized = sanitized.replace(SCRIPT_REGEX, '');
  }

  // Custom sanitization
  if (options.customSanitizer) {
    sanitized = options.customSanitizer(sanitized);
  }

  result.sanitized = sanitized;
  return result;
}

// Validate and sanitize email addresses
export function validateEmail(email: string): ValidationResult {
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  
  const result = sanitizeString(email, {
    required: true,
    maxLength: 255,
    pattern: emailRegex
  });

  if (result.sanitized) {
    result.sanitized = result.sanitized.toLowerCase();
  }

  return result;
}

// Validate and sanitize phone numbers
export function validatePhoneNumber(phone: string): ValidationResult {
  const phoneRegex = /^\+?[\d\s\-\(\)\.]{7,20}$/;
  
  return sanitizeString(phone, {
    required: true,
    maxLength: 20,
    pattern: phoneRegex,
    customSanitizer: (input) => input.replace(/[^\d\+\-\(\)\s\.]/g, '')
  });
}

// Validate and sanitize names (person, company)
export function validateName(name: string): ValidationResult {
  const nameRegex = /^[a-zA-Z\s\-'\.&,]+$/;
  
  return sanitizeString(name, {
    required: true,
    minLength: 1,
    maxLength: 100,
    pattern: nameRegex
  });
}

// Validate and sanitize addresses
export function validateAddress(address: string): ValidationResult {
  const addressRegex = /^[a-zA-Z0-9\s\-'\.#,]+$/;
  
  return sanitizeString(address, {
    required: true,
    maxLength: 500,
    pattern: addressRegex
  });
}

// Validate and sanitize notes/descriptions
export function validateNotes(notes: string): ValidationResult {
  return sanitizeString(notes, {
    required: false,
    maxLength: 2000,
    allowHTML: false
  });
}

// Validate numeric inputs
export function validateNumber(value: any, min?: number, max?: number): ValidationResult {
  const result: ValidationResult = {
    isValid: true,
    errors: [],
    warnings: []
  };

  const num = Number(value);
  
  if (isNaN(num)) {
    result.isValid = false;
    result.errors.push("Must be a valid number");
    return result;
  }

  if (min !== undefined && num < min) {
    result.isValid = false;
    result.errors.push(`Must be at least ${min}`);
  }

  if (max !== undefined && num > max) {
    result.isValid = false;
    result.errors.push(`Must be at most ${max}`);
  }

  result.sanitized = num.toString();
  return result;
}

// Validate invoice data
export function validateInvoiceData(data: any): ValidationResult {
  const result: ValidationResult = {
    isValid: true,
    errors: [],
    warnings: []
  };

  // Validate required fields
  if (!data.customerId) {
    result.errors.push("Customer is required");
    result.isValid = false;
  }

  if (!data.items || !Array.isArray(data.items) || data.items.length === 0) {
    result.errors.push("At least one line item is required");
    result.isValid = false;
  }

  // Validate line items
  if (data.items) {
    data.items.forEach((item: any, index: number) => {
      const descValidation = sanitizeString(item.description, { required: true, maxLength: 500 });
      if (!descValidation.isValid) {
        result.errors.push(`Item ${index + 1}: ${descValidation.errors.join(', ')}`);
        result.isValid = false;
      }

      const qtyValidation = validateNumber(item.quantity, 0.01);
      if (!qtyValidation.isValid) {
        result.errors.push(`Item ${index + 1} quantity: ${qtyValidation.errors.join(', ')}`);
        result.isValid = false;
      }

      const priceValidation = validateNumber(item.unitPrice, 0);
      if (!priceValidation.isValid) {
        result.errors.push(`Item ${index + 1} price: ${priceValidation.errors.join(', ')}`);
        result.isValid = false;
      }
    });
  }

  // Validate tax rate
  if (data.taxRate !== undefined) {
    const taxValidation = validateNumber(data.taxRate, 0, 100);
    if (!taxValidation.isValid) {
      result.errors.push(`Tax rate: ${taxValidation.errors.join(', ')}`);
      result.isValid = false;
    }
  }

  // Validate notes
  if (data.notes) {
    const notesValidation = validateNotes(data.notes);
    if (!notesValidation.isValid) {
      result.errors.push(`Notes: ${notesValidation.errors.join(', ')}`);
      result.isValid = false;
    }
    result.warnings.push(...notesValidation.warnings);
  }

  return result;
}

// Validate customer data
export function validateCustomerData(data: any): ValidationResult {
  const result: ValidationResult = {
    isValid: true,
    errors: [],
    warnings: []
  };

  // Validate name
  const nameValidation = validateName(data.name);
  if (!nameValidation.isValid) {
    result.errors.push(`Name: ${nameValidation.errors.join(', ')}`);
    result.isValid = false;
  }
  result.warnings.push(...nameValidation.warnings);

  // Validate email
  const emailValidation = validateEmail(data.email);
  if (!emailValidation.isValid) {
    result.errors.push(`Email: ${emailValidation.errors.join(', ')}`);
    result.isValid = false;
  }
  result.warnings.push(...emailValidation.warnings);

  // Validate phone
  const phoneValidation = validatePhoneNumber(data.phone);
  if (!phoneValidation.isValid) {
    result.errors.push(`Phone: ${phoneValidation.errors.join(', ')}`);
    result.isValid = false;
  }
  result.warnings.push(...phoneValidation.warnings);

  // Validate address
  const addressValidation = validateAddress(data.address);
  if (!addressValidation.isValid) {
    result.errors.push(`Address: ${addressValidation.errors.join(', ')}`);
    result.isValid = false;
  }
  result.warnings.push(...addressValidation.warnings);

  // Validate optional fields
  if (data.notes) {
    const notesValidation = validateNotes(data.notes);
    if (!notesValidation.isValid) {
      result.errors.push(`Notes: ${notesValidation.errors.join(', ')}`);
      result.isValid = false;
    }
    result.warnings.push(...notesValidation.warnings);
  }

  return result;
}

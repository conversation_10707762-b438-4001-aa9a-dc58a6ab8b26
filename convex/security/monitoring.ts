// Security monitoring and alerting for BH-CRM
// Tracks security events, rate limit violations, and suspicious activities

import { v } from "convex/values";
import { query, mutation, internalMutation } from "../_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";
import { internal } from "../_generated/api";

// Get current user helper
async function getCurrentUser(ctx: any) {
  const userId = await getAuthUserId(ctx);
  if (!userId) throw new Error("Not authenticated");
  return userId;
}

// Check if user has admin role
async function checkAdminPermission(ctx: any, userId: string) {
  const user = await ctx.db.get(userId);
  if (!user || (user.role !== "admin" && user.role !== "master")) {
    throw new Error("Unauthorized - Admin access required");
  }
}

export interface SecurityEvent {
  type: 'RATE_LIMIT_EXCEEDED' | 'AUTHENTICATION_FAILURE' | 'SUSPICIOUS_ACTIVITY' | 'ENCRYPTION_ERROR' | 'UNAUTHORIZED_ACCESS';
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  userId?: string;
  ipAddress?: string;
  userAgent?: string;
  details: Record<string, any>;
  timestamp: number;
}

export interface SecurityMetrics {
  totalEvents: number;
  criticalEvents: number;
  highEvents: number;
  rateLimitViolations: number;
  authFailures: number;
  suspiciousActivities: number;
  topThreats: Array<{
    type: string;
    count: number;
    lastSeen: number;
  }>;
  topIPs: Array<{
    ip: string;
    eventCount: number;
    threatLevel: string;
  }>;
}

// Log security events
export const logSecurityEvent = internalMutation({
  args: {
    type: v.string(),
    severity: v.string(),
    userId: v.optional(v.string()),
    ipAddress: v.optional(v.string()),
    userAgent: v.optional(v.string()),
    details: v.any(),
  },
  handler: async (ctx, args) => {
    // Store security event (you would need to add this table to schema)
    // For now, we'll log to console and could store in a dedicated table
    const event: SecurityEvent = {
      type: args.type as any,
      severity: args.severity as any,
      userId: args.userId,
      ipAddress: args.ipAddress,
      userAgent: args.userAgent,
      details: args.details,
      timestamp: Date.now()
    };

    console.log(`SECURITY EVENT [${event.severity}]: ${event.type}`, {
      userId: event.userId,
      ip: event.ipAddress?.substring(0, 8) + '***', // Partial IP for privacy
      details: event.details,
      timestamp: new Date(event.timestamp).toISOString()
    });

    // In a production system, you would:
    // 1. Store in a dedicated security_events table
    // 2. Send alerts for CRITICAL/HIGH severity events
    // 3. Integrate with external monitoring systems
    // 4. Trigger automated responses for certain threat types

    return event.timestamp;
  },
});

// Get security dashboard metrics
export const getSecurityMetrics = query({
  args: {
    timeRange: v.optional(v.string()), // '24h', '7d', '30d'
  },
  handler: async (ctx, args): Promise<SecurityMetrics> => {
    const userId = await getCurrentUser(ctx);
    await checkAdminPermission(ctx, userId);

    const timeRange = args.timeRange || '24h';
    const now = Date.now();
    let startTime: number;

    switch (timeRange) {
      case '24h':
        startTime = now - (24 * 60 * 60 * 1000);
        break;
      case '7d':
        startTime = now - (7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startTime = now - (30 * 24 * 60 * 60 * 1000);
        break;
      default:
        startTime = now - (24 * 60 * 60 * 1000);
    }

    // Get rate limit violations
    const rateLimitViolations = await ctx.db
      .query("rateLimits")
      .withIndex("by_last_request", (q: any) => q.gte("lastRequest", startTime))
      .filter((q: any) => q.eq(q.field("blocked"), true))
      .collect();

    // Analyze rate limit data for security metrics
    const blockedUsers = new Set();
    const ipThreats = new Map<string, number>();
    const actionThreats = new Map<string, number>();

    rateLimitViolations.forEach(violation => {
      blockedUsers.add(violation.userId);
      
      // Count by action type
      const currentCount = actionThreats.get(violation.action) || 0;
      actionThreats.set(violation.action, currentCount + 1);
      
      // If userId looks like an IP (for IP-based rate limiting)
      if (violation.userId.includes('.')) {
        const currentIpCount = ipThreats.get(violation.userId) || 0;
        ipThreats.set(violation.userId, currentIpCount + 1);
      }
    });

    // Create top threats array
    const topThreats = Array.from(actionThreats.entries())
      .map(([type, count]) => ({
        type,
        count,
        lastSeen: now // Simplified - would get actual last seen from data
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // Create top IPs array
    const topIPs = Array.from(ipThreats.entries())
      .map(([ip, eventCount]) => ({
        ip: ip.substring(0, 8) + '***', // Mask IP for privacy
        eventCount,
        threatLevel: eventCount > 10 ? 'HIGH' : eventCount > 5 ? 'MEDIUM' : 'LOW'
      }))
      .sort((a, b) => b.eventCount - a.eventCount)
      .slice(0, 10);

    return {
      totalEvents: rateLimitViolations.length,
      criticalEvents: rateLimitViolations.filter(v => v.action === 'login_attempt' && v.requestCount > 10).length,
      highEvents: rateLimitViolations.filter(v => v.blocked && v.requestCount > 5).length,
      rateLimitViolations: rateLimitViolations.length,
      authFailures: rateLimitViolations.filter(v => v.action.includes('login') || v.action.includes('auth')).length,
      suspiciousActivities: rateLimitViolations.filter(v => v.requestCount > 20).length,
      topThreats,
      topIPs
    };
  },
});

// Get recent security events
export const getRecentSecurityEvents = query({
  args: {
    limit: v.optional(v.number()),
    severity: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getCurrentUser(ctx);
    await checkAdminPermission(ctx, userId);

    const limit = args.limit || 50;
    const now = Date.now();
    const last24Hours = now - (24 * 60 * 60 * 1000);

    // Get recent rate limit violations as security events
    const recentViolations = await ctx.db
      .query("rateLimits")
      .withIndex("by_last_request", (q: any) => q.gte("lastRequest", last24Hours))
      .order("desc")
      .take(limit);

    // Convert to security event format
    const events = recentViolations.map(violation => ({
      id: violation._id,
      type: violation.blocked ? 'RATE_LIMIT_EXCEEDED' : 'SUSPICIOUS_ACTIVITY',
      severity: violation.requestCount > 20 ? 'CRITICAL' : 
                violation.requestCount > 10 ? 'HIGH' : 
                violation.requestCount > 5 ? 'MEDIUM' : 'LOW',
      userId: violation.userId.includes('.') ? undefined : violation.userId, // Don't show if it's an IP
      ipAddress: violation.userId.includes('.') ? violation.userId.substring(0, 8) + '***' : undefined,
      action: violation.action,
      requestCount: violation.requestCount,
      blocked: violation.blocked,
      timestamp: violation.lastRequest,
      details: {
        action: violation.action,
        requestCount: violation.requestCount,
        windowStart: violation.windowStart,
        blocked: violation.blocked
      }
    }));

    // Filter by severity if specified
    if (args.severity) {
      return events.filter(event => event.severity === args.severity);
    }

    return events;
  },
});

// Get security status overview
export const getSecurityStatus = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getCurrentUser(ctx);
    await checkAdminPermission(ctx, userId);

    const now = Date.now();
    const last24Hours = now - (24 * 60 * 60 * 1000);

    // Check recent rate limit violations
    const recentViolations = await ctx.db
      .query("rateLimits")
      .withIndex("by_last_request", (q: any) => q.gte("lastRequest", last24Hours))
      .collect();

    const criticalViolations = recentViolations.filter(v => v.requestCount > 20).length;
    const blockedUsers = recentViolations.filter(v => v.blocked).length;
    const authFailures = recentViolations.filter(v => v.action.includes('login')).length;

    // Determine overall security status
    let status: 'SECURE' | 'WARNING' | 'ALERT' | 'CRITICAL';
    let message: string;

    if (criticalViolations > 5) {
      status = 'CRITICAL';
      message = `${criticalViolations} critical security violations detected in the last 24 hours`;
    } else if (blockedUsers > 10 || authFailures > 20) {
      status = 'ALERT';
      message = `High number of security events: ${blockedUsers} blocked users, ${authFailures} auth failures`;
    } else if (blockedUsers > 3 || authFailures > 10) {
      status = 'WARNING';
      message = `Moderate security activity: ${blockedUsers} blocked users, ${authFailures} auth failures`;
    } else {
      status = 'SECURE';
      message = 'No significant security threats detected';
    }

    return {
      status,
      message,
      lastUpdated: now,
      metrics: {
        totalViolations: recentViolations.length,
        criticalViolations,
        blockedUsers,
        authFailures,
        uniqueIPs: new Set(recentViolations.filter(v => v.userId.includes('.')).map(v => v.userId)).size
      }
    };
  },
});

// Manual security alert (for admin use)
export const triggerSecurityAlert = mutation({
  args: {
    type: v.string(),
    severity: v.string(),
    message: v.string(),
    details: v.optional(v.any()),
  },
  handler: async (ctx, args) => {
    const userId = await getCurrentUser(ctx);
    await checkAdminPermission(ctx, userId);

    // Log the manual security alert
    await ctx.runMutation(internal.security.monitoring.logSecurityEvent, {
      type: args.type,
      severity: args.severity,
      userId: userId,
      details: {
        message: args.message,
        manualAlert: true,
        triggeredBy: userId,
        ...args.details
      }
    });

    console.log(`MANUAL SECURITY ALERT [${args.severity}]: ${args.type} - ${args.message}`);

    return { success: true, timestamp: Date.now() };
  },
});

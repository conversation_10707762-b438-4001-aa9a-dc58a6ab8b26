// Active rate limiting system for BH-CRM
// Implements sliding window rate limiting with database persistence

import { v } from "convex/values";
import { mutation, query, internalMutation, internalQuery } from "../_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";

export interface RateLimitConfig {
  action: string;
  windowMs: number;
  maxRequests: number;
  blockDurationMs?: number;
}

export interface RateLimitResult {
  allowed: boolean;
  remaining: number;
  resetTime: number;
  retryAfter?: number;
  error?: string;
}

export interface RateLimitEntry {
  userId: string;
  action: string;
  windowStart: number;
  requestCount: number;
  lastRequest: number;
  blocked: boolean;
  blockExpires?: number;
}

// Progressive rate limit configurations
export interface ProgressiveRateLimitConfig extends RateLimitConfig {
  progressiveLimits?: Array<{
    attempts: number;
    windowMs: number;
    blockDurationMs: number;
  }>;
}

// Rate limit configurations with progressive penalties
export const RATE_LIMIT_CONFIGS: Record<string, ProgressiveRateLimitConfig> = {
  // Authentication limits with progressive blocking
  login_attempt: {
    action: "login_attempt",
    windowMs: 5 * 60 * 1000, // 5 minutes base window
    maxRequests: 3, // Reduced from 5
    blockDurationMs: 5 * 60 * 1000, // 5 minutes initial block
    progressiveLimits: [
      { attempts: 3, windowMs: 5 * 60 * 1000, blockDurationMs: 5 * 60 * 1000 }, // 5 min block after 3 attempts
      { attempts: 6, windowMs: 15 * 60 * 1000, blockDurationMs: 30 * 60 * 1000 }, // 30 min block after 6 attempts
      { attempts: 10, windowMs: 60 * 60 * 1000, blockDurationMs: 24 * 60 * 60 * 1000 }, // 24 hour block after 10 attempts
    ]
  },
  password_reset: {
    action: "password_reset",
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 2, // Reduced from 3
    blockDurationMs: 60 * 60 * 1000, // 1 hour block
  },

  // SMS limits with tighter controls
  sms_send: {
    action: "sms_send",
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 5, // Reduced from 10
    blockDurationMs: 5 * 60 * 1000, // 5 minute block
  },
  sms_send_hourly: {
    action: "sms_send_hourly",
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 50, // Reduced from 100
    blockDurationMs: 60 * 60 * 1000, // 1 hour block
  },
  sms_send_daily: {
    action: "sms_send_daily",
    windowMs: 24 * 60 * 60 * 1000, // 24 hours
    maxRequests: 200, // Reduced from 500
    blockDurationMs: 24 * 60 * 60 * 1000, // 24 hour block
  },

  // Configuration changes
  template_change: {
    action: "template_change",
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 20,
  },
  config_change: {
    action: "config_change",
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 5,
  },

  // API endpoints with enhanced security
  api_general: {
    action: "api_general",
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 60, // Reduced from 100
    blockDurationMs: 5 * 60 * 1000, // 5 minute block
  },
  api_sensitive: {
    action: "api_sensitive",
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 5, // Reduced from 10
    blockDurationMs: 15 * 60 * 1000, // 15 minute block
  },

  // IP-based rate limiting for additional security
  ip_based_auth: {
    action: "ip_based_auth",
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 20, // 20 attempts per IP
    blockDurationMs: 60 * 60 * 1000, // 1 hour block
  },
};

class RateLimitManager {
  private cleanupInterval: NodeJS.Timeout | null = null;

  constructor() {
    // Start cleanup process for expired entries
    this.startCleanup();
  }

  private startCleanup(): void {
    // Clean up expired entries every 5 minutes
    this.cleanupInterval = setInterval(() => {
      this.cleanupExpiredEntries();
    }, 5 * 60 * 1000);
  }

  private async cleanupExpiredEntries(): Promise<void> {
    // This would be implemented as an internal mutation
    // For now, we'll rely on the database cleanup
  }

  /**
   * Check progressive rate limits for enhanced security
   */
  private async checkProgressiveRateLimit(
    ctx: any,
    userId: string,
    action: string,
    config: ProgressiveRateLimitConfig
  ): Promise<RateLimitResult> {
    if (!config.progressiveLimits) {
      return this.checkBasicRateLimit(ctx, userId, action, config);
    }

    const now = Date.now();

    // Get all rate limit entries for this user/action to calculate total attempts
    const allEntries = await ctx.db
      .query("rateLimits")
      .withIndex("by_user_action", (q: any) =>
        q.eq("userId", userId).eq("action", action)
      )
      .collect();

    // Calculate total attempts in the last 24 hours
    const dayStart = now - (24 * 60 * 60 * 1000);
    const totalAttempts = allEntries
      .filter((entry: any) => entry.lastRequest > dayStart)
      .reduce((sum: number, entry: any) => sum + entry.requestCount, 0);

    // Find applicable progressive limit
    let applicableLimit = config.progressiveLimits[0];
    for (const limit of config.progressiveLimits) {
      if (totalAttempts >= limit.attempts) {
        applicableLimit = limit;
      }
    }

    // Use the progressive limit configuration
    const progressiveConfig: RateLimitConfig = {
      action: config.action,
      windowMs: applicableLimit.windowMs,
      maxRequests: Math.max(1, config.maxRequests - Math.floor(totalAttempts / 5)), // Reduce allowed requests
      blockDurationMs: applicableLimit.blockDurationMs
    };

    return this.checkBasicRateLimit(ctx, userId, action, progressiveConfig);
  }

  /**
   * Basic rate limit check
   */
  private async checkBasicRateLimit(
    ctx: any,
    userId: string,
    action: string,
    config: RateLimitConfig
  ): Promise<RateLimitResult> {
    const now = Date.now();
    const windowStart = now - config.windowMs;

    // Get existing rate limit entry
    const existing = await ctx.db
      .query("rateLimits")
      .withIndex("by_user_action", (q: any) =>
        q.eq("userId", userId).eq("action", action)
      )
      .first();

    // Check if user is currently blocked
    if (existing?.blocked && existing.blockExpires && existing.blockExpires > now) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: existing.blockExpires,
        retryAfter: existing.blockExpires - now,
        error: "Rate limit exceeded - temporarily blocked",
      };
    }

    // Calculate current request count in window
    let requestCount = 0;
    let resetTime = now + config.windowMs;

    if (existing && existing.windowStart > windowStart) {
      // Existing window is still valid
      requestCount = existing.requestCount;
      resetTime = existing.windowStart + config.windowMs;
    }

    // Check if limit would be exceeded
    if (requestCount >= config.maxRequests) {
      // Block user if configured
      if (config.blockDurationMs) {
        const blockExpires = now + config.blockDurationMs;
        await this.updateRateLimitEntry(ctx, userId, action, {
          windowStart: existing?.windowStart || now,
          requestCount,
          lastRequest: now,
          blocked: true,
          blockExpires,
        });

        return {
          allowed: false,
          remaining: 0,
          resetTime: blockExpires,
          retryAfter: config.blockDurationMs,
          error: "Rate limit exceeded - temporarily blocked",
        };
      }

      return {
        allowed: false,
        remaining: 0,
        resetTime,
        error: "Rate limit exceeded",
      };
    }

    return {
      allowed: true,
      remaining: config.maxRequests - requestCount - 1,
      resetTime,
    };
  }

  /**
   * Check if an action is rate limited for a user
   */
  async checkRateLimit(
    ctx: any,
    userId: string,
    action: string,
    ipAddress?: string
  ): Promise<RateLimitResult> {
    const config = RATE_LIMIT_CONFIGS[action];
    if (!config) {
      return {
        allowed: true,
        remaining: 999,
        resetTime: Date.now() + 60000,
      };
    }

    // Check IP-based rate limiting for authentication actions
    if (ipAddress && (action === 'login_attempt' || action === 'password_reset')) {
      const ipResult = await this.checkBasicRateLimit(ctx, ipAddress, 'ip_based_auth', RATE_LIMIT_CONFIGS.ip_based_auth);
      if (!ipResult.allowed) {
        return {
          ...ipResult,
          error: "Too many attempts from this IP address - temporarily blocked"
        };
      }
    }

    // Use progressive rate limiting for authentication actions
    if (config.progressiveLimits) {
      return this.checkProgressiveRateLimit(ctx, userId, action, config);
    }

    return this.checkBasicRateLimit(ctx, userId, action, config);
  }

  /**
   * Record a rate limited action
   */
  async recordAction(
    ctx: any,
    userId: string,
    action: string,
    ipAddress?: string
  ): Promise<void> {
    const config = RATE_LIMIT_CONFIGS[action];
    if (!config) return;

    const now = Date.now();
    const windowStart = now - config.windowMs;

    const existing = await ctx.db
      .query("rateLimits")
      .withIndex("by_user_action", (q: any) => 
        q.eq("userId", userId).eq("action", action)
      )
      .first();

    let requestCount = 1;
    let newWindowStart = now;

    if (existing && existing.windowStart > windowStart) {
      // Existing window is still valid
      requestCount = existing.requestCount + 1;
      newWindowStart = existing.windowStart;
    }

    await this.updateRateLimitEntry(ctx, userId, action, {
      windowStart: newWindowStart,
      requestCount,
      lastRequest: now,
      blocked: false,
    });
  }

  private async updateRateLimitEntry(
    ctx: any,
    userId: string,
    action: string,
    data: Partial<RateLimitEntry>
  ): Promise<void> {
    const existing = await ctx.db
      .query("rateLimits")
      .withIndex("by_user_action", (q: any) => 
        q.eq("userId", userId).eq("action", action)
      )
      .first();

    const entryData = {
      userId,
      action,
      windowStart: data.windowStart || Date.now(),
      requestCount: data.requestCount || 1,
      lastRequest: data.lastRequest || Date.now(),
      blocked: data.blocked || false,
      blockExpires: data.blockExpires,
    };

    if (existing) {
      await ctx.db.patch(existing._id, entryData);
    } else {
      await ctx.db.insert("rateLimits", entryData);
    }
  }

  /**
   * Get rate limit status for monitoring
   */
  async getRateLimitStatus(
    ctx: any,
    userId: string,
    action?: string
  ): Promise<RateLimitEntry[]> {
    if (action) {
      const entry = await ctx.db
        .query("rateLimits")
        .withIndex("by_user_action", (q: any) => 
          q.eq("userId", userId).eq("action", action)
        )
        .first();
      return entry ? [entry] : [];
    }

    return await ctx.db
      .query("rateLimits")
      .withIndex("by_user", (q: any) => q.eq("userId", userId))
      .collect();
  }

  /**
   * Reset rate limits for a user (admin function)
   */
  async resetRateLimits(
    ctx: any,
    userId: string,
    action?: string
  ): Promise<void> {
    if (action) {
      const entry = await ctx.db
        .query("rateLimits")
        .withIndex("by_user_action", (q: any) => 
          q.eq("userId", userId).eq("action", action)
        )
        .first();
      if (entry) {
        await ctx.db.delete(entry._id);
      }
    } else {
      const entries = await ctx.db
        .query("rateLimits")
        .withIndex("by_user", (q: any) => q.eq("userId", userId))
        .collect();
      
      for (const entry of entries) {
        await ctx.db.delete(entry._id);
      }
    }
  }
}

// Singleton instance
let rateLimitManager: RateLimitManager | null = null;

function getRateLimitManager(): RateLimitManager {
  if (!rateLimitManager) {
    rateLimitManager = new RateLimitManager();
  }
  return rateLimitManager;
}

// Public API functions
export async function checkRateLimit(
  ctx: any,
  userId: string,
  action: string,
  ipAddress?: string
): Promise<RateLimitResult> {
  const manager = getRateLimitManager();
  return manager.checkRateLimit(ctx, userId, action, ipAddress);
}

export async function recordRateLimitedAction(
  ctx: any,
  userId: string,
  action: string,
  ipAddress?: string
): Promise<void> {
  const manager = getRateLimitManager();
  await manager.recordAction(ctx, userId, action, ipAddress);
}

export async function getRateLimitStatus(
  ctx: any,
  userId: string,
  action?: string
): Promise<RateLimitEntry[]> {
  const manager = getRateLimitManager();
  return manager.getRateLimitStatus(ctx, userId, action);
}

export async function resetUserRateLimits(
  ctx: any,
  userId: string,
  action?: string
): Promise<void> {
  const manager = getRateLimitManager();
  await manager.resetRateLimits(ctx, userId, action);
}

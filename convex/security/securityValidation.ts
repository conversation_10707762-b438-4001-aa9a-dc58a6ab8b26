// Security validation and testing utilities
// Comprehensive validation of encryption and rate limiting systems

import { v } from "convex/values";
import { query, mutation } from "../_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";
import { 
  validateEncryptionConfig, 
  encryptCredential, 
  decryptCredential,
  getCurrentKeyVersion 
} from "./encryption";
import { 
  checkRateLimit, 
  recordRateLimitedAction,
  RATE_LIMIT_CONFIGS 
} from "./rateLimiting";

export interface SecurityValidationReport {
  encryption: {
    configured: boolean;
    keyVersion: number;
    testPassed: boolean;
    error?: string;
  };
  rateLimiting: {
    configured: boolean;
    testPassed: boolean;
    activeConfigs: number;
    error?: string;
  };
  overall: {
    status: "SECURE" | "WARNING" | "CRITICAL";
    issues: string[];
    recommendations: string[];
  };
}

export interface EncryptionTestResult {
  testName: string;
  passed: boolean;
  duration: number;
  error?: string;
  details?: any;
}

export interface RateLimitTestResult {
  action: string;
  passed: boolean;
  remaining: number;
  resetTime: number;
  error?: string;
}

// Get current user helper
async function getCurrentUser(ctx: any) {
  const userId = await getAuthUserId(ctx);
  if (!userId) throw new Error("Not authenticated");
  return userId;
}

// Check if user has admin role
async function checkAdmin(ctx: any, userId: string) {
  const user = await ctx.db.get(userId);
  if (!user || (user.role !== "admin" && user.role !== "master")) {
    throw new Error("Unauthorized - Admin access required");
  }
  return user;
}

// Comprehensive security validation
export const validateSecurityConfiguration = query({
  args: {},
  handler: async (ctx): Promise<SecurityValidationReport> => {
    const userId = await getCurrentUser(ctx);
    await checkAdmin(ctx, userId);

    const report: SecurityValidationReport = {
      encryption: {
        configured: false,
        keyVersion: 0,
        testPassed: false,
      },
      rateLimiting: {
        configured: false,
        testPassed: false,
        activeConfigs: 0,
      },
      overall: {
        status: "CRITICAL",
        issues: [],
        recommendations: [],
      },
    };

    // Test encryption configuration
    try {
      const encryptionValidation = await validateEncryptionConfig();
      report.encryption.configured = encryptionValidation.isValid;
      
      if (encryptionValidation.isValid) {
        report.encryption.keyVersion = getCurrentKeyVersion();
        report.encryption.testPassed = true;
      } else {
        report.encryption.error = encryptionValidation.error;
        report.overall.issues.push(`Encryption: ${encryptionValidation.error}`);
      }
    } catch (error) {
      report.encryption.error = `Encryption test failed: ${error}`;
      report.overall.issues.push(report.encryption.error);
    }

    // Test rate limiting configuration
    try {
      const configCount = Object.keys(RATE_LIMIT_CONFIGS).length;
      report.rateLimiting.activeConfigs = configCount;
      report.rateLimiting.configured = configCount > 0;
      
      // Test rate limiting functionality
      const testResult = await checkRateLimit(ctx, userId, "api_general");
      report.rateLimiting.testPassed = testResult.allowed !== undefined;
    } catch (error) {
      report.rateLimiting.error = `Rate limiting test failed: ${error}`;
      report.overall.issues.push(report.rateLimiting.error);
    }

    // Determine overall status
    if (report.encryption.testPassed && report.rateLimiting.testPassed) {
      report.overall.status = "SECURE";
    } else if (report.encryption.configured || report.rateLimiting.configured) {
      report.overall.status = "WARNING";
    } else {
      report.overall.status = "CRITICAL";
    }

    // Generate recommendations
    if (!report.encryption.configured) {
      report.overall.recommendations.push("Configure encryption by setting ENCRYPTION_MASTER_KEY environment variable");
    }
    if (!report.rateLimiting.configured) {
      report.overall.recommendations.push("Rate limiting configuration appears incomplete");
    }
    if (report.encryption.keyVersion === 1) {
      report.overall.recommendations.push("Consider implementing key rotation for enhanced security");
    }

    return report;
  },
});

// Test encryption functionality
export const testEncryption = mutation({
  args: {
    testData: v.optional(v.string()),
  },
  handler: async (ctx, args): Promise<EncryptionTestResult[]> => {
    const userId = await getCurrentUser(ctx);
    await checkAdmin(ctx, userId);

    const tests: EncryptionTestResult[] = [];
    const testData = args.testData || "test-encryption-data-12345";

    // Test 1: Basic encryption/decryption
    try {
      const startTime = Date.now();
      const encrypted = await encryptCredential(testData);
      const decrypted = await decryptCredential(encrypted);
      const duration = Date.now() - startTime;

      tests.push({
        testName: "Basic Encryption/Decryption",
        passed: decrypted === testData,
        duration,
        details: {
          originalLength: testData.length,
          encryptedLength: JSON.stringify(encrypted).length,
          keyVersion: getCurrentKeyVersion(),
        },
      });
    } catch (error) {
      tests.push({
        testName: "Basic Encryption/Decryption",
        passed: false,
        duration: 0,
        error: String(error),
      });
    }

    // Test 2: Empty string handling
    try {
      const startTime = Date.now();
      encryptCredential("");
      const duration = Date.now() - startTime;

      tests.push({
        testName: "Empty String Handling",
        passed: false, // Should fail
        duration,
        error: "Expected error for empty string",
      });
    } catch (error) {
      tests.push({
        testName: "Empty String Handling",
        passed: true, // Should throw error
        duration: 0,
        details: { expectedError: true },
      });
    }

    // Test 3: Large data encryption
    try {
      const largeData = "x".repeat(1000);
      const startTime = Date.now();
      const encrypted = await encryptCredential(largeData);
      const decrypted = await decryptCredential(encrypted);
      const duration = Date.now() - startTime;

      tests.push({
        testName: "Large Data Encryption",
        passed: decrypted === largeData,
        duration,
        details: {
          dataSize: largeData.length,
          encryptedSize: JSON.stringify(encrypted).length,
        },
      });
    } catch (error) {
      tests.push({
        testName: "Large Data Encryption",
        passed: false,
        duration: 0,
        error: String(error),
      });
    }

    // Test 4: Special characters
    try {
      const specialData = "!@#$%^&*()_+-=[]{}|;:,.<>?`~";
      const startTime = Date.now();
      const encrypted = await encryptCredential(specialData);
      const decrypted = await decryptCredential(encrypted);
      const duration = Date.now() - startTime;

      tests.push({
        testName: "Special Characters",
        passed: decrypted === specialData,
        duration,
        details: { specialChars: specialData.length },
      });
    } catch (error) {
      tests.push({
        testName: "Special Characters",
        passed: false,
        duration: 0,
        error: String(error),
      });
    }

    return tests;
  },
});

// Test rate limiting functionality
export const testRateLimiting = mutation({
  args: {
    testAction: v.optional(v.string()),
    testCount: v.optional(v.number()),
  },
  handler: async (ctx, args): Promise<RateLimitTestResult[]> => {
    const userId = await getCurrentUser(ctx);
    await checkAdmin(ctx, userId);

    const testAction = args.testAction || "api_general";
    const testCount = args.testCount || 5;
    const testUserId = `test-user-${Date.now()}`;
    
    const results: RateLimitTestResult[] = [];

    try {
      // Test multiple requests to trigger rate limiting
      for (let i = 0; i < testCount; i++) {
        const result = await checkRateLimit(ctx, testUserId, testAction);
        
        results.push({
          action: `${testAction}_${i + 1}`,
          passed: result.allowed !== undefined,
          remaining: result.remaining,
          resetTime: result.resetTime,
          error: result.error,
        });

        // Record the action if allowed
        if (result.allowed) {
          await recordRateLimitedAction(ctx, testUserId, testAction);
        }
      }

      // Clean up test data
      const { resetUserRateLimits } = await import("./rateLimiting");
      await resetUserRateLimits(ctx, testUserId);

    } catch (error) {
      results.push({
        action: testAction,
        passed: false,
        remaining: 0,
        resetTime: 0,
        error: String(error),
      });
    }

    return results;
  },
});

// Get security metrics for monitoring
export const getSecurityMetrics = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getCurrentUser(ctx);
    await checkAdmin(ctx, userId);

    const now = Date.now();
    const oneHourAgo = now - (60 * 60 * 1000);
    const oneDayAgo = now - (24 * 60 * 60 * 1000);

    // Get rate limit entries
    const recentRateLimits = await ctx.db
      .query("rateLimits")
      .withIndex("by_last_request")
      .filter((q) => q.gte(q.field("lastRequest"), oneDayAgo))
      .collect();

    const blockedUsers = recentRateLimits.filter(limit => 
      limit.blocked && limit.blockExpires && limit.blockExpires > now
    ).length;

    const activeUsers = new Set(
      recentRateLimits
        .filter(limit => limit.lastRequest > oneHourAgo)
        .map(limit => limit.userId)
    ).size;

    // Get Twilio settings to check encryption
    const twilioSettings = await ctx.db
      .query("twilioSettings")
      .withIndex("by_active", (q) => q.eq("isActive", true))
      .first();

    const encryptionStatus = twilioSettings ? "ENCRYPTED" : "NOT_CONFIGURED";

    return {
      rateLimiting: {
        totalEntries: recentRateLimits.length,
        blockedUsers,
        activeUsers,
        topActions: getTopActions(recentRateLimits),
      },
      encryption: {
        status: encryptionStatus,
        keyVersion: getCurrentKeyVersion(),
        twilioConfigured: !!twilioSettings,
      },
      timestamp: now,
    };
  },
});

// Migrate existing Twilio credentials to use new encryption
export const migrateExistingCredentials = mutation({
  args: {},
  handler: async (ctx) => {
    const userId = await getCurrentUser(ctx);
    await checkAdmin(ctx, userId);

    // Validate encryption is configured
    const encryptionValidation = await validateEncryptionConfig();
    if (!encryptionValidation.isValid) {
      throw new Error(`Cannot migrate: ${encryptionValidation.error}`);
    }

    // Get all Twilio settings
    const allSettings = await ctx.db.query("twilioSettings").collect();
    let migratedCount = 0;
    let errorCount = 0;
    const errors: string[] = [];

    for (const setting of allSettings) {
      try {
        // Try to decrypt with new system first (already migrated)
        try {
          await decryptCredential(JSON.parse(setting.authToken));
          continue; // Already migrated
        } catch {
          // Not in new format, try legacy decryption
        }

        // Try legacy Base64 decryption
        const decrypted = Buffer.from(setting.authToken, 'base64').toString('utf-8');

        // Re-encrypt with new system
        const newEncrypted = await encryptCredential(decrypted);

        // Update the record
        await ctx.db.patch(setting._id, {
          authToken: JSON.stringify(newEncrypted),
          updatedAt: Date.now(),
        });

        migratedCount++;
      } catch (error) {
        errorCount++;
        errors.push(`Setting ${setting._id}: ${error}`);
      }
    }

    return {
      success: true,
      migratedCount,
      errorCount,
      errors,
      totalSettings: allSettings.length,
    };
  },
});

// Helper function to get top actions
function getTopActions(rateLimits: any[]): Array<{ action: string; count: number }> {
  const actionCounts = new Map<string, number>();

  rateLimits.forEach(limit => {
    const count = actionCounts.get(limit.action) || 0;
    actionCounts.set(limit.action, count + limit.requestCount);
  });

  return Array.from(actionCounts.entries())
    .map(([action, count]) => ({ action, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 5);
}

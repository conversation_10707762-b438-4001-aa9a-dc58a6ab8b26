// Authentication rate limiting middleware
// Protects against brute-force attacks and credential stuffing

import { v } from "convex/values";
import { mutation, action } from "../_generated/server";
import { 
  checkRateLimit, 
  recordRateLimitedAction,
  RateLimitResult 
} from "./rateLimiting";

export interface AuthRateLimitResult {
  allowed: boolean;
  remaining: number;
  resetTime: number;
  retryAfter?: number;
  error?: string;
  headers: Record<string, string>;
}

/**
 * Check rate limit for authentication attempts
 */
export async function checkAuthRateLimit(
  ctx: any,
  identifier: string, // email or user ID
  action: "login_attempt" | "password_reset" | "signup_attempt" = "login_attempt",
  ipAddress?: string
): Promise<AuthRateLimitResult> {
  try {
    // Use email/identifier as the rate limit key for auth attempts
    const result = await checkRateLimit(ctx, identifier, action, ipAddress);
    
    // Generate rate limit headers
    const headers = generateRateLimitHeaders(result, action);
    
    return {
      allowed: result.allowed,
      remaining: result.remaining,
      resetTime: result.resetTime,
      retryAfter: result.retryAfter,
      error: result.error,
      headers,
    };
  } catch (error) {
    console.error("Auth rate limit check failed:", error);
    // Fail secure - deny access if rate limiting fails
    return {
      allowed: false,
      remaining: 0,
      resetTime: Date.now() + 15 * 60 * 1000, // 15 minutes
      error: "Rate limiting service unavailable",
      headers: {
        "X-RateLimit-Limit": "5",
        "X-RateLimit-Remaining": "0",
        "X-RateLimit-Reset": String(Math.ceil((Date.now() + 15 * 60 * 1000) / 1000)),
      },
    };
  }
}

/**
 * Record an authentication attempt
 */
export async function recordAuthAttempt(
  ctx: any,
  identifier: string,
  action: "login_attempt" | "password_reset" | "signup_attempt" = "login_attempt",
  success: boolean,
  ipAddress?: string
): Promise<void> {
  try {
    // Always record the attempt
    await recordRateLimitedAction(ctx, identifier, action, ipAddress);
    
    // For failed login attempts, also record under IP address if available
    if (!success && ipAddress && action === "login_attempt") {
      await recordRateLimitedAction(ctx, ipAddress, "login_attempt_ip", ipAddress);
    }
    
    // Log security event
    console.log("Auth attempt recorded:", {
      identifier: identifier.substring(0, 3) + "***", // Partial masking
      action,
      success,
      ipAddress: ipAddress ? ipAddress.substring(0, 7) + "***" : undefined,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Failed to record auth attempt:", error);
    // Don't throw error to avoid breaking the auth flow
  }
}

/**
 * Generate standard rate limit headers
 */
function generateRateLimitHeaders(
  result: RateLimitResult,
  action: string
): Record<string, string> {
  const config = {
    login_attempt: { limit: 5, window: 900 }, // 5 attempts per 15 minutes
    password_reset: { limit: 3, window: 3600 }, // 3 attempts per hour
    signup_attempt: { limit: 10, window: 3600 }, // 10 attempts per hour
  };
  
  const actionConfig = config[action as keyof typeof config] || config.login_attempt;
  
  return {
    "X-RateLimit-Limit": String(actionConfig.limit),
    "X-RateLimit-Remaining": String(Math.max(0, result.remaining)),
    "X-RateLimit-Reset": String(Math.ceil(result.resetTime / 1000)),
    ...(result.retryAfter && {
      "Retry-After": String(Math.ceil(result.retryAfter / 1000)),
    }),
  };
}

/**
 * Enhanced login rate limiting with IP-based protection
 */
export async function checkLoginRateLimit(
  ctx: any,
  email: string,
  ipAddress?: string
): Promise<AuthRateLimitResult> {
  // Check both email-based and IP-based rate limits
  const emailLimit = await checkAuthRateLimit(ctx, email, "login_attempt", ipAddress);
  
  if (!emailLimit.allowed) {
    return emailLimit;
  }
  
  // Also check IP-based rate limiting if IP is available
  if (ipAddress) {
    const ipLimit = await checkRateLimit(ctx, ipAddress, "login_attempt_ip", ipAddress);
    
    if (!ipLimit.allowed) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: ipLimit.resetTime,
        retryAfter: ipLimit.retryAfter,
        error: "Too many login attempts from this IP address",
        headers: {
          "X-RateLimit-Limit": "20", // IP limit is higher
          "X-RateLimit-Remaining": "0",
          "X-RateLimit-Reset": String(Math.ceil(ipLimit.resetTime / 1000)),
          ...(ipLimit.retryAfter && {
            "Retry-After": String(Math.ceil(ipLimit.retryAfter / 1000)),
          }),
        },
      };
    }
  }
  
  return emailLimit;
}

/**
 * Check if an account should be temporarily locked
 */
export async function checkAccountLockout(
  ctx: any,
  email: string
): Promise<{ locked: boolean; unlockTime?: number; reason?: string }> {
  try {
    // Check if user has exceeded the block threshold
    const result = await checkRateLimit(ctx, email, "login_attempt");
    
    if (!result.allowed && result.retryAfter) {
      return {
        locked: true,
        unlockTime: result.resetTime,
        reason: "Too many failed login attempts",
      };
    }
    
    return { locked: false };
  } catch (error) {
    console.error("Account lockout check failed:", error);
    return { locked: false };
  }
}

/**
 * Admin function to unlock an account
 */
export const unlockAccount = mutation({
  args: {
    email: v.string(),
    adminUserId: v.id("users"),
  },
  handler: async (ctx, args) => {
    // Verify admin permissions
    const admin = await ctx.db.get(args.adminUserId);
    if (!admin || admin.role !== "admin") {
      throw new Error("Unauthorized - Admin access required");
    }
    
    // Reset rate limits for the account
    const { resetUserRateLimits } = await import("./rateLimiting");
    await resetUserRateLimits(ctx, args.email, "login_attempt");
    
    // Log admin action
    console.log("Account unlocked by admin:", {
      email: args.email.substring(0, 3) + "***",
      adminId: args.adminUserId,
      timestamp: new Date().toISOString(),
    });
    
    return { success: true };
  },
});

/**
 * Get rate limit status for monitoring
 */
export const getRateLimitStatus = mutation({
  args: {
    identifier: v.string(),
    adminUserId: v.id("users"),
  },
  handler: async (ctx, args) => {
    // Verify admin permissions
    const admin = await ctx.db.get(args.adminUserId);
    if (!admin || admin.role !== "admin") {
      throw new Error("Unauthorized - Admin access required");
    }
    
    const { getRateLimitStatus: getStatus } = await import("./rateLimiting");
    return await getStatus(ctx, args.identifier);
  },
});

/**
 * Middleware function to apply rate limiting to auth endpoints
 */
export async function withAuthRateLimit<T>(
  ctx: any,
  identifier: string,
  action: "login_attempt" | "password_reset" | "signup_attempt",
  operation: () => Promise<T>,
  ipAddress?: string
): Promise<T> {
  // Check rate limit before proceeding
  const rateLimitResult = await checkAuthRateLimit(ctx, identifier, action, ipAddress);
  
  if (!rateLimitResult.allowed) {
    const error = new Error(rateLimitResult.error || "Rate limit exceeded");
    // Attach rate limit info to error for client handling
    (error as any).rateLimitInfo = {
      retryAfter: rateLimitResult.retryAfter,
      resetTime: rateLimitResult.resetTime,
      headers: rateLimitResult.headers,
    };
    throw error;
  }
  
  let success = false;
  let result: T;
  
  try {
    result = await operation();
    success = true;
    return result;
  } catch (error) {
    success = false;
    throw error;
  } finally {
    // Record the attempt regardless of success/failure
    await recordAuthAttempt(ctx, identifier, action, success, ipAddress);
  }
}

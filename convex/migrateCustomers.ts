import { mutation } from "./_generated/server";

// A one-time migration to add missing required fields to existing customer records
export const migrateExistingCustomers = mutation({
  args: {},
  handler: async (ctx) => {
    // Get all customers
    const customers = await ctx.db.query("customers").collect();
    const now = Date.now();
    let migratedCount = 0;

    // Update each customer
    for (const customer of customers) {
      const updates: Record<string, any> = {};
      let needsUpdate = false;

      if (customer.status === undefined) {
        updates.status = "active";
        needsUpdate = true;
      }

      if (customer.createdAt === undefined) {
        updates.createdAt = customer._creationTime || now;
        needsUpdate = true;
      }

      if (customer.updatedAt === undefined) {
        updates.updatedAt = now;
        needsUpdate = true;
      }

      if (needsUpdate) {
        await ctx.db.patch(customer._id, updates);
        migratedCount++;
      }
    }

    return { migratedCount, totalCustomers: customers.length };
  },
});
import { auth } from "./auth";
import router from "./router";
import { httpAction } from "./_generated/server";
import { internal } from "./_generated/api";
import { getAuthUserId } from "@convex-dev/auth/server";

// Security headers for all HTTP responses
const SECURITY_HEADERS = {
  'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline' https://chef.convex.dev; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https://*.convex.cloud https://*.convex.site; frame-ancestors 'none';",
  'X-Frame-Options': 'DENY',
  'X-Content-Type-Options': 'nosniff',
  'X-XSS-Protection': '1; mode=block',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Permissions-Policy': 'camera=(), microphone=(), geolocation=(), payment=()',
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
  'Cache-Control': 'no-cache, no-store, must-revalidate',
  'Pragma': 'no-cache',
  'Expires': '0'
};

// Helper function to create secure response
function createSecureResponse(body: any, options: { status?: number; headers?: Record<string, string> } = {}) {
  const responseHeaders = {
    ...SECURITY_HEADERS,
    ...options.headers
  };

  return new Response(body, {
    status: options.status || 200,
    headers: responseHeaders
  });
}

// Create a copy of the router to add our routes to
const http = router;

// Add auth routes
auth.addHttpRoutes(http);

// Add a route to get a storage URL by ID
http.route({
  path: "/storage/:storageId",
  method: "GET",
  handler: httpAction(async (ctx, request) => {
    const url = new URL(request.url);
    const storageId = url.pathname.split('/').pop();

    if (!storageId) {
      return createSecureResponse("Storage ID is required", { status: 400 });
    }

    // Validate storageId format to prevent injection
    if (!/^[a-zA-Z0-9_-]+$/.test(storageId)) {
      return createSecureResponse("Invalid storage ID format", { status: 400 });
    }

    try {
      const fileUrl = await ctx.storage.getUrl(storageId);
      return createSecureResponse(fileUrl, {
        headers: { 'Content-Type': 'text/plain' }
      });
    } catch (error) {
      console.error('Storage URL retrieval error:', error);
      return createSecureResponse("Failed to get storage URL", { status: 500 });
    }
  }),
});

// Add a route to download PDF by invoice ID
http.route({
  path: "/invoice/:invoiceId/pdf",
  method: "GET",
  handler: httpAction(async (ctx, request) => {
    const url = new URL(request.url);
    const invoiceId = url.pathname.split('/')[2]; // Extract invoiceId from path

    if (!invoiceId) {
      return createSecureResponse("Invoice ID is required", { status: 400 });
    }

    // Validate invoiceId format to prevent injection
    if (!/^[a-zA-Z0-9_-]+$/.test(invoiceId)) {
      return createSecureResponse("Invalid invoice ID format", { status: 400 });
    }

    try {
      // Get the authenticated user
      const userId = await getAuthUserId(ctx);
      if (!userId) {
        return createSecureResponse("Unauthorized", { status: 401 });
      }

      // Get the invoice to check permissions and get PDF storage ID
      const invoice = await ctx.runQuery(internal.invoices.getInvoiceWithCustomer, {
        invoiceId: invoiceId as any
      });

      if (!invoice) {
        return createSecureResponse("Invoice not found", { status: 404 });
      }

      // Check if user has permission to access this invoice
      if (invoice.createdBy !== userId) {
        return createSecureResponse("Forbidden", { status: 403 });
      }

      // Check if PDF exists
      if (!invoice.pdfStorageId) {
        return createSecureResponse("PDF not found for this invoice", { status: 404 });
      }

      // Validate PDF storage ID
      if (!/^[a-zA-Z0-9_-]+$/.test(invoice.pdfStorageId)) {
        return createSecureResponse("Invalid PDF storage ID", { status: 400 });
      }

      // Get the PDF URL from storage
      const pdfUrl = await ctx.storage.getUrl(invoice.pdfStorageId);
      if (!pdfUrl) {
        return createSecureResponse("PDF file not accessible", { status: 404 });
      }

      // Redirect to the PDF URL with security headers
      return new Response(null, {
        status: 302,
        headers: {
          ...SECURITY_HEADERS,
          Location: pdfUrl,
        },
      });

    } catch (error) {
      console.error("PDF download error:", error);
      return createSecureResponse("Failed to download PDF", { status: 500 });
    }
  }),
});

// Add a route to get PDF URL by invoice ID (returns JSON)
http.route({
  path: "/invoice/:invoiceId/pdf-url",
  method: "GET",
  handler: httpAction(async (ctx, request) => {
    const url = new URL(request.url);
    const invoiceId = url.pathname.split('/')[2]; // Extract invoiceId from path

    if (!invoiceId) {
      return new Response(JSON.stringify({ error: "Invoice ID is required" }), {
        status: 400,
        headers: { "Content-Type": "application/json" }
      });
    }

    try {
      // Get the authenticated user
      const userId = await getAuthUserId(ctx);
      if (!userId) {
        return new Response(JSON.stringify({ error: "Unauthorized" }), {
          status: 401,
          headers: { "Content-Type": "application/json" }
        });
      }

      // Get the invoice to check permissions and get PDF storage ID
      const invoice = await ctx.runQuery(internal.invoices.getInvoiceWithCustomer, {
        invoiceId: invoiceId as any
      });

      if (!invoice) {
        return new Response(JSON.stringify({ error: "Invoice not found" }), {
          status: 404,
          headers: { "Content-Type": "application/json" }
        });
      }

      // Check if user has permission to access this invoice
      if (invoice.createdBy !== userId) {
        return new Response(JSON.stringify({ error: "Forbidden" }), {
          status: 403,
          headers: { "Content-Type": "application/json" }
        });
      }

      // Check if PDF exists
      if (!invoice.pdfStorageId) {
        return new Response(JSON.stringify({
          hasPdf: false,
          message: "PDF not found for this invoice"
        }), {
          status: 200,
          headers: { "Content-Type": "application/json" }
        });
      }

      // Get the PDF URL from storage
      const pdfUrl = await ctx.storage.getUrl(invoice.pdfStorageId);

      return new Response(JSON.stringify({
        hasPdf: true,
        pdfUrl: pdfUrl,
        invoiceNumber: invoice.invoiceNumber
      }), {
        status: 200,
        headers: { "Content-Type": "application/json" }
      });

    } catch (error) {
      console.error("PDF URL retrieval error:", error);
      return new Response(JSON.stringify({ error: "Failed to get PDF URL" }), {
        status: 500,
        headers: { "Content-Type": "application/json" }
      });
    }
  }),
});

export default http;
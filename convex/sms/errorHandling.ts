// SMS error handling utilities
// Comprehensive error handling for SMS functionality

export interface SMSError {
  code: string;
  message: string;
  userMessage: string;
  severity: "low" | "medium" | "high" | "critical";
  retryable: boolean;
  category: "validation" | "authentication" | "network" | "rate_limit" | "service" | "configuration";
}

// Error codes and their details
const ERROR_DEFINITIONS: Record<string, Omit<SMSError, "message">> = {
  // Validation errors
  INVALID_PHONE_NUMBER: {
    code: "INVALID_PHONE_NUMBER",
    userMessage: "The phone number format is invalid. Please check and try again.",
    severity: "medium",
    retryable: true,
    category: "validation",
  },
  EMPTY_MESSAGE: {
    code: "EMPTY_MESSAGE",
    userMessage: "Message content cannot be empty.",
    severity: "medium",
    retryable: true,
    category: "validation",
  },
  MESSAGE_TOO_LONG: {
    code: "MESSAGE_TOO_LONG",
    userMessage: "Message is too long. Please shorten your message and try again.",
    severity: "medium",
    retryable: true,
    category: "validation",
  },
  SUSPICIOUS_CONTENT: {
    code: "SUSPICIOUS_CONTENT",
    userMessage: "Message content contains suspicious patterns. Please review and modify your message.",
    severity: "high",
    retryable: true,
    category: "validation",
  },

  // Authentication errors
  INVALID_CREDENTIALS: {
    code: "INVALID_CREDENTIALS",
    userMessage: "SMS service credentials are invalid. Please contact your administrator.",
    severity: "critical",
    retryable: false,
    category: "authentication",
  },
  UNAUTHORIZED_USER: {
    code: "UNAUTHORIZED_USER",
    userMessage: "You don't have permission to perform this action.",
    severity: "high",
    retryable: false,
    category: "authentication",
  },
  ADMIN_REQUIRED: {
    code: "ADMIN_REQUIRED",
    userMessage: "Administrator privileges are required for this action.",
    severity: "high",
    retryable: false,
    category: "authentication",
  },

  // Network/Service errors
  TWILIO_API_ERROR: {
    code: "TWILIO_API_ERROR",
    userMessage: "SMS service is temporarily unavailable. Please try again later.",
    severity: "high",
    retryable: true,
    category: "service",
  },
  NETWORK_ERROR: {
    code: "NETWORK_ERROR",
    userMessage: "Network connection failed. Please check your connection and try again.",
    severity: "medium",
    retryable: true,
    category: "network",
  },
  SERVICE_UNAVAILABLE: {
    code: "SERVICE_UNAVAILABLE",
    userMessage: "SMS service is currently unavailable. Please try again later.",
    severity: "high",
    retryable: true,
    category: "service",
  },

  // Rate limiting
  RATE_LIMIT_EXCEEDED: {
    code: "RATE_LIMIT_EXCEEDED",
    userMessage: "You've sent too many messages. Please wait before sending more.",
    severity: "medium",
    retryable: true,
    category: "rate_limit",
  },
  DAILY_LIMIT_EXCEEDED: {
    code: "DAILY_LIMIT_EXCEEDED",
    userMessage: "Daily SMS limit reached. Please try again tomorrow.",
    severity: "medium",
    retryable: false,
    category: "rate_limit",
  },

  // Configuration errors
  SMS_NOT_CONFIGURED: {
    code: "SMS_NOT_CONFIGURED",
    userMessage: "SMS service is not configured. Please contact your administrator.",
    severity: "critical",
    retryable: false,
    category: "configuration",
  },
  INVALID_CONFIGURATION: {
    code: "INVALID_CONFIGURATION",
    userMessage: "SMS service configuration is invalid. Please contact your administrator.",
    severity: "critical",
    retryable: false,
    category: "configuration",
  },

  // Customer/Data errors
  CUSTOMER_NOT_FOUND: {
    code: "CUSTOMER_NOT_FOUND",
    userMessage: "Customer not found. Please refresh and try again.",
    severity: "medium",
    retryable: true,
    category: "validation",
  },
  NO_PHONE_NUMBER: {
    code: "NO_PHONE_NUMBER",
    userMessage: "Customer doesn't have a phone number. Please add one before sending SMS.",
    severity: "medium",
    retryable: true,
    category: "validation",
  },

  // Template errors
  TEMPLATE_NOT_FOUND: {
    code: "TEMPLATE_NOT_FOUND",
    userMessage: "SMS template not found. Please select a different template.",
    severity: "medium",
    retryable: true,
    category: "validation",
  },
  TEMPLATE_INACTIVE: {
    code: "TEMPLATE_INACTIVE",
    userMessage: "Selected template is inactive. Please choose an active template.",
    severity: "medium",
    retryable: true,
    category: "validation",
  },
  INVALID_TEMPLATE_VARIABLES: {
    code: "INVALID_TEMPLATE_VARIABLES",
    userMessage: "Template contains invalid variables. Please check the template configuration.",
    severity: "medium",
    retryable: true,
    category: "validation",
  },

  // Generic errors
  UNKNOWN_ERROR: {
    code: "UNKNOWN_ERROR",
    userMessage: "An unexpected error occurred. Please try again or contact support.",
    severity: "medium",
    retryable: true,
    category: "service",
  },
};

// Create a standardized error
export function createSMSError(code: string, originalMessage?: string): SMSError {
  const definition = ERROR_DEFINITIONS[code] || ERROR_DEFINITIONS.UNKNOWN_ERROR;
  
  return {
    ...definition,
    message: originalMessage || definition.userMessage,
  };
}

// Parse Twilio errors into standardized format
export function parseTwilioError(error: any): SMSError {
  const errorCode = error.code;
  const errorMessage = error.message || '';

  // Map Twilio error codes to our standardized errors
  switch (errorCode) {
    case 21211:
      return createSMSError("INVALID_PHONE_NUMBER", "Invalid phone number format.");
    
    case 21408:
      return createSMSError("INVALID_CREDENTIALS", "Permission denied - invalid Twilio credentials.");
    
    case 21610:
      return createSMSError("INVALID_PHONE_NUMBER", "Message cannot be sent to this number.");
    
    case 21614:
      return createSMSError("MESSAGE_TOO_LONG", "Message body is too long.");
    
    case 21617:
      return createSMSError("INVALID_PHONE_NUMBER", "Not a valid mobile number.");
    
    case 21619:
      return createSMSError("INVALID_PHONE_NUMBER", "SMS not supported for this number.");
    
    case 20003:
      return createSMSError("INVALID_CREDENTIALS", "Authentication failed - check Twilio credentials.");
    
    case 20404:
      return createSMSError("INVALID_CONFIGURATION", "Twilio phone number not found.");
    
    case 20429:
      return createSMSError("RATE_LIMIT_EXCEEDED", "Too many requests - rate limit exceeded.");
    
    default:
      // Check error message for common patterns
      if (errorMessage.includes('authenticate') || errorMessage.includes('credential')) {
        return createSMSError("INVALID_CREDENTIALS", errorMessage);
      }
      
      if (errorMessage.includes('phone number') || errorMessage.includes('invalid number')) {
        return createSMSError("INVALID_PHONE_NUMBER", errorMessage);
      }
      
      if (errorMessage.includes('rate limit') || errorMessage.includes('too many')) {
        return createSMSError("RATE_LIMIT_EXCEEDED", errorMessage);
      }
      
      if (errorMessage.includes('network') || errorMessage.includes('connection')) {
        return createSMSError("NETWORK_ERROR", errorMessage);
      }
      
      return createSMSError("TWILIO_API_ERROR", errorMessage);
  }
}

// Handle and log errors
export function handleSMSError(
  error: any,
  context: {
    userId?: string;
    customerId?: string;
    action: string;
    details?: any;
  }
): SMSError {
  let smsError: SMSError;

  // Determine error type and create standardized error
  if (error.code && typeof error.code === 'number') {
    // Twilio error
    smsError = parseTwilioError(error);
  } else if (typeof error === 'string') {
    // String error message
    smsError = createSMSError("UNKNOWN_ERROR", error);
  } else if (error.message) {
    // Error object with message
    smsError = createSMSError("UNKNOWN_ERROR", error.message);
  } else {
    // Unknown error format
    smsError = createSMSError("UNKNOWN_ERROR");
  }

  // Log error for debugging
  console.error("SMS Error:", {
    error: smsError,
    context,
    originalError: error,
    timestamp: new Date().toISOString(),
  });

  return smsError;
}

// Retry logic for retryable errors
export async function retryWithBackoff<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  let lastError: any;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      
      const smsError = handleSMSError(error, { action: "retry_operation" });
      
      // Don't retry if error is not retryable
      if (!smsError.retryable || attempt === maxRetries) {
        throw error;
      }

      // Calculate delay with exponential backoff
      const delay = baseDelay * Math.pow(2, attempt);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError;
}

// Validate error recovery options
export function getErrorRecoveryOptions(error: SMSError): {
  canRetry: boolean;
  suggestedActions: string[];
  escalationRequired: boolean;
} {
  const suggestedActions: string[] = [];
  let escalationRequired = false;

  switch (error.category) {
    case "validation":
      suggestedActions.push("Check and correct the input data");
      if (error.code === "INVALID_PHONE_NUMBER") {
        suggestedActions.push("Verify the phone number format");
        suggestedActions.push("Ensure the number includes country code");
      }
      break;

    case "authentication":
      escalationRequired = true;
      suggestedActions.push("Contact your administrator");
      suggestedActions.push("Verify your account permissions");
      break;

    case "configuration":
      escalationRequired = true;
      suggestedActions.push("Contact your administrator");
      suggestedActions.push("Check SMS service configuration");
      break;

    case "rate_limit":
      suggestedActions.push("Wait before sending more messages");
      suggestedActions.push("Consider spreading messages over time");
      break;

    case "network":
    case "service":
      suggestedActions.push("Check your internet connection");
      suggestedActions.push("Try again in a few minutes");
      if (error.severity === "critical") {
        escalationRequired = true;
        suggestedActions.push("Contact support if problem persists");
      }
      break;
  }

  return {
    canRetry: error.retryable,
    suggestedActions,
    escalationRequired,
  };
}

// Format error for user display
export function formatErrorForUser(error: SMSError): {
  title: string;
  message: string;
  actions: string[];
  severity: string;
} {
  const recovery = getErrorRecoveryOptions(error);
  
  return {
    title: getErrorTitle(error),
    message: error.userMessage,
    actions: recovery.suggestedActions,
    severity: error.severity,
  };
}

// Get user-friendly error title
function getErrorTitle(error: SMSError): string {
  switch (error.category) {
    case "validation":
      return "Input Error";
    case "authentication":
      return "Permission Error";
    case "configuration":
      return "Configuration Error";
    case "rate_limit":
      return "Rate Limit Exceeded";
    case "network":
      return "Connection Error";
    case "service":
      return "Service Error";
    default:
      return "Error";
  }
}

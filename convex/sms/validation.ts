// SMS validation utilities
// Validation functions for SMS functionality

export interface ValidationResult {
  isValid: boolean;
  error?: string;
}

// Validate customer data for SMS sending
export function validateCustomerForSMS(customer: any): ValidationResult {
  if (!customer) {
    return { isValid: false, error: "Customer not found." };
  }

  if (!customer.phone) {
    return { isValid: false, error: "Customer phone number is required for SMS." };
  }

  // Basic phone number validation
  const phoneValidation = validatePhoneNumberFormat(customer.phone);
  if (!phoneValidation.isValid) {
    return phoneValidation;
  }

  return { isValid: true };
}

// Validate phone number format
export function validatePhoneNumberFormat(phoneNumber: string): ValidationResult {
  if (!phoneNumber || phoneNumber.trim().length === 0) {
    return { isValid: false, error: "Phone number is required." };
  }

  // Remove all non-digit characters for validation
  const cleaned = phoneNumber.replace(/\D/g, '');
  
  // Check minimum and maximum length
  if (cleaned.length < 7) {
    return { isValid: false, error: "Phone number is too short." };
  }
  
  if (cleaned.length > 15) {
    return { isValid: false, error: "Phone number is too long." };
  }

  // Check for valid US phone number (10 digits)
  if (cleaned.length === 10) {
    // US phone number validation
    const areaCode = cleaned.substring(0, 3);
    const exchange = cleaned.substring(3, 6);
    
    // Area code cannot start with 0 or 1
    if (areaCode.startsWith('0') || areaCode.startsWith('1')) {
      return { isValid: false, error: "Invalid US phone number format." };
    }
    
    // Exchange cannot start with 0 or 1
    if (exchange.startsWith('0') || exchange.startsWith('1')) {
      return { isValid: false, error: "Invalid US phone number format." };
    }
  }

  return { isValid: true };
}

// Validate Twilio configuration
export function validateTwilioConfig(): ValidationResult {
  const accountSid = process.env.TWILIO_ACCOUNT_SID;
  const authToken = process.env.TWILIO_AUTH_TOKEN;
  const phoneNumber = process.env.TWILIO_PHONE_NUMBER;
  
  if (!accountSid) {
    return { 
      isValid: false, 
      error: "SMS service not configured. Please set TWILIO_ACCOUNT_SID environment variable." 
    };
  }

  if (!authToken) {
    return { 
      isValid: false, 
      error: "SMS service not configured. Please set TWILIO_AUTH_TOKEN environment variable." 
    };
  }

  if (!phoneNumber) {
    return { 
      isValid: false, 
      error: "SMS service not configured. Please set TWILIO_PHONE_NUMBER environment variable." 
    };
  }

  // Validate Account SID format
  if (!accountSid.startsWith('AC') || accountSid.length !== 34) {
    return { 
      isValid: false, 
      error: "Invalid Twilio Account SID format. Should start with 'AC' and be 34 characters long." 
    };
  }

  // Validate Auth Token format (32 characters)
  if (authToken.length !== 32) {
    return { 
      isValid: false, 
      error: "Invalid Twilio Auth Token format. Should be 32 characters long." 
    };
  }

  // Validate phone number format
  const phoneValidation = validatePhoneNumberFormat(phoneNumber);
  if (!phoneValidation.isValid) {
    return { 
      isValid: false, 
      error: `Invalid Twilio phone number: ${phoneValidation.error}` 
    };
  }

  return { isValid: true };
}

// Validate SMS template
export function validateSMSTemplate(template: any): ValidationResult {
  if (!template.name || template.name.trim().length === 0) {
    return { isValid: false, error: "Template name is required." };
  }

  if (!template.content || template.content.trim().length === 0) {
    return { isValid: false, error: "Template content is required." };
  }

  if (template.content.length > 1600) {
    return { 
      isValid: false, 
      error: "Template content is too long. SMS templates should be under 1600 characters." 
    };
  }

  if (!template.category || template.category.trim().length === 0) {
    return { isValid: false, error: "Template category is required." };
  }

  // Validate category
  const validCategories = ['appointment', 'service', 'invoice', 'general'];
  if (!validCategories.includes(template.category)) {
    return { 
      isValid: false, 
      error: `Invalid template category. Must be one of: ${validCategories.join(', ')}` 
    };
  }

  return { isValid: true };
}

// Validate SMS message before sending
export function validateSMSMessage(message: any): ValidationResult {
  if (!message.to || message.to.trim().length === 0) {
    return { isValid: false, error: "Recipient phone number is required." };
  }

  if (!message.body || message.body.trim().length === 0) {
    return { isValid: false, error: "Message content is required." };
  }

  // Validate phone number
  const phoneValidation = validatePhoneNumberFormat(message.to);
  if (!phoneValidation.isValid) {
    return { 
      isValid: false, 
      error: `Invalid recipient phone number: ${phoneValidation.error}` 
    };
  }

  // Validate message length
  if (message.body.length > 1600) {
    return { 
      isValid: false, 
      error: "Message is too long. SMS messages should be under 1600 characters." 
    };
  }

  return { isValid: true };
}

// Validate job data for SMS notifications
export function validateJobForSMS(job: any): ValidationResult {
  if (!job) {
    return { isValid: false, error: "Job not found." };
  }

  if (!job.customerId) {
    return { isValid: false, error: "Job must be associated with a customer." };
  }

  return { isValid: true };
}

// Validate invoice data for SMS notifications
export function validateInvoiceForSMS(invoice: any): ValidationResult {
  if (!invoice) {
    return { isValid: false, error: "Invoice not found." };
  }

  if (!invoice.customerId) {
    return { isValid: false, error: "Invoice must be associated with a customer." };
  }

  return { isValid: true };
}

// Validate user permissions for SMS operations
export function validateSMSPermissions(user: any, operation: string): ValidationResult {
  if (!user) {
    return { isValid: false, error: "User not authenticated." };
  }

  // Admin-only operations
  const adminOnlyOperations = ['configure_twilio', 'manage_templates', 'view_all_history'];
  
  if (adminOnlyOperations.includes(operation) && user.role !== 'admin' && user.role !== 'master') {
    return {
      isValid: false,
      error: "Unauthorized - Admin access required for this operation."
    };
  }

  return { isValid: true };
}

// Validate template variables
export function validateTemplateVariables(
  template: string, 
  availableVariables: string[]
): ValidationResult {
  // Extract variables from template (format: {variableName})
  const variableRegex = /\{([^}]+)\}/g;
  const usedVariables: string[] = [];
  let match;

  while ((match = variableRegex.exec(template)) !== null) {
    usedVariables.push(match[1]);
  }

  // Check if all used variables are available
  const invalidVariables = usedVariables.filter(
    variable => !availableVariables.includes(variable)
  );

  if (invalidVariables.length > 0) {
    return {
      isValid: false,
      error: `Invalid template variables: ${invalidVariables.join(', ')}. Available variables: ${availableVariables.join(', ')}`
    };
  }

  return { isValid: true };
}

// Validate Twilio credentials format (without making API calls)
export function validateTwilioCredentialsFormat(
  accountSid: string,
  authToken: string,
  phoneNumber: string
): ValidationResult {
  // Validate Account SID format
  if (!accountSid || !accountSid.startsWith('AC') || accountSid.length !== 34) {
    return {
      isValid: false,
      error: "Invalid Twilio Account SID format. Should start with 'AC' and be 34 characters long.",
    };
  }

  // Validate Auth Token format
  if (!authToken || authToken.length !== 32) {
    return {
      isValid: false,
      error: "Invalid Twilio Auth Token format. Should be 32 characters long.",
    };
  }

  // Validate phone number format
  const phoneValidation = validatePhoneNumberFormat(phoneNumber);
  if (!phoneValidation.isValid) {
    return phoneValidation;
  }

  return { isValid: true };
}

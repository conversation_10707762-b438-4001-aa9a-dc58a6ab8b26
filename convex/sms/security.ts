// SMS security utilities
// Security functions for SMS functionality

export interface SecurityValidationResult {
  isValid: boolean;
  error?: string;
  riskLevel?: "low" | "medium" | "high";
}

// Rate limiting configuration
const RATE_LIMITS = {
  SMS_PER_MINUTE: 10,
  SMS_PER_HOUR: 100,
  SMS_PER_DAY: 500,
  TEMPLATE_CHANGES_PER_HOUR: 20,
  CONFIG_CHANGES_PER_HOUR: 5,
};

// Validate SMS content for security risks
export function validateSMSContentSecurity(content: string): SecurityValidationResult {
  const suspiciousPatterns = [
    // URLs and links
    /https?:\/\/[^\s]+/gi,
    /www\.[^\s]+/gi,
    /[^\s]+\.(com|org|net|edu|gov|mil|int|co|io|ly|me|tv|cc|tk|ml|ga|cf)[^\s]*/gi,
    
    // Phone numbers (potential spam)
    /\b\d{3}[-.]?\d{3}[-.]?\d{4}\b/g,
    /\b\+?1?[-.\s]?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}\b/g,
    
    // Financial terms (potential fraud)
    /\b(credit|debit|bank|account|ssn|social security|routing|wire transfer|bitcoin|crypto)\b/gi,
    
    // Urgency/pressure tactics
    /\b(urgent|emergency|immediate|act now|limited time|expires|deadline)\b/gi,
    
    // Suspicious requests
    /\b(click here|download|install|verify|confirm|update|suspend|locked|frozen)\b/gi,
  ];

  let riskLevel: "low" | "medium" | "high" = "low";
  const foundPatterns: string[] = [];

  for (const pattern of suspiciousPatterns) {
    const matches = content.match(pattern);
    if (matches) {
      foundPatterns.push(...matches);
    }
  }

  // Determine risk level based on patterns found
  if (foundPatterns.length === 0) {
    riskLevel = "low";
  } else if (foundPatterns.length <= 2) {
    riskLevel = "medium";
  } else {
    riskLevel = "high";
  }

  // High-risk patterns that should be blocked
  const highRiskPatterns = [
    /\b(password|pin|code|verification code|otp)\b/gi,
    /\b(click.*link|tap.*link|visit.*link)\b/gi,
    /\b(free money|cash prize|lottery|winner|congratulations.*won)\b/gi,
  ];

  for (const pattern of highRiskPatterns) {
    if (pattern.test(content)) {
      return {
        isValid: false,
        error: "Message content contains high-risk patterns that are not allowed.",
        riskLevel: "high",
      };
    }
  }

  // Warn about medium/high risk but allow
  if (riskLevel === "high") {
    return {
      isValid: false,
      error: "Message content contains multiple suspicious patterns. Please review before sending.",
      riskLevel,
    };
  }

  return {
    isValid: true,
    riskLevel,
  };
}

// Validate phone number for security (prevent SMS bombing)
export function validatePhoneNumberSecurity(phoneNumber: string): SecurityValidationResult {
  // Remove formatting
  const cleaned = phoneNumber.replace(/\D/g, '');
  
  // Check for suspicious patterns
  const suspiciousPatterns = [
    // Repeated digits
    /^(\d)\1{6,}$/,
    // Sequential numbers
    /^(0123456789|1234567890|9876543210)$/,
    // Common test numbers
    /^(1234567890|0000000000|1111111111|5555555555)$/,
    // Premium rate numbers (US)
    /^1?900\d{7}$/,
    /^1?976\d{7}$/,
  ];

  for (const pattern of suspiciousPatterns) {
    if (pattern.test(cleaned)) {
      return {
        isValid: false,
        error: "Phone number appears to be invalid or suspicious.",
        riskLevel: "high",
      };
    }
  }

  return { isValid: true, riskLevel: "low" };
}

// Import secure encryption functions
import {
  encryptCredentialLegacy,
  decryptCredentialLegacy,
  validateEncryptionConfig
} from "../security/encryption";

// Encrypt sensitive data using AES-256-GCM encryption
export async function encryptCredential(credential: string): Promise<string> {
  try {
    // Validate encryption configuration before use
    const configValidation = await validateEncryptionConfig();
    if (!configValidation.isValid) {
      console.error("Encryption configuration invalid:", configValidation.error);
      throw new Error("Encryption service not properly configured");
    }

    return await encryptCredentialLegacy(credential);
  } catch (error) {
    console.error("Failed to encrypt credential:", error);
    throw new Error("Failed to encrypt credential");
  }
}

// Decrypt sensitive data using AES-256-GCM encryption
export async function decryptCredential(encryptedCredential: string): Promise<string> {
  try {
    return await decryptCredentialLegacy(encryptedCredential);
  } catch (error) {
    console.error("Failed to decrypt credential:", error);
    throw new Error("Failed to decrypt credential");
  }
}

// Import active rate limiting functions
import {
  checkRateLimit,
  recordRateLimitedAction,
  RATE_LIMIT_CONFIGS
} from "../security/rateLimiting";

// Validate rate limits using active rate limiting system
export async function validateRateLimit(
  ctx: any,
  userId: string,
  action: string,
  timeWindow?: "minute" | "hour" | "day"
): Promise<SecurityValidationResult> {
  try {
    // Map timeWindow to specific action if provided
    let rateLimitAction = action;
    if (action === "sms_send" && timeWindow) {
      switch (timeWindow) {
        case "minute":
          rateLimitAction = "sms_send";
          break;
        case "hour":
          rateLimitAction = "sms_send_hourly";
          break;
        case "day":
          rateLimitAction = "sms_send_daily";
          break;
      }
    }

    const result = await checkRateLimit(ctx, userId, rateLimitAction);

    if (!result.allowed) {
      return {
        isValid: false,
        error: result.error || "Rate limit exceeded",
        riskLevel: "high",
      };
    }

    return {
      isValid: true,
      riskLevel: result.remaining < 5 ? "medium" : "low"
    };
  } catch (error) {
    console.error("Rate limit validation error:", error);
    return {
      isValid: false,
      error: "Rate limit validation failed",
      riskLevel: "high",
    };
  }
}

// Record a rate limited action
export async function recordRateLimitAction(
  ctx: any,
  userId: string,
  action: string,
  timeWindow?: "minute" | "hour" | "day"
): Promise<void> {
  try {
    // Record for all applicable time windows for SMS
    if (action === "sms_send") {
      await recordRateLimitedAction(ctx, userId, "sms_send");
      await recordRateLimitedAction(ctx, userId, "sms_send_hourly");
      await recordRateLimitedAction(ctx, userId, "sms_send_daily");
    } else {
      await recordRateLimitedAction(ctx, userId, action);
    }
  } catch (error) {
    console.error("Failed to record rate limit action:", error);
    // Don't throw error to avoid breaking the main operation
  }
}

// Sanitize user input
export function sanitizeInput(input: string): string {
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: URLs
    .replace(/data:/gi, '') // Remove data: URLs
    .substring(0, 2000); // Limit length
}

// Validate admin permissions with additional security checks
export function validateAdminSecurity(user: any, action: string): SecurityValidationResult {
  if (!user) {
    return { isValid: false, error: "User not authenticated.", riskLevel: "high" };
  }

  if (user.role !== "admin" && user.role !== "master") {
    return {
      isValid: false,
      error: "Unauthorized - Admin access required.",
      riskLevel: "high"
    };
  }

  // Additional security checks for sensitive actions
  const sensitiveActions = [
    "configure_twilio",
    "view_all_history",
    "export_data",
    "manage_templates",
  ];

  if (sensitiveActions.includes(action)) {
    // In production, you might want additional verification
    // like 2FA, recent password confirmation, etc.
    
    // Check if user account is in good standing
    if (user.isLocked || user.isSuspended) {
      return {
        isValid: false,
        error: "Account is locked or suspended.",
        riskLevel: "high",
      };
    }
  }

  return { isValid: true, riskLevel: "low" };
}

// Audit log entry structure
export interface AuditLogEntry {
  userId: string;
  action: string;
  resource: string;
  resourceId?: string;
  details: any;
  ipAddress?: string;
  userAgent?: string;
  timestamp: number;
  success: boolean;
  error?: string;
}

// Create audit log entry
export function createAuditLogEntry(
  userId: string,
  action: string,
  resource: string,
  details: any,
  success: boolean,
  error?: string,
  resourceId?: string
): AuditLogEntry {
  return {
    userId,
    action,
    resource,
    resourceId,
    details,
    timestamp: Date.now(),
    success,
    error,
  };
}

// Validate Twilio credentials format (without making API calls)
export function validateTwilioCredentialsFormat(
  accountSid: string,
  authToken: string,
  phoneNumber: string
): SecurityValidationResult {
  // Validate Account SID format
  if (!accountSid || !accountSid.startsWith('AC') || accountSid.length !== 34) {
    return {
      isValid: false,
      error: "Invalid Twilio Account SID format. Should start with 'AC' and be 34 characters long.",
      riskLevel: "medium",
    };
  }

  // Validate Auth Token format
  if (!authToken || authToken.length !== 32) {
    return {
      isValid: false,
      error: "Invalid Twilio Auth Token format. Should be 32 characters long.",
      riskLevel: "medium",
    };
  }

  // Validate phone number format
  const phoneValidation = validatePhoneNumberSecurity(phoneNumber);
  if (!phoneValidation.isValid) {
    return phoneValidation;
  }

  // Check for obviously fake credentials
  if (accountSid.includes('test') || authToken.includes('test')) {
    return {
      isValid: true,
      riskLevel: "low", // Test credentials are okay
    };
  }

  return { isValid: true, riskLevel: "low" };
}

// Generate secure random string for testing
export function generateSecureRandomString(length: number): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// SMS service utilities
// Core SMS functionality using Twilio through Convex component

import { validateSMSContentSecurity, validatePhoneNumberSecurity } from "./security";
import { handleSMSError, createSMSError } from "./errorHandling";

export interface TwilioConfig {
  accountSid: string;
  authToken: string;
  phoneNumber: string;
  testMode?: boolean;
}

export interface SMSResult {
  success: boolean;
  messageSid?: string;
  error?: string;
  status?: string;
}

export interface SMSMessage {
  to: string;
  body: string;
  from?: string;
}

// Twilio client interface
export interface TwilioClient {
  messages: {
    create: (params: {
      body: string;
      from: string;
      to: string;
    }) => Promise<{
      sid: string;
      status: string;
    }>;
  };
}

// Initialize Twilio client with configuration
export function createTwilioClient(config: TwilioConfig): TwilioClient {
  if (!config.accountSid || !config.authToken) {
    throw new Error("Invalid Twilio configuration");
  }

  // This would use the actual Twilio SDK in production
  // For now, we'll throw an error to indicate SMS is not available
  throw new Error("SMS service requires Twilio SDK integration. Please configure Twilio properly.");
}

// Get Twilio configuration from environment and settings
export function getTwilioConfig(settings: any): TwilioConfig {
  // In production, these should come from the twilioSettings table
  // For now, we'll use environment variables as fallback
  const accountSid = process.env.TWILIO_ACCOUNT_SID || settings?.accountSid;
  const authToken = process.env.TWILIO_AUTH_TOKEN || settings?.authToken;
  const phoneNumber = process.env.TWILIO_PHONE_NUMBER || settings?.phoneNumber;
  
  return {
    accountSid: accountSid!,
    authToken: authToken!,
    phoneNumber: phoneNumber!,
    testMode: settings?.testMode || false
  };
}

// Send SMS message using Twilio with enhanced security and error handling
export async function sendSMS(
  _client: TwilioClient,
  message: SMSMessage,
  _fromNumber: string
): Promise<SMSResult> {
  try {
    // Security validation
    const contentValidation = validateSMSContentSecurity(message.body);
    if (!contentValidation.isValid) {
      const error = createSMSError("SUSPICIOUS_CONTENT", contentValidation.error);
      return {
        success: false,
        error: error.userMessage,
      };
    }

    const phoneValidation = validatePhoneNumberSecurity(message.to);
    if (!phoneValidation.isValid) {
      const error = createSMSError("INVALID_PHONE_NUMBER", phoneValidation.error);
      return {
        success: false,
        error: error.userMessage,
      };
    }

    // For now, return an error indicating SMS is not available
    // In production, this would send the actual SMS
    return {
      success: false,
      error: "SMS service is not available. Please configure Twilio integration.",
    };
  } catch (error: any) {
    const smsError = handleSMSError(error, {
      action: "send_sms",
      details: { to: message.to, bodyLength: message.body.length },
    });

    return {
      success: false,
      error: smsError.userMessage,
    };
  }
}

// Parse Twilio API errors into user-friendly messages
export function parseTwilioError(error: any): string {
  const errorMessage = error.message || '';
  const errorCode = error.code;
  
  // Common Twilio error codes
  switch (errorCode) {
    case 21211:
      return "Invalid phone number format. Please check the recipient's phone number.";
    case 21408:
      return "Permission denied. Please verify your Twilio account permissions.";
    case 21610:
      return "Message cannot be sent to this number. The number may be blocked or invalid.";
    case 21614:
      return "Message body is invalid or too long. Please shorten your message.";
    case 21617:
      return "The phone number is not a valid mobile number.";
    case 21619:
      return "SMS is not supported for this phone number.";
    case 20003:
      return "Authentication failed. Please check your Twilio credentials.";
    case 20404:
      return "Twilio phone number not found. Please verify your Twilio phone number.";
    default:
      if (errorMessage.includes('authenticate')) {
        return "Invalid Twilio credentials. Please check your Account SID and Auth Token.";
      }
      if (errorMessage.includes('phone number')) {
        return "Invalid phone number. Please check the recipient's phone number format.";
      }
      if (errorMessage.includes('message body')) {
        return "Message content is invalid. Please check your message format.";
      }
      return `SMS delivery failed: ${errorMessage || 'Unknown error'}. Please try again or contact support.`;
  }
}

// Validate phone number format
export function validatePhoneNumber(phoneNumber: string): { isValid: boolean; error?: string } {
  // Remove all non-digit characters
  const cleaned = phoneNumber.replace(/\D/g, '');
  
  // Check if it's a valid US phone number (10 digits) or international (7-15 digits)
  if (cleaned.length === 10) {
    // US phone number
    return { isValid: true };
  } else if (cleaned.length >= 7 && cleaned.length <= 15) {
    // International phone number
    return { isValid: true };
  } else {
    return { 
      isValid: false, 
      error: "Phone number must be 10 digits for US numbers or 7-15 digits for international numbers." 
    };
  }
}

// Format phone number for Twilio (E.164 format)
export function formatPhoneNumber(phoneNumber: string, countryCode: string = '+1'): string {
  const cleaned = phoneNumber.replace(/\D/g, '');
  
  if (cleaned.length === 10 && countryCode === '+1') {
    // US phone number
    return `+1${cleaned}`;
  } else if (cleaned.startsWith('1') && cleaned.length === 11) {
    // US phone number with country code
    return `+${cleaned}`;
  } else if (cleaned.length >= 7 && cleaned.length <= 15) {
    // International number - assume it already has country code if > 10 digits
    if (cleaned.length > 10) {
      return `+${cleaned}`;
    } else {
      return `${countryCode}${cleaned}`;
    }
  }
  
  return phoneNumber; // Return as-is if we can't format it
}

// Replace template variables in SMS content
export function replaceTemplateVariables(
  template: string, 
  variables: Record<string, string>
): string {
  let result = template;
  
  Object.entries(variables).forEach(([key, value]) => {
    const placeholder = `{${key}}`;
    result = result.replace(new RegExp(placeholder, 'g'), value || '');
  });
  
  return result;
}

// Validate SMS message content
export function validateSMSContent(message: string): { isValid: boolean; error?: string } {
  if (!message || message.trim().length === 0) {
    return { isValid: false, error: "Message content cannot be empty." };
  }
  
  if (message.length > 1600) {
    return { 
      isValid: false, 
      error: "Message is too long. SMS messages should be under 1600 characters." 
    };
  }
  
  return { isValid: true };
}

// Log SMS operation for debugging and audit
export function logSMSOperation(
  operation: string,
  details: any,
  success: boolean,
  error?: string
): void {
  const logEntry = {
    timestamp: new Date().toISOString(),
    operation,
    success,
    details: success ? details : { error, ...details },
  };
  
  console.log(`SMS ${operation}:`, logEntry);
}

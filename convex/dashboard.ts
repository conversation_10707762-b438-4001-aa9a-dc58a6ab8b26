import { getAuthUserId } from "@convex-dev/auth/server";
import { query } from "./_generated/server";
import { checkStaffOrAdminPermission } from "./auth";

// Get current user helper
async function getCurrentUser(ctx: any) {
  const userId = await getAuthUserId(ctx);
  if (!userId) throw new Error("Not authenticated");
  return userId;
}

// Get comprehensive dashboard statistics
export const getStats = query({
  args: {},
  handler: async (ctx) => {
    // Check if user has staff or admin permissions
    await checkStaffOrAdminPermission(ctx);

    // Staff and admin users can see all data
    const customers = await ctx.db
      .query("customers")
      .order("desc")
      .collect();

    const jobs = await ctx.db
      .query("jobs")
      .order("desc")
      .collect();

    const invoices = await ctx.db
      .query("invoices")
      .order("desc")
      .collect();

    const products = await ctx.db
      .query("products")
      .filter((q) => q.eq(q.field("isActive"), true))
      .order("desc")
      .collect();

    // Calculate job statistics
    const jobsByStatus = {
      scheduled: jobs.filter(j => j.status === "scheduled").length,
      inProgress: jobs.filter(j => j.status === "in-progress").length,
      completed: jobs.filter(j => j.status === "completed").length,
      cancelled: jobs.filter(j => j.status === "cancelled").length,
    };

    // Calculate invoice statistics
    const totalRevenue = invoices
      .filter(inv => inv.status === "paid")
      .reduce((sum, inv) => sum + inv.total, 0);
    
    const pendingRevenue = invoices
      .filter(inv => inv.status === "sent")
      .reduce((sum, inv) => sum + inv.total, 0);
    
    const overdueInvoices = invoices.filter(inv => 
      inv.status === "sent" && inv.dueDate < Date.now()
    );

    // Recent activity (last 30 days)
    const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
    const recentJobs = jobs.filter(j => j._creationTime > thirtyDaysAgo);
    const recentInvoices = invoices.filter(i => i._creationTime > thirtyDaysAgo);

    return {
      // Overview counts
      totalCustomers: customers.length,
      totalJobs: jobs.length,
      totalInvoices: invoices.length,
      totalProducts: products.length,
      activeJobs: jobsByStatus.scheduled + jobsByStatus.inProgress,
      
      // Job statistics
      jobsByStatus,
      
      // Financial statistics
      totalRevenue,
      pendingRevenue,
      overdueInvoices: overdueInvoices.length,
      overdueAmount: overdueInvoices.reduce((sum, inv) => sum + inv.total, 0),
      
      // Recent activity
      recentJobsCount: recentJobs.length,
      recentInvoicesCount: recentInvoices.length,
      
      // Monthly revenue trend (simplified)
      monthlyRevenue: calculateMonthlyRevenue(invoices),
    };
  },
});

// Helper function to calculate monthly revenue
function calculateMonthlyRevenue(invoices: any[]) {
  const monthlyData: { [key: string]: number } = {};
  
  invoices
    .filter(inv => inv.status === "paid" && inv.paidDate)
    .forEach(inv => {
      const date = new Date(inv.paidDate);
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      monthlyData[monthKey] = (monthlyData[monthKey] || 0) + inv.total;
    });
  
  // Return last 6 months
  const result = [];
  const now = new Date();
  for (let i = 5; i >= 0; i--) {
    const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
    const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
    result.push({
      month: date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
      revenue: monthlyData[monthKey] || 0,
    });
  }
  
  return result;
}

// Get recent activity feed
export const getRecentActivity = query({
  args: {},
  handler: async (ctx) => {
    // Check if user has staff or admin permissions
    await checkStaffOrAdminPermission(ctx);

    // Staff and admin users can see all recent activity
    const recentJobs = await ctx.db
      .query("jobs")
      .order("desc")
      .take(5);

    // Get recent invoices with customer info
    const recentInvoices = await ctx.db
      .query("invoices")
      .order("desc")
      .take(5);

    // Combine and sort by creation time
    const activities = [];
    
    for (const job of recentJobs) {
      const customer = await ctx.db.get(job.customerId);
      activities.push({
        type: "job",
        id: job._id,
        title: job.title,
        customerName: customer?.name || "Unknown",
        status: job.status,
        createdAt: job._creationTime,
      });
    }
    
    for (const invoice of recentInvoices) {
      const customer = await ctx.db.get(invoice.customerId);
      activities.push({
        type: "invoice",
        id: invoice._id,
        title: `Invoice ${invoice.invoiceNumber}`,
        customerName: customer?.name || "Unknown",
        status: invoice.status,
        amount: invoice.total,
        createdAt: invoice._creationTime,
      });
    }
    
    return activities
      .sort((a, b) => b.createdAt - a.createdAt)
      .slice(0, 10);
  },
});

import { v } from "convex/values";
import { query, mutation } from "./_generated/server";
import {
  getCurrentUserWithRole,
  checkAdminPermission,
  checkStaffOrAdminPermission,
  canDeleteRecord
} from "./auth";

// List all active products (staff and admin can see all products)
export const list = query({
  args: {},
  handler: async (ctx) => {
    // Check if user has staff or admin permissions
    await checkStaffOrAdminPermission(ctx);

    // Staff and admin users can see all active products
    return await ctx.db
      .query("products")
      .filter((q) => q.eq(q.field("isActive"), true))
      .order("desc")
      .collect();
  },
});

// Get products by category (staff and admin can see all products)
export const getByCategory = query({
  args: { category: v.string() },
  handler: async (ctx, args) => {
    // Check if user has staff or admin permissions
    await checkStaffOrAdminPermission(ctx);

    // Staff and admin users can see all active products by category
    const products = await ctx.db
      .query("products")
      .withIndex("by_category", (q) => q.eq("category", args.category))
      .filter((q) => q.eq(q.field("isActive"), true))
      .collect();

    return products;
  },
});

// Get a single product by ID (staff and admin can access any product)
export const get = query({
  args: { id: v.id("products") },
  handler: async (ctx, args) => {
    // Check if user has staff or admin permissions
    await checkStaffOrAdminPermission(ctx);

    const product = await ctx.db.get(args.id);
    if (!product) {
      throw new Error("Product not found");
    }
    return product;
  },
});

// Create a new product
export const create = mutation({
  args: {
    name: v.string(),
    description: v.string(),
    price: v.number(),
    category: v.string(),
    sku: v.optional(v.string()),
    isActive: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    // Check if user has staff or admin permissions
    const { user } = await checkStaffOrAdminPermission(ctx);

    return await ctx.db.insert("products", {
      ...args,
      isActive: true,
      createdBy: user._id,
    });
  },
});

// Update an existing product
export const update = mutation({
  args: {
    id: v.id("products"),
    name: v.string(),
    description: v.string(),
    price: v.number(),
    category: v.string(),
    sku: v.optional(v.string()),
    isActive: v.boolean(),
  },
  handler: async (ctx, args) => {
    // Check if user has staff or admin permissions
    await checkStaffOrAdminPermission(ctx);

    const { id, ...updates } = args;

    const product = await ctx.db.get(id);
    if (!product) {
      throw new Error("Product not found");
    }

    await ctx.db.patch(id, updates);
  },
});

// Delete a product (admin only - soft delete by setting isActive to false)
export const remove = mutation({
  args: { id: v.id("products") },
  handler: async (ctx, args) => {
    // Check if user has admin permissions (only admins can delete)
    await checkAdminPermission(ctx);

    const product = await ctx.db.get(args.id);
    if (!product) {
      throw new Error("Product not found");
    }
    await ctx.db.patch(args.id, { isActive: false });
  },
});

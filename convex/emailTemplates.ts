import { v } from "convex/values";
import { mutation, query, internalQuery } from "./_generated/server";
import { getCurrentUser, checkAdminPermission } from "./auth";

// Available variables for email templates
const AVAILABLE_VARIABLES = {
  invoice: [
    "{customerName}",
    "{customerEmail}",
    "{customerPhone}",
    "{customerAddress}",
    "{customerCity}",
    "{customerState}",
    "{customerZipCode}",
    "{customerCompany}",
    "{invoiceNumber}",
    "{invoiceDate}",
    "{dueDate}",
    "{subtotal}",
    "{taxAmount}",
    "{total}",
    "{companyName}",
    "{companyEmail}",
    "{companyPhone}",
    "{companyAddress}",
    "{paymentTerms}",
    "{invoiceUrl}",
    "{notes}",
    // PDF-specific variables
    "{pdfDownloadUrl}",
    "{hasAttachment}",
    "{downloadInstructions}",
    "{pdfDownloadButton}",
    "{attachmentInfo}"
  ],
  notification: [
    "{customerName}",
    "{customerEmail}",
    "{companyName}",
    "{companyEmail}",
    "{companyPhone}",
    "{message}",
    "{date}"
  ],
  reminder: [
    "{customerName}",
    "{customerEmail}",
    "{invoiceNumber}",
    "{dueDate}",
    "{total}",
    "{companyName}",
    "{companyEmail}",
    "{companyPhone}",
    "{daysPastDue}"
  ],
  general: [
    "{customerName}",
    "{customerEmail}",
    "{companyName}",
    "{companyEmail}",
    "{companyPhone}",
    "{date}"
  ]
};

// Template validation
function validateEmailTemplate(template: any) {
  if (!template.name || template.name.trim().length === 0) {
    return { isValid: false, error: "Template name is required" };
  }
  
  if (!template.subject || template.subject.trim().length === 0) {
    return { isValid: false, error: "Template subject is required" };
  }
  
  if (!template.content || template.content.trim().length === 0) {
    return { isValid: false, error: "Template content is required" };
  }
  
  if (!template.category || !Object.keys(AVAILABLE_VARIABLES).includes(template.category)) {
    return { isValid: false, error: "Valid template category is required" };
  }
  
  return { isValid: true };
}

// Validate template variables
function validateTemplateVariables(content: string, subject: string, availableVariables: string[]) {
  const allText = content + " " + subject;
  const variablePattern = /\{([^}]+)\}/g;
  const usedVariables: string[] = [];
  let match;

  while ((match = variablePattern.exec(allText)) !== null) {
    const variable = `{${match[1]}}`;
    if (!availableVariables.includes(variable)) {
      return {
        isValid: false,
        error: `Invalid variable "${variable}". Available variables: ${availableVariables.join(", ")}`
      };
    }
    if (!usedVariables.includes(variable)) {
      usedVariables.push(variable);
    }
  }

  return { isValid: true, usedVariables };
}

// Get available variables for a category
export const getAvailableVariables = query({
  args: { category: v.optional(v.string()) },
  handler: async (ctx, args) => {
    const userId = await getCurrentUser(ctx);
    await checkAdminPermission(ctx, userId);
    
    if (args.category && AVAILABLE_VARIABLES[args.category as keyof typeof AVAILABLE_VARIABLES]) {
      return {
        category: args.category,
        variables: AVAILABLE_VARIABLES[args.category as keyof typeof AVAILABLE_VARIABLES]
      };
    }
    
    return {
      all: AVAILABLE_VARIABLES
    };
  },
});

// List email templates
export const list = query({
  args: {
    category: v.optional(v.string()),
    activeOnly: v.optional(v.boolean())
  },
  handler: async (ctx, args) => {
    const userId = await getCurrentUser(ctx);
    await checkAdminPermission(ctx, userId);

    let templates;

    if (args.category) {
      templates = await ctx.db
        .query("emailTemplates")
        .withIndex("by_category", (q) => q.eq("category", args.category as string))
        .collect();
    } else {
      templates = await ctx.db.query("emailTemplates").collect();
    }

    if (args.activeOnly) {
      return templates.filter(template => template.isActive);
    }

    return templates.sort((a, b) => b.updatedAt - a.updatedAt);
  },
});

// Get a specific email template
export const get = query({
  args: { id: v.id("emailTemplates") },
  handler: async (ctx, args) => {
    const userId = await getCurrentUser(ctx);
    await checkAdminPermission(ctx, userId);
    
    return await ctx.db.get(args.id);
  },
});

// Create email template
export const create = mutation({
  args: {
    name: v.string(),
    subject: v.string(),
    content: v.string(),
    category: v.string(),
    description: v.optional(v.string()),
    variables: v.optional(v.array(v.string())),
  },
  handler: async (ctx, args) => {
    const userId = await getCurrentUser(ctx);
    await checkAdminPermission(ctx, userId);

    // Validate template
    const validation = validateEmailTemplate(args);
    if (!validation.isValid) {
      throw new Error(validation.error);
    }

    // Get available variables for category
    const availableVariables = AVAILABLE_VARIABLES[args.category as keyof typeof AVAILABLE_VARIABLES];
    if (!availableVariables) {
      throw new Error(`Invalid category: ${args.category}`);
    }

    // Validate template variables
    const variableValidation = validateTemplateVariables(args.content, args.subject, availableVariables);
    if (!variableValidation.isValid) {
      throw new Error(variableValidation.error);
    }

    const templateId = await ctx.db.insert("emailTemplates", {
      name: args.name,
      subject: args.subject,
      content: args.content,
      category: args.category,
      description: args.description,
      variables: variableValidation.usedVariables || [],
      isActive: true,
      isDefault: false,
      createdBy: userId,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });

    return templateId;
  },
});

// Update email template
export const update = mutation({
  args: {
    id: v.id("emailTemplates"),
    name: v.optional(v.string()),
    subject: v.optional(v.string()),
    content: v.optional(v.string()),
    category: v.optional(v.string()),
    description: v.optional(v.string()),
    isActive: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const userId = await getCurrentUser(ctx);
    await checkAdminPermission(ctx, userId);

    const existing = await ctx.db.get(args.id);
    if (!existing) {
      throw new Error("Template not found");
    }

    // Build updated template object
    const updatedTemplate = {
      name: args.name ?? existing.name,
      subject: args.subject ?? existing.subject,
      content: args.content ?? existing.content,
      category: args.category ?? existing.category,
      description: args.description ?? existing.description,
    };

    // Validate updated template
    const validation = validateEmailTemplate(updatedTemplate);
    if (!validation.isValid) {
      throw new Error(validation.error);
    }

    // Get available variables for category
    const availableVariables = AVAILABLE_VARIABLES[updatedTemplate.category as keyof typeof AVAILABLE_VARIABLES];
    if (!availableVariables) {
      throw new Error(`Invalid category: ${updatedTemplate.category}`);
    }

    // Validate template variables
    const variableValidation = validateTemplateVariables(updatedTemplate.content, updatedTemplate.subject, availableVariables);
    if (!variableValidation.isValid) {
      throw new Error(variableValidation.error);
    }

    await ctx.db.patch(args.id, {
      ...updatedTemplate,
      variables: variableValidation.usedVariables || [],
      isActive: args.isActive ?? existing.isActive,
      updatedAt: Date.now(),
    });

    return args.id;
  },
});

// Delete email template
export const remove = mutation({
  args: { id: v.id("emailTemplates") },
  handler: async (ctx, args) => {
    const userId = await getCurrentUser(ctx);
    await checkAdminPermission(ctx, userId);

    const template = await ctx.db.get(args.id);
    if (!template) {
      throw new Error("Template not found");
    }

    // Don't allow deletion of default templates
    if (template.isDefault) {
      throw new Error("Cannot delete default templates");
    }

    await ctx.db.delete(args.id);
    return args.id;
  },
});

// Get template by category and name (for email service)
export const getByCategory = query({
  args: {
    category: v.string(),
    name: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    const userId = await getCurrentUser(ctx);
    await checkAdminPermission(ctx, userId);

    const templates = await ctx.db
      .query("emailTemplates")
      .withIndex("by_category", (q) => q.eq("category", args.category))
      .filter((q) => q.eq(q.field("isActive"), true))
      .collect();

    if (args.name) {
      return templates.find(t => t.name === args.name);
    }

    // Return the first active template for the category
    return templates[0];
  },
});

// Internal query for email service (no auth required)
export const getByCategoryInternal = internalQuery({
  args: {
    category: v.string(),
    name: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    const templates = await ctx.db
      .query("emailTemplates")
      .withIndex("by_category", (q) => q.eq("category", args.category))
      .filter((q) => q.eq(q.field("isActive"), true))
      .collect();

    if (args.name) {
      return templates.find(t => t.name === args.name);
    }

    // Return the first active template for the category
    return templates[0];
  },
});

// Initialize default email templates
export const initializeDefaults = mutation({
  args: {},
  handler: async (ctx) => {
    const userId = await getCurrentUser(ctx);
    await checkAdminPermission(ctx, userId);

    // Check if default templates already exist
    const existingDefaults = await ctx.db
      .query("emailTemplates")
      .withIndex("by_default", (q) => q.eq("isDefault", true))
      .collect();

    if (existingDefaults.length > 0) {
      return { message: "Default templates already exist", count: existingDefaults.length };
    }

    // Default invoice email template
    const defaultInvoiceTemplate = {
      name: "Default Invoice Email",
      subject: "Invoice {invoiceNumber} from {companyName}",
      content: `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Invoice {invoiceNumber} from {companyName}</title>
</head>
<body style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333333; background-color: #f8fafc; margin: 0; padding: 20px;">
  <div style="max-width: 600px; margin: 0 auto; background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);">

    <!-- Header -->
    <div style="background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); padding: 30px; text-align: center; color: white;">
      <h1 style="margin: 0; font-size: 28px; font-weight: 700;">{companyName}</h1>
      <p style="margin: 8px 0 0 0; font-size: 16px; opacity: 0.9;">Professional Invoice</p>
    </div>

    <!-- Content -->
    <div style="padding: 30px;">
      <div style="text-align: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #e5e7eb;">
        <h2 style="color: #1f2937; margin: 0 0 10px 0; font-size: 24px;">Invoice {invoiceNumber}</h2>
        <p style="color: #6b7280; margin: 0; font-size: 16px;">New invoice ready for your review</p>
      </div>

      <!-- Customer Info -->
      <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin-bottom: 25px; border-left: 4px solid #2563eb;">
        <h3 style="color: #1f2937; margin: 0 0 15px 0; font-size: 18px;">Bill To:</h3>
        <p style="margin: 4px 0; font-weight: 600; font-size: 16px;">{customerName}</p>
        <p style="margin: 4px 0; color: #2563eb; font-size: 14px;">📧 {customerEmail}</p>
        <p style="margin: 4px 0; color: #059669; font-size: 14px;">📞 {customerPhone}</p>
      </div>

      <!-- Invoice Details -->
      <div style="display: flex; justify-content: space-between; margin-bottom: 25px; flex-wrap: wrap;">
        <div style="flex: 1; margin-right: 20px; min-width: 200px;">
          <div style="background: #fef3c7; padding: 15px; border-radius: 8px; border-left: 4px solid #f59e0b;">
            <p style="margin: 0 0 5px 0; color: #92400e; font-size: 12px; font-weight: 600;">ISSUE DATE</p>
            <p style="margin: 0; color: #1f2937; font-size: 16px; font-weight: 600;">{invoiceDate}</p>
          </div>
        </div>
        <div style="flex: 1; min-width: 200px;">
          <div style="background: #fecaca; padding: 15px; border-radius: 8px; border-left: 4px solid #ef4444;">
            <p style="margin: 0 0 5px 0; color: #991b1b; font-size: 12px; font-weight: 600;">DUE DATE</p>
            <p style="margin: 0; color: #1f2937; font-size: 16px; font-weight: 600;">{dueDate}</p>
          </div>
        </div>
      </div>

      <!-- Total -->
      <div style="background: #f0f9ff; padding: 20px; border-radius: 8px; margin-bottom: 25px; border: 2px solid #2563eb; text-align: center;">
        <p style="margin: 0 0 5px 0; color: #1e40af; font-size: 14px; font-weight: 600;">TOTAL AMOUNT</p>
        <p style="margin: 0; color: #1f2937; font-size: 32px; font-weight: 700;">{total}</p>
      </div>

      <!-- Main CTA Button -->
      <div style="text-align: center; margin-bottom: 25px;">
        <a href="{invoiceUrl}" style="display: inline-block; background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); color: white; padding: 16px 32px; text-decoration: none; border-radius: 8px; font-weight: 600; font-size: 16px;">
          📄 View & Download Invoice
        </a>
      </div>

      <!-- PDF-Specific Download Section (shows additional PDF download button if different from main URL) -->
      <div style="text-align: center; margin-bottom: 20px;">
        {pdfDownloadButton}
      </div>

      <!-- PDF Attachment/Download Information -->
      <div style="text-align: center; margin-bottom: 20px;">
        {attachmentInfo}
      </div>

      <!-- Download Instructions (for PDF emails) -->
      <div style="background: #f0fdf4; padding: 15px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #059669;">
        <p style="margin: 0; color: #065f46; font-size: 14px; line-height: 1.5;">{downloadInstructions}</p>
      </div>

      <!-- Payment Terms -->
      <div style="background: #f3f4f6; padding: 15px; border-radius: 8px; text-align: center;">
        <p style="margin: 0; color: #374151; font-size: 14px;"><strong>Payment Terms:</strong> {paymentTerms}</p>
      </div>
    </div>

    <!-- Footer -->
    <div style="background: #1f2937; padding: 25px; text-align: center; color: white;">
      <h3 style="margin: 0 0 15px 0; font-size: 18px;">{companyName}</h3>
      <p style="margin: 0; color: #d1d5db; font-size: 14px;">📧 {companyEmail} | 📞 {companyPhone}</p>
      <p style="margin: 15px 0 0 0; font-size: 16px; font-weight: 600;">Thank you for your business! 🙏</p>
    </div>
  </div>
</body>
</html>`,
      category: "invoice",
      description: "Default template for invoice emails",
      variables: ["{customerName}", "{customerEmail}", "{customerPhone}", "{invoiceNumber}", "{invoiceDate}", "{dueDate}", "{total}", "{companyName}", "{companyEmail}", "{companyPhone}", "{paymentTerms}", "{invoiceUrl}", "{pdfDownloadUrl}", "{hasAttachment}", "{downloadInstructions}", "{pdfDownloadButton}", "{attachmentInfo}"],
      isActive: true,
      isDefault: true,
      createdBy: userId,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    };

    const invoiceTemplateId = await ctx.db.insert("emailTemplates", defaultInvoiceTemplate);

    return {
      message: "Default templates initialized successfully",
      templates: [invoiceTemplateId],
      count: 1
    };
  },
});

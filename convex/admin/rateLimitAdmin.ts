// Admin interface for rate limit monitoring and management
// Provides comprehensive rate limit administration capabilities

import { v } from "convex/values";
import { query, mutation } from "../_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";
import { 
  getRateLimitStatus, 
  resetUserRateLimits,
  RATE_LIMIT_CONFIGS,
  RateLimitEntry 
} from "../security/rateLimiting";

// Get current user helper
async function getCurrentUser(ctx: any) {
  const userId = await getAuthUserId(ctx);
  if (!userId) throw new Error("Not authenticated");
  return userId;
}

// Check if user has admin role
async function checkAdmin(ctx: any, userId: string) {
  const user = await ctx.db.get(userId);
  if (!user || (user.role !== "admin" && user.role !== "master")) {
    throw new Error("Unauthorized - Admin access required");
  }
  return user;
}

export interface RateLimitSummary {
  totalUsers: number;
  activeBlocks: number;
  recentActivity: number;
  topActions: Array<{ action: string; count: number }>;
}

export interface UserRateLimitInfo {
  userId: string;
  userEmail?: string;
  userName?: string;
  limits: RateLimitEntry[];
  isBlocked: boolean;
  blockExpires?: number;
  totalRequests: number;
}

// Get rate limit overview for admin dashboard
export const getRateLimitOverview = query({
  args: {},
  handler: async (ctx): Promise<RateLimitSummary> => {
    const userId = await getCurrentUser(ctx);
    await checkAdmin(ctx, userId);

    const now = Date.now();
    const oneHourAgo = now - (60 * 60 * 1000);

    // Get all rate limit entries
    const allLimits = await ctx.db.query("rateLimits").collect();

    // Calculate summary statistics
    const uniqueUsers = new Set(allLimits.map(limit => limit.userId)).size;
    const activeBlocks = allLimits.filter(limit => 
      limit.blocked && limit.blockExpires && limit.blockExpires > now
    ).length;
    const recentActivity = allLimits.filter(limit => 
      limit.lastRequest > oneHourAgo
    ).length;

    // Count actions
    const actionCounts = new Map<string, number>();
    allLimits.forEach(limit => {
      const count = actionCounts.get(limit.action) || 0;
      actionCounts.set(limit.action, count + limit.requestCount);
    });

    const topActions = Array.from(actionCounts.entries())
      .map(([action, count]) => ({ action, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    return {
      totalUsers: uniqueUsers,
      activeBlocks,
      recentActivity,
      topActions,
    };
  },
});

// Get detailed rate limit information for a specific user
export const getUserRateLimits = query({
  args: {
    userId: v.string(),
  },
  handler: async (ctx, args): Promise<UserRateLimitInfo | null> => {
    const adminUserId = await getCurrentUser(ctx);
    await checkAdmin(ctx, adminUserId);

    const limits = await getRateLimitStatus(ctx, args.userId);
    
    if (limits.length === 0) {
      return null;
    }

    const now = Date.now();
    const isBlocked = limits.some(limit => 
      limit.blocked && limit.blockExpires && limit.blockExpires > now
    );
    const blockExpires = limits
      .filter(limit => limit.blocked && limit.blockExpires)
      .reduce((max, limit) => Math.max(max, limit.blockExpires || 0), 0);

    const totalRequests = limits.reduce((sum, limit) => sum + limit.requestCount, 0);

    // Try to get user info if it's a user ID
    let userEmail: string | undefined;
    let userName: string | undefined;

    try {
      // Check if userId looks like a Convex ID and try to get user
      if (args.userId.startsWith('j') && args.userId.length > 10) {
        const user = await ctx.db.get(args.userId as any);
        // Only access email/name if it's actually a user document
        if (user && 'email' in user && 'name' in user) {
          userEmail = user.email;
          userName = user.name;
        }
      }
    } catch {
      // Not a user ID, might be email or IP
    }

    // If not found as user ID, check if it's an email
    if (!userEmail && args.userId.includes('@')) {
      userEmail = args.userId;
    }

    return {
      userId: args.userId,
      userEmail,
      userName,
      limits,
      isBlocked,
      blockExpires: blockExpires > 0 ? blockExpires : undefined,
      totalRequests,
    };
  },
});

// Get all users with active rate limits
export const getAllRateLimitedUsers = query({
  args: {
    includeExpired: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const userId = await getCurrentUser(ctx);
    await checkAdmin(ctx, userId);

    const now = Date.now();
    const oneHourAgo = now - (60 * 60 * 1000);

    let allLimits = await ctx.db.query("rateLimits").collect();

    // Filter out expired entries unless requested
    if (!args.includeExpired) {
      allLimits = allLimits.filter(limit => 
        limit.lastRequest > oneHourAgo || 
        (limit.blocked && limit.blockExpires && limit.blockExpires > now)
      );
    }

    // Group by user
    const userMap = new Map<string, RateLimitEntry[]>();
    allLimits.forEach(limit => {
      const existing = userMap.get(limit.userId) || [];
      existing.push(limit);
      userMap.set(limit.userId, existing);
    });

    // Convert to array with summary info
    const users: UserRateLimitInfo[] = [];
    for (const [userId, limits] of userMap.entries()) {
      const isBlocked = limits.some(limit => 
        limit.blocked && limit.blockExpires && limit.blockExpires > now
      );
      const blockExpires = limits
        .filter(limit => limit.blocked && limit.blockExpires)
        .reduce((max, limit) => Math.max(max, limit.blockExpires || 0), 0);
      const totalRequests = limits.reduce((sum, limit) => sum + limit.requestCount, 0);

      // Try to get user info
      let userEmail: string | undefined;
      let userName: string | undefined;
      
      try {
        // Check if userId looks like a Convex ID and try to get user
        if (userId.startsWith('j') && userId.length > 10) {
          const user = await ctx.db.get(userId as any);
          // Only access email/name if it's actually a user document
          if (user && 'email' in user && 'name' in user) {
            userEmail = user.email;
            userName = user.name;
          }
        }
      } catch {
        // Not a user ID
      }

      // If not found as user ID, check if it's an email
      if (!userEmail && userId.includes('@')) {
        userEmail = userId;
      }

      users.push({
        userId,
        userEmail,
        userName,
        limits,
        isBlocked,
        blockExpires: blockExpires > 0 ? blockExpires : undefined,
        totalRequests,
      });
    }

    // Sort by most recent activity
    users.sort((a, b) => {
      const aLatest = Math.max(...a.limits.map(l => l.lastRequest));
      const bLatest = Math.max(...b.limits.map(l => l.lastRequest));
      return bLatest - aLatest;
    });

    return users;
  },
});

// Reset rate limits for a specific user
export const resetUserRateLimit = mutation({
  args: {
    targetUserId: v.string(),
    action: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const adminUserId = await getCurrentUser(ctx);
    await checkAdmin(ctx, adminUserId);

    await resetUserRateLimits(ctx, args.targetUserId, args.action);

    // Log admin action
    console.log("Rate limits reset by admin:", {
      targetUserId: args.targetUserId.substring(0, 3) + "***",
      action: args.action,
      adminUserId,
      timestamp: new Date().toISOString(),
    });

    return { success: true };
  },
});

// Get rate limit configuration
export const getRateLimitConfig = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getCurrentUser(ctx);
    await checkAdmin(ctx, userId);

    return Object.entries(RATE_LIMIT_CONFIGS).map(([key, config]) => ({
      key,
      ...config,
      windowMinutes: Math.round(config.windowMs / (60 * 1000)),
      blockDurationMinutes: config.blockDurationMs ? 
        Math.round(config.blockDurationMs / (60 * 1000)) : undefined,
    }));
  },
});

// Get rate limit statistics by action
export const getRateLimitStatsByAction = query({
  args: {
    timeRangeHours: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userId = await getCurrentUser(ctx);
    await checkAdmin(ctx, userId);

    const timeRange = args.timeRangeHours || 24;
    const cutoff = Date.now() - (timeRange * 60 * 60 * 1000);

    const recentLimits = await ctx.db
      .query("rateLimits")
      .withIndex("by_last_request")
      .filter((q) => q.gte(q.field("lastRequest"), cutoff))
      .collect();

    // Group by action
    const actionStats = new Map<string, {
      totalRequests: number;
      uniqueUsers: number;
      blockedUsers: number;
      avgRequestsPerUser: number;
    }>();

    recentLimits.forEach(limit => {
      const existing = actionStats.get(limit.action) || {
        totalRequests: 0,
        uniqueUsers: 0,
        blockedUsers: 0,
        avgRequestsPerUser: 0,
      };

      existing.totalRequests += limit.requestCount;
      existing.uniqueUsers += 1;
      if (limit.blocked) {
        existing.blockedUsers += 1;
      }

      actionStats.set(limit.action, existing);
    });

    // Calculate averages
    actionStats.forEach((stats, action) => {
      stats.avgRequestsPerUser = stats.uniqueUsers > 0 ? 
        Math.round(stats.totalRequests / stats.uniqueUsers * 100) / 100 : 0;
    });

    return Array.from(actionStats.entries()).map(([action, stats]) => ({
      action,
      ...stats,
    }));
  },
});

// Emergency function to clear all rate limits (use with caution)
export const emergencyClearAllRateLimits = mutation({
  args: {
    confirmationCode: v.string(),
  },
  handler: async (ctx, args) => {
    const adminUserId = await getCurrentUser(ctx);
    await checkAdmin(ctx, adminUserId);

    // Require confirmation code for safety
    if (args.confirmationCode !== "EMERGENCY_CLEAR_ALL_LIMITS") {
      throw new Error("Invalid confirmation code");
    }

    const allLimits = await ctx.db.query("rateLimits").collect();
    
    for (const limit of allLimits) {
      await ctx.db.delete(limit._id);
    }

    // Log emergency action
    console.warn("EMERGENCY: All rate limits cleared by admin:", {
      adminUserId,
      clearedCount: allLimits.length,
      timestamp: new Date().toISOString(),
    });

    return { 
      success: true, 
      clearedCount: allLimits.length 
    };
  },
});

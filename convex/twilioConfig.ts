import { v } from "convex/values";
import { query, mutation, action, internalQuery } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";
import { internal } from "./_generated/api";
import { validateTwilioConfig, validateSMSPermissions, validateTwilioCredentialsFormat } from "./sms/validation";
import { encryptCredential, decryptCredentialLegacy, validateEncryptionConfig } from "./security/encryption";

// Get current user helper
async function getCurrentUser(ctx: any) {
  const userId = await getAuthUserId(ctx);
  if (!userId) throw new Error("Not authenticated");
  return userId;
}

// Check if user has admin role
async function checkAdmin(ctx: any, userId: string) {
  const user = await ctx.db.get(userId);
  if (!user || (user.role !== "admin" && user.role !== "master")) {
    throw new Error("Unauthorized - Admin access required");
  }
}

// Get Twilio configuration (admin only, returns masked credentials)
export const getConfig = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getCurrentUser(ctx);
    await checkAdmin(ctx, userId);
    
    const config = await ctx.db
      .query("twilioSettings")
      .withIndex("by_active", (q) => q.eq("isActive", true))
      .first();
    
    if (!config) {
      return null;
    }
    
    // Return masked credentials for security
    return {
      _id: config._id,
      accountSid: config.accountSid ? maskCredential(config.accountSid) : "",
      authToken: config.authToken ? maskCredential(config.authToken) : "",
      phoneNumber: config.phoneNumber,
      isActive: config.isActive,
      testMode: config.testMode || false,
      createdAt: config.createdAt,
      updatedAt: config.updatedAt,
    };
  },
});

// Test Twilio configuration (admin only)
export const testConfig = action({
  args: {
    accountSid: v.string(),
    authToken: v.string(),
    phoneNumber: v.string(),
  },
  handler: async (ctx, args) => {
    const userId = await getCurrentUser(ctx);
    // For actions, we need to use runQuery to get user data
    const user = await ctx.runQuery(internal.twilioConfig.getUserById, { userId });
    
    // Validate admin permissions
    const permissionValidation = validateSMSPermissions(user, "configure_twilio");
    if (!permissionValidation.isValid) {
      throw new Error(permissionValidation.error);
    }

    try {
      // Validate credentials format first
      const validation = validateTwilioCredentialsFormat(
        args.accountSid,
        args.authToken,
        args.phoneNumber
      );

      if (!validation.isValid) {
        return {
          success: false,
          error: validation.error || "Invalid Twilio credentials format",
        };
      }

      // For now, we'll just validate the format since we don't have the actual Twilio SDK
      // In production, you would test the actual connection here
      return {
        success: true,
        accountName: "Twilio Account",
        accountStatus: "active",
        phoneNumber: args.phoneNumber,
        note: "Configuration format validated. Full testing requires Twilio SDK integration.",
      };
    } catch (error: any) {
      return {
        success: false,
        error: `Twilio configuration test failed: ${error.message}`,
      };
    }
  },
});

// Save Twilio configuration (admin only)
export const saveConfig = mutation({
  args: {
    accountSid: v.string(),
    authToken: v.string(),
    phoneNumber: v.string(),
    testMode: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const userId = await getCurrentUser(ctx);
    await checkAdmin(ctx, userId);

    // Validate configuration format
    if (!args.accountSid.startsWith('AC') || args.accountSid.length !== 34) {
      throw new Error("Invalid Account SID format. Should start with 'AC' and be 34 characters long.");
    }

    if (args.authToken.length !== 32) {
      throw new Error("Invalid Auth Token format. Should be 32 characters long.");
    }

    // Deactivate existing configurations
    const existingConfigs = await ctx.db
      .query("twilioSettings")
      .withIndex("by_active", (q) => q.eq("isActive", true))
      .collect();

    for (const config of existingConfigs) {
      await ctx.db.patch(config._id, { isActive: false });
    }

    // Validate encryption configuration
    const encryptionValidation = await validateEncryptionConfig();
    if (!encryptionValidation.isValid) {
      throw new Error(`Encryption not configured: ${encryptionValidation.error}`);
    }

    // Encrypt sensitive credentials before storage
    const encryptedAuthToken = await encryptCredential(args.authToken);

    // Create new configuration with encrypted credentials
    const configId = await ctx.db.insert("twilioSettings", {
      accountSid: args.accountSid,
      authToken: JSON.stringify(encryptedAuthToken), // Serialize the encryption result
      phoneNumber: args.phoneNumber,
      isActive: true,
      testMode: args.testMode || false,
      createdBy: userId,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });

    return configId;
  },
});

// Update Twilio configuration (admin only)
export const updateConfig = mutation({
  args: {
    configId: v.id("twilioSettings"),
    accountSid: v.optional(v.string()),
    authToken: v.optional(v.string()),
    phoneNumber: v.optional(v.string()),
    testMode: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const userId = await getCurrentUser(ctx);
    await checkAdmin(ctx, userId);

    const config = await ctx.db.get(args.configId);
    if (!config) {
      throw new Error("Twilio configuration not found.");
    }

    // Validate new values if provided
    if (args.accountSid) {
      if (!args.accountSid.startsWith('AC') || args.accountSid.length !== 34) {
        throw new Error("Invalid Account SID format. Should start with 'AC' and be 34 characters long.");
      }
    }

    if (args.authToken) {
      if (args.authToken.length !== 32) {
        throw new Error("Invalid Auth Token format. Should be 32 characters long.");
      }
    }

    // Update configuration
    const updateData: any = {
      updatedAt: Date.now(),
    };

    if (args.accountSid) updateData.accountSid = args.accountSid;
    if (args.authToken) updateData.authToken = args.authToken;
    if (args.phoneNumber) updateData.phoneNumber = args.phoneNumber;
    if (args.testMode !== undefined) updateData.testMode = args.testMode;

    await ctx.db.patch(args.configId, updateData);
    
    return args.configId;
  },
});

// Delete Twilio configuration (admin only)
export const deleteConfig = mutation({
  args: { configId: v.id("twilioSettings") },
  handler: async (ctx, args) => {
    const userId = await getCurrentUser(ctx);
    await checkAdmin(ctx, userId);

    const config = await ctx.db.get(args.configId);
    if (!config) {
      throw new Error("Twilio configuration not found.");
    }

    await ctx.db.delete(args.configId);
    return true;
  },
});

// Get configuration status (admin only)
export const getConfigStatus = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getCurrentUser(ctx);
    await checkAdmin(ctx, userId);

    const config = await ctx.db
      .query("twilioSettings")
      .withIndex("by_active", (q) => q.eq("isActive", true))
      .first();

    return {
      isConfigured: !!config,
      isActive: config?.isActive || false,
      testMode: config?.testMode || false,
      phoneNumber: config?.phoneNumber || null,
      lastUpdated: config?.updatedAt || null,
    };
  },
});

// Toggle test mode (admin only)
export const toggleTestMode = mutation({
  args: { 
    configId: v.id("twilioSettings"),
    testMode: v.boolean(),
  },
  handler: async (ctx, args) => {
    const userId = await getCurrentUser(ctx);
    await checkAdmin(ctx, userId);

    const config = await ctx.db.get(args.configId);
    if (!config) {
      throw new Error("Twilio configuration not found.");
    }

    await ctx.db.patch(args.configId, {
      testMode: args.testMode,
      updatedAt: Date.now(),
    });

    return true;
  },
});

// Helper function to mask credentials for display
function maskCredential(credential: string): string {
  if (credential.length <= 8) {
    return "*".repeat(credential.length);
  }
  
  const start = credential.substring(0, 4);
  const end = credential.substring(credential.length - 4);
  const middle = "*".repeat(credential.length - 8);
  
  return start + middle + end;
}

// Get environment variable configuration status (for migration)
export const getEnvConfigStatus = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getCurrentUser(ctx);
    await checkAdmin(ctx, userId);

    // Check if environment variables are set
    const hasEnvConfig = !!(
      process.env.TWILIO_ACCOUNT_SID &&
      process.env.TWILIO_AUTH_TOKEN &&
      process.env.TWILIO_PHONE_NUMBER
    );

    return {
      hasEnvironmentConfig: hasEnvConfig,
      accountSid: process.env.TWILIO_ACCOUNT_SID ? maskCredential(process.env.TWILIO_ACCOUNT_SID) : null,
      phoneNumber: process.env.TWILIO_PHONE_NUMBER || null,
    };
  },
});

// Migrate from environment variables to database (admin only)
export const migrateFromEnv = mutation({
  args: {},
  handler: async (ctx) => {
    const userId = await getCurrentUser(ctx);
    await checkAdmin(ctx, userId);

    const accountSid = process.env.TWILIO_ACCOUNT_SID;
    const authToken = process.env.TWILIO_AUTH_TOKEN;
    const phoneNumber = process.env.TWILIO_PHONE_NUMBER;

    if (!accountSid || !authToken || !phoneNumber) {
      throw new Error("Environment variables not found. Cannot migrate configuration.");
    }

    // Check if configuration already exists
    const existingConfig = await ctx.db
      .query("twilioSettings")
      .withIndex("by_active", (q) => q.eq("isActive", true))
      .first();

    if (existingConfig) {
      throw new Error("Twilio configuration already exists in database.");
    }

    // Create configuration from environment variables
    const configId = await ctx.db.insert("twilioSettings", {
      accountSid,
      authToken,
      phoneNumber,
      isActive: true,
      testMode: false,
      createdBy: userId,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });

    return configId;
  },
});

// Internal query to get user by ID (for actions)
export const getUserById = internalQuery({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.userId);
  },
});

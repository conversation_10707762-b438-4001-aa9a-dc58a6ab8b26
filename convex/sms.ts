import { v } from "convex/values";
import { query, mutation, action, internalQuery, internalMutation } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";
import { internal } from "./_generated/api";
import {
  validateCustomerForSMS,
  validateSMSMessage,
  validateTwilioConfig,
  validateSMSPermissions,
} from "./sms/validation";
import {
  validateRateLimit,
  recordRateLimitAction,
} from "./sms/security";
import {
  createTwilioClient,
  getTwilioConfig,
  sendSMS,
  formatPhoneNumber,
  replaceTemplateVariables,
  logSMSOperation,
} from "./sms/service";

// Get current user helper
async function getCurrentUser(ctx: any) {
  const userId = await getAuthUserId(ctx);
  if (!userId) throw new Error("Not authenticated");
  return userId;
}

// Check if user has admin role
async function checkAdmin(ctx: any, userId: string) {
  const user = await ctx.db.get(userId);
  if (!user || (user.role !== "admin" && user.role !== "master")) {
    throw new Error("Unauthorized - Admin access required");
  }
}

// Send SMS to customer
export const sendToCustomer = action({
  args: {
    customerId: v.id("customers"),
    message: v.string(),
    templateId: v.optional(v.id("smsTemplates")),
    jobId: v.optional(v.id("jobs")),
    invoiceId: v.optional(v.id("invoices")),
  },
  handler: async (ctx, args) => {
    const userId = await getCurrentUser(ctx);
    await checkAdmin(ctx, userId); // SMS sending is admin-only

    // Check SMS rate limits before proceeding
    const rateLimitCheck = await validateRateLimit(ctx, userId, "sms_send");
    if (!rateLimitCheck.isValid) {
      throw new Error(rateLimitCheck.error || "SMS rate limit exceeded. Please wait before sending more messages.");
    }

    // Get customer data
    const customer = await ctx.runQuery(internal.sms.getCustomerById, { id: args.customerId });

    if (!customer) {
      throw new Error("Customer not found.");
    }

    // Validate customer for SMS
    const customerValidation = validateCustomerForSMS(customer);
    if (!customerValidation.isValid) {
      throw new Error(customerValidation.error);
    }

    // Get Twilio configuration
    const twilioSettings = await ctx.runQuery(internal.sms.getTwilioSettings, {});
    if (!twilioSettings) {
      throw new Error("SMS service not configured. Please contact your administrator.");
    }

    const config = getTwilioConfig(twilioSettings);
    const configValidation = validateTwilioConfig();
    if (!configValidation.isValid) {
      throw new Error(configValidation.error);
    }

    // Format phone number
    const formattedPhone = formatPhoneNumber(customer.phone);
    
    // Prepare SMS message
    const smsMessage = {
      to: formattedPhone,
      body: args.message,
    };

    // Validate SMS message
    const messageValidation = validateSMSMessage(smsMessage);
    if (!messageValidation.isValid) {
      throw new Error(messageValidation.error);
    }

    try {
      // Create Twilio client and send SMS
      const client = createTwilioClient(config);
      const result = await sendSMS(client, smsMessage, config.phoneNumber);

      // Log the SMS in history
      await ctx.runMutation(internal.sms.insertSMSHistory, {
        customerId: args.customerId,
        jobId: args.jobId,
        invoiceId: args.invoiceId,
        templateId: args.templateId,
        phoneNumber: formattedPhone,
        message: args.message,
        status: result.success ? "sent" : "failed",
        twilioSid: result.messageSid,
        errorMessage: result.error,
        sentBy: userId,
        sentAt: Date.now(),
        deliveredAt: result.success && result.status === "delivered" ? Date.now() : undefined,
      });

      logSMSOperation("sendToCustomer", {
        customerId: args.customerId,
        phoneNumber: formattedPhone,
        messageSid: result.messageSid,
      }, result.success, result.error);

      // Record successful SMS for rate limiting
      if (result.success) {
        await recordRateLimitAction(ctx, userId, "sms_send");
      }

      return result;
    } catch (error: any) {
      // Log failed attempt
      await ctx.runMutation(internal.sms.insertSMSHistory, {
        customerId: args.customerId,
        jobId: args.jobId,
        invoiceId: args.invoiceId,
        templateId: args.templateId,
        phoneNumber: formattedPhone,
        message: args.message,
        status: "failed",
        errorMessage: error.message,
        sentBy: userId,
        sentAt: Date.now(),
      });

      logSMSOperation("sendToCustomer", {
        customerId: args.customerId,
        phoneNumber: formattedPhone,
      }, false, error.message);

      throw error;
    }
  },
});

// Send SMS using template
export const sendWithTemplate: any = action({
  args: {
    customerId: v.id("customers"),
    templateId: v.id("smsTemplates"),
    variables: v.optional(v.object({})),
    jobId: v.optional(v.id("jobs")),
    invoiceId: v.optional(v.id("invoices")),
  },
  handler: async (ctx, args): Promise<any> => {
    const userId = await getCurrentUser(ctx);
    await checkAdmin(ctx, userId); // SMS sending is admin-only

    // Get template
    const template = await ctx.runQuery(internal.sms.getTemplateById, { id: args.templateId });

    if (!template || !template.isActive) {
      throw new Error("SMS template not found or inactive.");
    }

    // Get customer data for variable replacement
    const customer = await ctx.runQuery(internal.sms.getCustomerById, { id: args.customerId });

    if (!customer) {
      throw new Error("Customer not found.");
    }

    // Prepare template variables
    const templateVariables = {
      customerName: customer.name || "",
      companyName: customer.company || "",
      ...args.variables,
    };

    // Replace variables in template
    const message = replaceTemplateVariables(template.content, templateVariables);

    // For now, return an error since we don't have real Twilio integration
    // In production, this would call the sendToCustomer action
    throw new Error("SMS service is not available. Please configure Twilio integration.");
  },
});

// Get SMS history for a customer (staff and admin access)
export const getCustomerHistory = query({
  args: { customerId: v.id("customers") },
  handler: async (ctx, args) => {
    const userId = await getCurrentUser(ctx);
    // Allow staff and admin users to access customer conversation history
    const user = await ctx.db.get(userId);
    if (!user || (user.role !== "staff" && user.role !== "admin")) {
      throw new Error("Unauthorized - Staff or Admin access required");
    }

    return await ctx.db
      .query("smsHistory")
      .withIndex("by_customer", (q) => q.eq("customerId", args.customerId))
      .order("desc")
      .collect();
  },
});

// Get SMS history for current user
export const getMyHistory = query({
  args: { limit: v.optional(v.number()) },
  handler: async (ctx, args) => {
    const userId = await getCurrentUser(ctx);
    
    const query = ctx.db
      .query("smsHistory")
      .withIndex("by_sent_by", (q) => q.eq("sentBy", userId))
      .order("desc");
    
    if (args.limit) {
      return await query.take(args.limit);
    }
    
    return await query.collect();
  },
});

// Get all SMS history (admin only)
export const getAllHistory = query({
  args: {
    limit: v.optional(v.number()),
    status: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getCurrentUser(ctx);
    await checkAdmin(ctx, userId);

    if (args.status) {
      const query = ctx.db
        .query("smsHistory")
        .withIndex("by_status", (q) => q.eq("status", args.status!))
        .order("desc");

      if (args.limit) {
        return await query.take(args.limit);
      }
      return await query.collect();
    } else {
      const query = ctx.db
        .query("smsHistory")
        .order("desc");

      if (args.limit) {
        return await query.take(args.limit);
      }
      return await query.collect();
    }
  },
});

// Internal function to log SMS history
export const logSMSHistory = mutation({
  args: {
    customerId: v.id("customers"),
    jobId: v.optional(v.id("jobs")),
    invoiceId: v.optional(v.id("invoices")),
    templateId: v.optional(v.id("smsTemplates")),
    phoneNumber: v.string(),
    message: v.string(),
    status: v.string(),
    twilioSid: v.optional(v.string()),
    errorMessage: v.optional(v.string()),
    sentBy: v.id("users"),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("smsHistory", {
      ...args,
      sentAt: Date.now(),
      deliveredAt: args.status === "delivered" ? Date.now() : undefined,
    });
  },
});

// Internal function to get Twilio settings
export const getTwilioSettings = internalQuery({
  args: {},
  handler: async (ctx) => {
    // Get the active Twilio configuration
    const settings = await ctx.db
      .query("twilioSettings")
      .withIndex("by_active", (q) => q.eq("isActive", true))
      .first();

    if (!settings) {
      return null;
    }

    // Decrypt the auth token before returning
    try {
      const { decryptCredentialLegacy } = await import("./security/encryption");
      const decryptedAuthToken = decryptCredentialLegacy(settings.authToken);

      return {
        ...settings,
        authToken: decryptedAuthToken,
      };
    } catch (error) {
      console.error("Failed to decrypt Twilio auth token:", error);
      throw new Error("Failed to decrypt Twilio credentials");
    }
  },
});

// Internal function to get template by ID
export const getTemplateById = internalQuery({
  args: { id: v.id("smsTemplates") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.id);
  },
});

// Internal function to get customer by ID
export const getCustomerById = internalQuery({
  args: { id: v.id("customers") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.id);
  },
});

// Internal function to insert SMS history record
export const insertSMSHistory = internalMutation({
  args: {
    customerId: v.id("customers"),
    jobId: v.optional(v.id("jobs")),
    invoiceId: v.optional(v.id("invoices")),
    templateId: v.optional(v.id("smsTemplates")),
    phoneNumber: v.string(),
    message: v.string(),
    status: v.string(),
    twilioSid: v.optional(v.string()),
    errorMessage: v.optional(v.string()),
    sentBy: v.id("users"),
    sentAt: v.number(),
    deliveredAt: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("smsHistory", args);
  },
});



// Update SMS delivery status (webhook handler)
export const updateDeliveryStatus = mutation({
  args: {
    twilioSid: v.string(),
    status: v.string(),
    errorCode: v.optional(v.string()),
    errorMessage: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Find SMS record by Twilio SID
    const smsRecord = await ctx.db
      .query("smsHistory")
      .filter((q) => q.eq(q.field("twilioSid"), args.twilioSid))
      .first();

    if (!smsRecord) {
      console.warn(`SMS record not found for Twilio SID: ${args.twilioSid}`);
      return;
    }

    const updateData: any = {
      status: args.status,
    };

    if (args.status === "delivered") {
      updateData.deliveredAt = Date.now();
    }

    if (args.errorMessage) {
      updateData.errorMessage = args.errorMessage;
    }

    await ctx.db.patch(smsRecord._id, updateData);

    logSMSOperation("statusUpdate", {
      twilioSid: args.twilioSid,
      newStatus: args.status,
      customerId: smsRecord.customerId,
    }, true);

    return smsRecord._id;
  },
});

// Get SMS statistics (admin only)
export const getStatistics = query({
  args: {
    startDate: v.optional(v.number()),
    endDate: v.optional(v.number()),
    userId: v.optional(v.id("users")),
  },
  handler: async (ctx, args) => {
    const userId = await getCurrentUser(ctx);
    await checkAdmin(ctx, userId);

    let records;

    // Apply user filter first if provided
    if (args.userId) {
      records = await ctx.db
        .query("smsHistory")
        .withIndex("by_sent_by", (q) => q.eq("sentBy", args.userId!))
        .collect();
    } else if (args.startDate || args.endDate) {
      // Apply date filters
      let query = ctx.db.query("smsHistory").withIndex("by_sent_at");
      if (args.startDate) {
        query = query.filter((q) => q.gte(q.field("sentAt"), args.startDate!));
      }
      if (args.endDate) {
        query = query.filter((q) => q.lte(q.field("sentAt"), args.endDate!));
      }
      records = await query.collect();
    } else {
      records = await ctx.db.query("smsHistory").collect();
    }

    // Calculate statistics
    const stats = {
      total: records.length,
      sent: records.filter(r => r.status === "sent").length,
      delivered: records.filter(r => r.status === "delivered").length,
      failed: records.filter(r => r.status === "failed").length,
      pending: records.filter(r => r.status === "pending").length,
      byCategory: {} as Record<string, number>,
      byUser: {} as Record<string, number>,
      deliveryRate: 0,
    };

    // Calculate delivery rate
    const deliverable = stats.sent + stats.delivered;
    if (deliverable > 0) {
      stats.deliveryRate = (stats.delivered / deliverable) * 100;
    }

    // Group by template category
    for (const record of records) {
      if (record.templateId) {
        // We'd need to fetch the template to get the category
        // For now, we'll skip this or implement it separately
      }
    }

    // Group by user
    for (const record of records) {
      const userKey = record.sentBy;
      stats.byUser[userKey] = (stats.byUser[userKey] || 0) + 1;
    }

    return stats;
  },
});

// Get recent SMS activity (admin only)
export const getRecentActivity = query({
  args: {
    limit: v.optional(v.number()),
    customerId: v.optional(v.id("customers")),
  },
  handler: async (ctx, args) => {
    const userId = await getCurrentUser(ctx);
    await checkAdmin(ctx, userId);

    let records;

    if (args.customerId) {
      records = await ctx.db
        .query("smsHistory")
        .withIndex("by_customer", (q) => q.eq("customerId", args.customerId!))
        .order("desc")
        .take(args.limit || 50);
    } else {
      records = await ctx.db
        .query("smsHistory")
        .order("desc")
        .take(args.limit || 50);
    }

    // Enrich with customer and user information
    const enrichedRecords = [];
    for (const record of records) {
      const customer = await ctx.db.get(record.customerId);
      const sender = await ctx.db.get(record.sentBy);

      enrichedRecords.push({
        ...record,
        customer: customer ? {
          name: customer.name,
          company: customer.company,
        } : null,
        sender: sender ? {
          name: sender.name,
          email: sender.email,
        } : null,
      });
    }

    return enrichedRecords;
  },
});

// Search SMS history
export const searchHistory = query({
  args: {
    query: v.string(),
    status: v.optional(v.string()),
    customerId: v.optional(v.id("customers")),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userId = await getCurrentUser(ctx);

    let records;

    if (args.status) {
      records = await ctx.db
        .query("smsHistory")
        .withIndex("by_status", (q) => q.eq("status", args.status!))
        .collect();
    } else if (args.customerId) {
      records = await ctx.db
        .query("smsHistory")
        .withIndex("by_customer", (q) => q.eq("customerId", args.customerId!))
        .collect();
    } else {
      records = await ctx.db.query("smsHistory").collect();
    }

    // Filter by search query (message content or phone number)
    const searchTerm = args.query.toLowerCase();
    const filteredRecords = records.filter(record =>
      record.message.toLowerCase().includes(searchTerm) ||
      record.phoneNumber.includes(searchTerm)
    );

    // Sort by date and limit
    const sortedRecords = filteredRecords
      .sort((a, b) => b.sentAt - a.sentAt)
      .slice(0, args.limit || 100);

    return sortedRecords;
  },
});

// Export SMS history (admin only)
export const exportHistory = query({
  args: {
    startDate: v.optional(v.number()),
    endDate: v.optional(v.number()),
    format: v.optional(v.string()), // "csv" or "json"
  },
  handler: async (ctx, args) => {
    const userId = await getCurrentUser(ctx);
    await checkAdmin(ctx, userId);

    let records;

    // Apply date filters
    if (args.startDate || args.endDate) {
      let query = ctx.db.query("smsHistory").withIndex("by_sent_at");
      if (args.startDate) {
        query = query.filter((q) => q.gte(q.field("sentAt"), args.startDate!));
      }
      if (args.endDate) {
        query = query.filter((q) => q.lte(q.field("sentAt"), args.endDate!));
      }
      records = await query.collect();
    } else {
      records = await ctx.db.query("smsHistory").collect();
    }

    // Enrich with related data
    const enrichedRecords = [];
    for (const record of records) {
      const customer = await ctx.db.get(record.customerId);
      const sender = await ctx.db.get(record.sentBy);

      enrichedRecords.push({
        id: record._id,
        sentAt: new Date(record.sentAt).toISOString(),
        deliveredAt: record.deliveredAt ? new Date(record.deliveredAt).toISOString() : null,
        customerName: customer?.name || "Unknown",
        customerCompany: customer?.company || "",
        phoneNumber: record.phoneNumber,
        message: record.message,
        status: record.status,
        senderName: sender?.name || "Unknown",
        senderEmail: sender?.email || "",
        twilioSid: record.twilioSid || "",
        errorMessage: record.errorMessage || "",
      });
    }

    return enrichedRecords;
  },
});

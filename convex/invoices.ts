import { v } from "convex/values";
import { mutation, query, internalQuery, internalMutation, action } from "./_generated/server";
import { internal } from "./_generated/api";
import {
  getCurrentUserWithRole,
  checkAdminPermission,
  checkStaffOrAdminPermission,
  canDeleteRecord
} from "./auth";

// List all invoices (staff and admin can see all invoices)
export const list = query({
  args: {},
  handler: async (ctx) => {
    // Check if user has staff or admin permissions
    await checkStaffOrAdminPermission(ctx);

    // Staff and admin users can see all invoices
    const invoices = await ctx.db
      .query("invoices")
      .order("desc")
      .collect();

    // Get customer and job data for each invoice
    const invoicesWithCustomers = await Promise.all(
      invoices.map(async (invoice) => {
        const customer = await ctx.db.get(invoice.customerId);
        const job = invoice.jobId ? await ctx.db.get(invoice.jobId) : null;
        return {
          ...invoice,
          customer,
          job,
        };
      })
    );

    return invoicesWithCustomers;
  },
});

// Get a single invoice by ID (staff and admin can access any invoice)
export const get = query({
  args: { id: v.id("invoices") },
  handler: async (ctx, args) => {
    // Check if user has staff or admin permissions
    await checkStaffOrAdminPermission(ctx);

    const invoice = await ctx.db.get(args.id);
    if (!invoice) {
      // Return null instead of throwing error to handle deleted invoices gracefully
      return null;
    }

    // Get customer and job details
    const customer = await ctx.db.get(invoice.customerId);
    const job = invoice.jobId ? await ctx.db.get(invoice.jobId) : null;

    // Return invoice with customer and job data
    return {
      ...invoice,
      customer,
      job,
    };
  },
});

// Create a new invoice
export const create = mutation({
  args: {
    invoiceNumber: v.string(),
    customerId: v.id("customers"),
    jobId: v.optional(v.id("jobs")),
    issueDate: v.string(),
    dueDate: v.string(),
    items: v.array(
      v.object({
        description: v.string(),
        quantity: v.number(),
        unitPrice: v.number(),
      })
    ),
    notes: v.optional(v.string()),
    terms: v.optional(v.string()),
    tax: v.optional(v.number()),
    discount: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    // Check if user has staff or admin permissions
    const { user } = await checkStaffOrAdminPermission(ctx);

    const now = Date.now();

    // Calculate subtotal, tax, and total
    const subtotal = args.items.reduce(
      (sum, item) => sum + item.quantity * item.unitPrice,
      0
    );

    const taxAmount = args.tax ? subtotal * (args.tax / 100) : 0;
    const discountAmount = args.discount ? subtotal * (args.discount / 100) : 0;
    const total = subtotal + taxAmount - discountAmount;

    // Extract fields that shouldn't be passed to the database
    const { tax, discount, ...dbFields } = args;

    return await ctx.db.insert("invoices", {
      ...dbFields,
      issueDate: new Date(args.issueDate).getTime(), // Convert string to timestamp
      dueDate: new Date(args.dueDate).getTime(), // Convert string to timestamp
      subtotal,
      taxRate: tax || 0, // Add taxRate from tax percentage
      taxAmount,
      discountAmount,
      total,
      status: "draft", // Default status
      createdBy: user._id,
      createdAt: now,
      updatedAt: now,
    });
  },
});

// Update an existing invoice
export const update = mutation({
  args: {
    id: v.id("invoices"),
    invoiceNumber: v.string(),
    customerId: v.id("customers"),
    jobId: v.optional(v.id("jobs")),
    issueDate: v.string(),
    dueDate: v.string(),
    items: v.array(
      v.object({
        description: v.string(),
        quantity: v.number(),
        unitPrice: v.number(),
      })
    ),
    notes: v.optional(v.string()),
    terms: v.optional(v.string()),
    tax: v.optional(v.number()),
    discount: v.optional(v.number()),
    status: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Check if user has staff or admin permissions
    await checkStaffOrAdminPermission(ctx);

    const { id, items, tax, discount, ...updates } = args;

    const invoice = await ctx.db.get(id);
    if (!invoice) {
      throw new Error("Invoice not found");
    }

    // Calculate subtotal, tax, and total
    const subtotal = items.reduce(
      (sum, item) => sum + item.quantity * item.unitPrice,
      0
    );

    const taxAmount = tax ? subtotal * (tax / 100) : 0;
    const discountAmount = discount ? subtotal * (discount / 100) : 0;
    const total = subtotal + taxAmount - discountAmount;

    await ctx.db.patch(id, {
      ...updates,
      issueDate: new Date(updates.issueDate).getTime(), // Convert string to timestamp
      dueDate: new Date(updates.dueDate).getTime(), // Convert string to timestamp
      items,
      subtotal,
      taxRate: tax || 0, // Add taxRate from tax percentage
      taxAmount,
      discountAmount,
      total,
      updatedAt: Date.now(),
    });
  },
});

// Delete an invoice (admin only)
export const remove = mutation({
  args: { id: v.id("invoices") },
  handler: async (ctx, args) => {
    // Check if user has admin permissions (only admins can delete)
    await checkAdminPermission(ctx);

    const invoice = await ctx.db.get(args.id);
    if (!invoice) {
      throw new Error("Invoice not found");
    }
    await ctx.db.delete(args.id);
  },
});

// Search invoices by invoice number or customer (staff and admin can search all invoices)
export const search = query({
  args: { query: v.string() },
  handler: async (ctx, args) => {
    // Check if user has staff or admin permissions
    await checkStaffOrAdminPermission(ctx);

    // Staff and admin users can search all invoices
    const invoices = await ctx.db
      .query("invoices")
      .collect();

    // Get all customers for the invoices - fix for Set iteration
    const customerIdsSet = new Set(invoices.map(invoice => invoice.customerId));
    const customerIds = Array.from(customerIdsSet);

    const customers = await Promise.all(
      customerIds.map(id => ctx.db.get(id))
    );

    // Create a map of customer IDs to customer objects
    const customerMap = new Map();
    customers.forEach(customer => {
      if (customer) {
        customerMap.set(customer._id, customer);
      }
    });

    const searchTerm = args.query.toLowerCase();
    const filteredInvoices = invoices.filter(
      (invoice) =>
        invoice.invoiceNumber.toLowerCase().includes(searchTerm) ||
        (customerMap.get(invoice.customerId) &&
         customerMap.get(invoice.customerId).name.toLowerCase().includes(searchTerm))
    );

    // Return invoices with customer data included
    return filteredInvoices.map(invoice => ({
      ...invoice,
      customer: customerMap.get(invoice.customerId),
    }));
  },
});

// Import invoices from CSV-formatted data
export const bulkImport = mutation({
  args: {
    invoices: v.array(
      v.object({
        id: v.optional(v.string()),
        invoiceNumber: v.string(),
        customerId: v.id("customers"),
        jobId: v.optional(v.id("jobs")),
        issueDate: v.string(),
        dueDate: v.string(),
        items: v.array(
          v.object({
            description: v.string(),
            quantity: v.number(),
            unitPrice: v.number(),
          })
        ),
        notes: v.optional(v.string()),
        terms: v.optional(v.string()),
        tax: v.optional(v.number()),
        discount: v.optional(v.number()),
        status: v.optional(v.string()),
        additionalFields: v.optional(v.any()), // Added to allow passing through additional fields
      })
    ),
  },
  handler: async (ctx, args) => {
    // Check if user has staff or admin permissions
    const { user } = await checkStaffOrAdminPermission(ctx);
    const results = {
      imported: 0,
      skipped: 0,
      updated: 0,
      errors: [] as string[],
      newFields: new Set<string>(), // Track any new fields encountered
    };
    
    // Process each invoice in the import
    for (const invoiceData of args.invoices) {
      try {
        // If additionalFields exists, track any new field names
        if (invoiceData.additionalFields && typeof invoiceData.additionalFields === 'object') {
          Object.keys(invoiceData.additionalFields).forEach(field => {
            results.newFields.add(field);
          });
        }

        const now = Date.now();
        
        // Calculate financial amounts
        const subtotal = invoiceData.items.reduce(
          (sum, item) => sum + item.quantity * item.unitPrice, 
          0
        );
        
        const taxAmount = invoiceData.tax ? subtotal * (invoiceData.tax / 100) : 0;
        const discountAmount = invoiceData.discount ? subtotal * (invoiceData.discount / 100) : 0;
        const total = subtotal + taxAmount - discountAmount;
        
        // Prepare the invoice data for insertion
        const invoiceRecord = {
          invoiceNumber: invoiceData.invoiceNumber,
          customerId: invoiceData.customerId,
          issueDate: new Date(invoiceData.issueDate).getTime(), // Convert string to timestamp
          dueDate: new Date(invoiceData.dueDate).getTime(), // Convert string to timestamp
          items: invoiceData.items,
          subtotal,
          taxRate: invoiceData.tax || 0, // Add the missing taxRate field
          taxAmount,
          discountAmount,
          total,
          notes: invoiceData.notes || "",
          terms: invoiceData.terms || "",
          status: invoiceData.status || "draft", // Default status
          createdAt: now,
          updatedAt: now,
          createdBy: user._id,
    };

        // Always insert as a new invoice
        await ctx.db.insert("invoices", invoiceRecord);
        results.imported++;
      } catch (error) {
        results.errors.push(`Error with invoice ${invoiceData.invoiceNumber}: ${error}`);
        results.skipped++;
      }
    }
    
    return {
      ...results,
      newFields: Array.from(results.newFields), // Convert Set to Array for JSON response
    };
  },
});

// Check import status and return missing invoices
export const checkImport = query({
  args: {
    invoices: v.array(
      v.object({
        id: v.optional(v.string()),
        invoiceNumber: v.string(),
      })
    ),
  },
  handler: async (ctx, args) => {
    // Check if user has staff or admin permissions
    await checkStaffOrAdminPermission(ctx);

    const missingInvoices = [];
    const existingInvoiceNumbers = new Set();

    // Get all existing invoice numbers (staff and admin can see all)
    const existingInvoices = await ctx.db
      .query("invoices")
      .collect();
    
    existingInvoices.forEach(invoice => {
      existingInvoiceNumbers.add(invoice.invoiceNumber);
    });
    
    // Check which invoices from the import are missing
    for (const invoice of args.invoices) {
      if (!invoice.invoiceNumber || !invoice.invoiceNumber.trim()) {
        // Skip invoices with empty invoice numbers
        continue;
      }

      if (!existingInvoiceNumbers.has(invoice.invoiceNumber)) {
        missingInvoices.push(invoice);
      }
    }
    
    return {
      existingCount: existingInvoices.length,
      missingCount: missingInvoices.length,
      missingInvoices: missingInvoices,
    };
  },
});

// Internal function to get invoice details for email
export const getForEmail = internalQuery({
  args: { invoiceId: v.id("invoices") },
  handler: async (ctx, args) => {
    const invoice = await ctx.db.get(args.invoiceId);
    if (!invoice) {
      throw new Error("Invoice not found");
    }

    // Get customer details
    const customer = await ctx.db.get(invoice.customerId);

    // Return invoice with customer data
    return {
      ...invoice,
      customer,
    };
  },
});

// Update invoice status with validation
export const updateStatus = mutation({
  args: {
    id: v.id("invoices"),
    status: v.string(),
  },
  handler: async (ctx, args) => {
    // Check if user has staff or admin permissions
    await checkStaffOrAdminPermission(ctx);

    // Get the invoice to check current status
    const invoice = await ctx.db.get(args.id);
    if (!invoice) {
      throw new Error("Invoice not found");
    }

    // Validate status values
    const validStatuses = ["draft", "sent", "paid", "overdue", "cancelled"];
    if (!validStatuses.includes(args.status)) {
      throw new Error(`Invalid status. Must be one of: ${validStatuses.join(", ")}`);
    }

    // Business logic for status transitions
    const currentStatus = invoice.status;
    const newStatus = args.status;

    // Prevent certain invalid transitions
    if (currentStatus === "paid" && newStatus !== "paid") {
      throw new Error("Cannot change status from 'paid' to another status");
    }

    // Check if invoice is overdue (past due date and not paid)
    const now = Date.now();
    const isOverdue = invoice.dueDate < now && currentStatus !== "paid" && currentStatus !== "cancelled";

    // If trying to set to 'sent' but invoice is overdue, suggest 'overdue' instead
    if (newStatus === "sent" && isOverdue) {
      throw new Error("This invoice is past its due date. Consider marking it as 'overdue' instead.");
    }

    // If marking as paid, set the paid date
    const updateData: any = {
      status: newStatus,
      updatedAt: Date.now(),
    };

    if (newStatus === "paid" && currentStatus !== "paid") {
      updateData.paidDate = Date.now();
    } else if (newStatus !== "paid" && currentStatus === "paid") {
      updateData.paidDate = undefined;
    }

    await ctx.db.patch(args.id, updateData);

    return { success: true, previousStatus: currentStatus, newStatus };
  },
});

// Get invoices for a specific customer (staff and admin can see all invoices)
export const getByCustomer = query({
  args: { customerId: v.id("customers") },
  handler: async (ctx, args) => {
    // Check if user has staff or admin permissions
    await checkStaffOrAdminPermission(ctx);

    // Staff and admin users can see all invoices for any customer
    return await ctx.db
      .query("invoices")
      .withIndex("by_customer", (q) => q.eq("customerId", args.customerId))
      .order("desc")
      .collect();
  },
});

// Check and update overdue invoices
export const checkOverdueInvoices = mutation({
  args: {},
  handler: async (ctx) => {
    // Check if user has staff or admin permissions
    await checkStaffOrAdminPermission(ctx);

    const now = Date.now();

    // Find all invoices that are past due and not paid/cancelled (staff and admin can update all)
    const invoices = await ctx.db
      .query("invoices")
      .collect();

    const overdueInvoices = invoices.filter(invoice =>
      invoice.dueDate < now &&
      invoice.status === "sent" // Only update sent invoices to overdue
    );

    let updatedCount = 0;
    for (const invoice of overdueInvoices) {
      await ctx.db.patch(invoice._id, {
        status: "overdue",
        updatedAt: now
      });
      updatedCount++;
    }

    return { updatedCount, totalChecked: invoices.length };
  },
});

// Internal function to mark invoice as email sent
export const markEmailSent = internalMutation({
  args: { invoiceId: v.id("invoices") },
  handler: async (ctx, args) => {
    const invoice = await ctx.db.get(args.invoiceId);
    if (!invoice) {
      throw new Error("Invoice not found");
    }

    // Update the invoice status if it's in draft
    if (invoice.status === "draft") {
      await ctx.db.patch(args.invoiceId, { status: "sent", updatedAt: Date.now() });
    }

    return true;
  },
});

// Generate PDF and store in Convex Storage
export const generateAndStorePDF = action({
  args: {
    invoiceId: v.id("invoices")
  },
  handler: async (ctx, args) => {
    try {
      // Validate input
      if (!args.invoiceId) {
        throw new Error("Invoice ID is required");
      }

      // Get invoice with customer data
      const invoiceWithCustomer = await ctx.runQuery(internal.invoices.getInvoiceWithCustomer, {
        invoiceId: args.invoiceId
      });

      if (!invoiceWithCustomer) {
        throw new Error("Invoice not found or you don't have permission to access it");
      }

      // Validate invoice data
      if (!invoiceWithCustomer.invoiceNumber) {
        throw new Error("Invoice number is missing");
      }

      if (!invoiceWithCustomer.items || invoiceWithCustomer.items.length === 0) {
        throw new Error("Invoice must have at least one item");
      }

      // Get company settings for PDF generation
      const settings = await ctx.runQuery(internal.settings.getForInvoice, {
        userId: invoiceWithCustomer.createdBy
      });

      // Validate required settings
      if (!settings) {
        throw new Error("Company settings not found. Please configure company settings first.");
      }

      // Server-side PDF generation is not supported with jsPDF
      // This function is deprecated and should not be used for actual PDF generation
      // Use client-side PDF generation with storePDFBlob action instead

      throw new Error(
        "Server-side PDF generation is not supported. " +
        "Please use client-side PDF generation with the existing PDF utilities. " +
        "Generate the PDF on the client using generateInvoicePDFBlob() and then " +
        "store it using the storePDFBlob action."
      );



    } catch (error) {
      console.error("PDF generation error:", error);

      // Provide more specific error messages
      let errorMessage = "Failed to generate PDF";
      if (error instanceof Error) {
        errorMessage = error.message;
      }

      throw new Error(errorMessage);
    }
  },
});

// Store PDF blob in Convex Storage (accepts PDF blob from client)
export const storePDFBlob = action({
  args: {
    invoiceId: v.id("invoices"),
    pdfBlob: v.any(), // PDF blob data from client (ArrayBuffer)
  },
  handler: async (ctx, args) => {
    try {
      // Validate input
      if (!args.invoiceId) {
        throw new Error("Invoice ID is required");
      }

      if (!args.pdfBlob) {
        throw new Error("PDF blob is required");
      }

      // Get invoice to validate it exists and user has permission
      const invoice = await ctx.runQuery(internal.invoices.getInvoiceWithCustomer, {
        invoiceId: args.invoiceId
      });

      if (!invoice) {
        throw new Error("Invoice not found or you don't have permission to access it");
      }

      // Create blob with proper PDF MIME type
      const pdfBlob = new Blob([args.pdfBlob], { type: 'application/pdf' });

      // Validate blob size (should be reasonable)
      if (pdfBlob.size === 0) {
        throw new Error("Generated PDF blob is empty");
      }

      if (pdfBlob.size > 10 * 1024 * 1024) { // 10MB limit
        throw new Error("Generated PDF is too large (>10MB)");
      }

      // Store the PDF in Convex Storage
      const storageId = await ctx.storage.store(pdfBlob);

      if (!storageId) {
        throw new Error("Failed to store PDF in storage");
      }

      // Update the invoice with the PDF storage ID
      await ctx.runMutation(internal.invoices.updatePDFStorage, {
        invoiceId: args.invoiceId,
        pdfStorageId: storageId,
      });

      return {
        success: true,
        storageId,
        message: "PDF stored successfully"
      };

    } catch (error) {
      console.error("PDF storage error:", error);

      // Provide more specific error messages
      let errorMessage = "Failed to store PDF";
      if (error instanceof Error) {
        errorMessage = error.message;
      }

      throw new Error(errorMessage);
    }
  },
});



// Internal mutation to update invoice with PDF storage ID
export const updatePDFStorage = internalMutation({
  args: {
    invoiceId: v.id("invoices"),
    pdfStorageId: v.string(),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.invoiceId, {
      pdfStorageId: args.pdfStorageId,
      updatedAt: Date.now(),
    });
  },
});

// Internal query to get invoice with customer data for PDF generation
export const getInvoiceWithCustomer = internalQuery({
  args: { invoiceId: v.id("invoices") },
  handler: async (ctx, args) => {
    const invoice = await ctx.db.get(args.invoiceId);
    if (!invoice) {
      return null;
    }

    // Get customer details
    const customer = await ctx.db.get(invoice.customerId);

    return {
      ...invoice,
      customer,
    };
  },
});

// Clean up old PDF storage for an invoice
export const cleanupOldPDF = mutation({
  args: { invoiceId: v.id("invoices") },
  handler: async (ctx, args) => {
    // Check if user has staff or admin permissions
    const { user } = await checkStaffOrAdminPermission(ctx);

    const invoice = await ctx.db.get(args.invoiceId);
    if (!invoice) {
      throw new Error("Invoice not found");
    }

    // Check if user has permission to modify this invoice
    if (invoice.createdBy !== user._id && user.role !== "admin") {
      throw new Error("You don't have permission to modify this invoice");
    }

    // Delete old PDF from storage if it exists
    if (invoice.pdfStorageId) {
      try {
        await ctx.storage.delete(invoice.pdfStorageId);
        console.log(`Deleted old PDF storage: ${invoice.pdfStorageId}`);
      } catch (error) {
        console.error("Error deleting old PDF from storage:", error);
        // Don't throw error, just log it - we still want to clear the reference
      }

      // Clear the PDF storage ID from the invoice
      await ctx.db.patch(args.invoiceId, {
        pdfStorageId: undefined,
        updatedAt: Date.now(),
      });
    }

    return { success: true, message: "Old PDF cleaned up successfully" };
  },
});

// Bulk cleanup of orphaned PDF files (admin only)
export const cleanupOrphanedPDFs = mutation({
  args: {},
  handler: async (ctx) => {
    // Check if user has admin permissions
    const { user } = await checkAdminPermission(ctx);

    let cleanedCount = 0;
    let errorCount = 0;

    try {
      // Get all invoices with PDF storage IDs
      const invoicesWithPDFs = await ctx.db
        .query("invoices")
        .filter((q) => q.neq(q.field("pdfStorageId"), undefined))
        .collect();

      console.log(`Found ${invoicesWithPDFs.length} invoices with PDFs`);

      // Check each PDF storage ID and clean up if file doesn't exist
      for (const invoice of invoicesWithPDFs) {
        if (invoice.pdfStorageId) {
          try {
            // Try to get the URL to see if it exists (storage.get doesn't exist in mutations)
            const url = await ctx.storage.getUrl(invoice.pdfStorageId);
            if (!url) {
              // File doesn't exist, clear the reference
              await ctx.db.patch(invoice._id, {
                pdfStorageId: undefined,
                updatedAt: Date.now(),
              });
              cleanedCount++;
              console.log(`Cleaned orphaned PDF reference for invoice ${invoice.invoiceNumber}`);
            }
          } catch (error) {
            console.error(`Error checking PDF for invoice ${invoice.invoiceNumber}:`, error);
            // If we can't get URL, assume file doesn't exist and clean up reference
            await ctx.db.patch(invoice._id, {
              pdfStorageId: undefined,
              updatedAt: Date.now(),
            });
            cleanedCount++;
          }
        }
      }

      return {
        success: true,
        message: `Cleanup completed. Cleaned ${cleanedCount} orphaned references. ${errorCount} errors encountered.`,
        cleanedCount,
        errorCount
      };

    } catch (error) {
      console.error("Bulk PDF cleanup error:", error);
      throw new Error("Failed to perform bulk PDF cleanup");
    }
  },
});

// Get PDF URL for an invoice
export const getPDFUrl = query({
  args: {
    invoiceId: v.id("invoices"),
  },
  handler: async (ctx, args) => {
    // Validate input
    if (!args.invoiceId) {
      throw new Error("Invoice ID is required");
    }

    // Check if user has staff or admin permissions
    await checkStaffOrAdminPermission(ctx);

    // Get the invoice
    const invoice = await ctx.db.get(args.invoiceId);
    if (!invoice) {
      // Return null instead of throwing error to handle deleted invoices gracefully
      return null;
    }

    // Check if PDF exists
    if (!invoice.pdfStorageId) {
      return null;
    }

    try {
      // Validate storage ID format
      if (typeof invoice.pdfStorageId !== 'string' || invoice.pdfStorageId.trim().length === 0) {
        console.error("Invalid PDF storage ID format");
        return null;
      }

      // Get the PDF URL from storage
      const pdfUrl = await ctx.storage.getUrl(invoice.pdfStorageId);

      // Validate URL
      if (pdfUrl && !pdfUrl.startsWith('http')) {
        console.error("Invalid PDF URL format");
        return null;
      }

      return pdfUrl;
    } catch (error) {
      console.error("Failed to get PDF URL:", error);
      return null;
    }
  },
});

// Check if invoice has a stored PDF
export const hasPDF = query({
  args: {
    invoiceId: v.id("invoices"),
  },
  handler: async (ctx, args) => {
    // Check if user has staff or admin permissions
    await checkStaffOrAdminPermission(ctx);

    // Get the invoice
    const invoice = await ctx.db.get(args.invoiceId);
    if (!invoice) {
      return false;
    }

    return Boolean(invoice.pdfStorageId);
  },
});

// Delete stored PDF for an invoice
export const deletePDF = mutation({
  args: {
    invoiceId: v.id("invoices"),
  },
  handler: async (ctx, args) => {
    // Check if user has staff or admin permissions
    await checkStaffOrAdminPermission(ctx);

    // Get the invoice
    const invoice = await ctx.db.get(args.invoiceId);
    if (!invoice) {
      throw new Error("Invoice not found");
    }

    // Check if PDF exists
    if (!invoice.pdfStorageId) {
      return { success: true, message: "No PDF to delete" };
    }

    try {
      // Delete the PDF from storage
      await ctx.storage.delete(invoice.pdfStorageId);

      // Remove the PDF storage ID from the invoice
      await ctx.db.patch(args.invoiceId, {
        pdfStorageId: undefined,
        updatedAt: Date.now(),
      });

      return { success: true, message: "PDF deleted successfully" };
    } catch (error) {
      console.error("Failed to delete PDF:", error);
      throw new Error("Failed to delete PDF");
    }
  },
});
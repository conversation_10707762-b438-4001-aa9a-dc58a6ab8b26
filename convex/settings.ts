import { v } from "convex/values";
import { query, mutation, internalMutation, internalQuery } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";
import { internal } from "./_generated/api";
import { Id } from "./_generated/dataModel";

// Get current user helper
async function getCurrentUser(ctx: any) {
  const userId = await getAuthUserId(ctx);
  if (!userId) throw new Error("Not authenticated");
  return userId;
}

// Check if user has admin role
async function checkAdmin(ctx: any, userId: string) {
  const user = await ctx.db.get(userId);
  if (!user || (user.role !== "admin" && user.role !== "master")) {
    throw new Error("Unauthorized - Admin access required");
  }
}

// Get company settings
export const get = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getCurrentUser(ctx);
    
    // Get settings for the current user's organization
    const settings = await ctx.db
      .query("settings")
      .withIndex("by_created_by", (q) => q.eq("createdBy", userId))
      .first();
    
    return settings || {
      companyName: "HVAC CRM",
      logoUrl: "",
      contactEmail: "",
      contactPhone: "",
      address: "",
      city: "",
      state: "",
      zipCode: "",
      termsUrl: "",
      privacyPolicyUrl: "",
      invoiceFooter: "",
      taxId: "",
      paymentTerms: "Net 30",
    };
  },
});

// Get settings history
export const getHistory = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getCurrentUser(ctx);
    await checkAdmin(ctx, userId);
    
    const settings = await ctx.db
      .query("settings")
      .withIndex("by_created_by", (q) => q.eq("createdBy", userId))
      .first();

    if (!settings) return [];

    return await ctx.db
      .query("settingsHistory")
      .withIndex("by_settings", (q) => q.eq("settingsId", settings._id))
      .order("desc")
      .collect();
  },
});

// Upload logo to Convex storage
export const uploadLogo = mutation({
  args: {
    file: v.optional(v.any()),
  },
  handler: async (ctx, { file }) => {
    const userId = await getCurrentUser(ctx);
    await checkAdmin(ctx, userId);
    
    try {
      // Generate an upload URL for the file
      const uploadUrl = await ctx.storage.generateUploadUrl();

      // Return the upload URL to the client
      // The client will then upload the file and get back a storageId
      // That storageId will be used as the logoUrl in the settings
      return uploadUrl;
    } catch (error) {
      console.error("Logo upload error:", error);
      throw new Error("Failed to generate upload URL: " + (error instanceof Error ? error.message : "Unknown error"));
    }
  },
});

export const update = mutation({
  args: {
    companyName: v.string(),
    logoUrl: v.optional(v.string()),
    contactEmail: v.string(),
    contactPhone: v.string(),
    address: v.string(),
    city: v.string(),
    state: v.string(),
    zipCode: v.string(),
    termsUrl: v.optional(v.string()),
    privacyPolicyUrl: v.optional(v.string()),
    invoiceFooter: v.optional(v.string()),
    taxId: v.optional(v.string()),
    paymentTerms: v.string(),
  },
  handler: async (ctx, args) => {
    const userId = await getCurrentUser(ctx);
    await checkAdmin(ctx, userId);
    
    // Get current settings
    const existingSettings = await ctx.db
      .query("settings")
      .withIndex("by_created_by", (q) => q.eq("createdBy", userId))
      .first();

    if (existingSettings) {
      // Create history record before updating
      await ctx.db.insert("settingsHistory", {
        settingsId: existingSettings._id,
        version: existingSettings._creationTime,
        data: existingSettings,
        changedBy: userId,
        changedAt: Date.now(),
      });
      
      // Update existing settings
      await ctx.db.patch(existingSettings._id, args);
    } else {
      // Create new settings
      await ctx.db.insert("settings", {
        ...args,
        createdBy: userId,
      });
    }
  },
});

// Internal query to get settings for invoices
export const getForInvoice = internalQuery({
  args: { userId: v.id("users") },
  handler: async (ctx: any, args: { userId: string }) => {
    return await ctx.db
      .query("settings")
      .withIndex("by_created_by", (q: any) => q.eq("createdBy", args.userId))
      .first();
  },
});

// Get URL for a logo storage ID
export const getLogoUrl = query({
  args: {
    storageId: v.string(),
  },
  handler: async (ctx, { storageId }) => {
    try {
      return await ctx.storage.getUrl(storageId);
    } catch (error) {
      console.error("Failed to get logo URL:", error);
      return null;
    }
  },
});
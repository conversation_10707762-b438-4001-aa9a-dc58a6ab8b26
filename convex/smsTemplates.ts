import { v } from "convex/values";
import { query, mutation } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";
import { validateSMSTemplate, validateSMSPermissions, validateTemplateVariables } from "./sms/validation";

// Get current user helper
async function getCurrentUser(ctx: any) {
  const userId = await getAuthUserId(ctx);
  if (!userId) throw new Error("Not authenticated");
  return userId;
}

// Check if user has admin role
async function checkAdmin(ctx: any, userId: string) {
  const user = await ctx.db.get(userId);
  if (!user || (user.role !== "admin" && user.role !== "master")) {
    throw new Error("Unauthorized - Admin access required");
  }
}

// Get all SMS templates (staff and admin access for viewing)
export const list = query({
  args: {
    category: v.optional(v.string()),
    activeOnly: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const userId = await getCurrentUser(ctx);
    // Allow staff and admin users to view templates (for conversations)
    const user = await ctx.db.get(userId);
    if (!user || (user.role !== "staff" && user.role !== "admin")) {
      throw new Error("Unauthorized - Staff or Admin access required");
    }

    if (args.category !== undefined) {
      const category = args.category; // TypeScript now knows this is string
      const query = ctx.db
        .query("smsTemplates")
        .withIndex("by_category", (q) => q.eq("category", category))
        .order("desc");
      return await query.collect();
    } else if (args.activeOnly) {
      const query = ctx.db
        .query("smsTemplates")
        .withIndex("by_active", (q) => q.eq("isActive", true))
        .order("desc");
      return await query.collect();
    } else {
      const query = ctx.db
        .query("smsTemplates")
        .order("desc");
      return await query.collect();
    }

  },
});

// Get SMS template by ID (admin only)
export const getById = query({
  args: { id: v.id("smsTemplates") },
  handler: async (ctx, args) => {
    const userId = await getCurrentUser(ctx);
    await checkAdmin(ctx, userId);
    return await ctx.db.get(args.id);
  },
});

// Create SMS template (admin only)
export const create = mutation({
  args: {
    name: v.string(),
    content: v.string(),
    category: v.string(),
    description: v.optional(v.string()),
    variables: v.array(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getCurrentUser(ctx);
    await checkAdmin(ctx, userId);

    // Validate template
    const validation = validateSMSTemplate(args);
    if (!validation.isValid) {
      throw new Error(validation.error);
    }

    // Validate template variables
    const availableVariables = getAvailableVariablesList();
    const variableValidation = validateTemplateVariables(args.content, availableVariables);
    if (!variableValidation.isValid) {
      throw new Error(variableValidation.error);
    }

    const templateId = await ctx.db.insert("smsTemplates", {
      name: args.name,
      content: args.content,
      category: args.category,
      description: args.description,
      variables: args.variables,
      isActive: true,
      createdBy: userId,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });

    return templateId;
  },
});

// Update SMS template (admin only)
export const update = mutation({
  args: {
    id: v.id("smsTemplates"),
    name: v.optional(v.string()),
    content: v.optional(v.string()),
    category: v.optional(v.string()),
    description: v.optional(v.string()),
    variables: v.optional(v.array(v.string())),
    isActive: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const userId = await getCurrentUser(ctx);
    await checkAdmin(ctx, userId);

    const template = await ctx.db.get(args.id);
    if (!template) {
      throw new Error("SMS template not found.");
    }

    // Prepare update data
    const updateData: any = {
      updatedAt: Date.now(),
    };

    if (args.name) updateData.name = args.name;
    if (args.content) updateData.content = args.content;
    if (args.category) updateData.category = args.category;
    if (args.description !== undefined) updateData.description = args.description;
    if (args.variables) updateData.variables = args.variables;
    if (args.isActive !== undefined) updateData.isActive = args.isActive;

    // Validate updated template
    const updatedTemplate = { ...template, ...updateData };
    const validation = validateSMSTemplate(updatedTemplate);
    if (!validation.isValid) {
      throw new Error(validation.error);
    }

    // Validate template variables if content is being updated
    if (args.content) {
      const availableVariables = getAvailableVariablesList();
      const variableValidation = validateTemplateVariables(args.content, availableVariables);
      if (!variableValidation.isValid) {
        throw new Error(variableValidation.error);
      }
    }

    await ctx.db.patch(args.id, updateData);
    return args.id;
  },
});

// Delete SMS template (admin only)
export const remove = mutation({
  args: { id: v.id("smsTemplates") },
  handler: async (ctx, args) => {
    const userId = await getCurrentUser(ctx);
    await checkAdmin(ctx, userId);

    const template = await ctx.db.get(args.id);
    if (!template) {
      throw new Error("SMS template not found.");
    }

    await ctx.db.delete(args.id);
    return true;
  },
});

// Toggle template active status (admin only)
export const toggleActive = mutation({
  args: { 
    id: v.id("smsTemplates"),
    isActive: v.boolean(),
  },
  handler: async (ctx, args) => {
    const userId = await getCurrentUser(ctx);
    await checkAdmin(ctx, userId);

    const template = await ctx.db.get(args.id);
    if (!template) {
      throw new Error("SMS template not found.");
    }

    await ctx.db.patch(args.id, {
      isActive: args.isActive,
      updatedAt: Date.now(),
    });

    return true;
  },
});

// Get templates by category (admin only)
export const getByCategory = query({
  args: { category: v.string() },
  handler: async (ctx, args) => {
    const userId = await getCurrentUser(ctx);
    await checkAdmin(ctx, userId);

    return await ctx.db
      .query("smsTemplates")
      .withIndex("by_category", (q) => q.eq("category", args.category))
      .filter((q) => q.eq(q.field("isActive"), true))
      .order("desc")
      .collect();
  },
});

// Get available template variables (admin only)
export const getAvailableVariables = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getCurrentUser(ctx);
    await checkAdmin(ctx, userId);
    return getAvailableVariablesList();
  },
});

// Initialize default templates (admin only)
export const initializeDefaults = mutation({
  args: {},
  handler: async (ctx) => {
    const userId = await getCurrentUser(ctx);
    await checkAdmin(ctx, userId);

    const defaultTemplates = getDefaultTemplates();
    const createdTemplates = [];

    for (const template of defaultTemplates) {
      // Check if template already exists
      const existing = await ctx.db
        .query("smsTemplates")
        .filter((q) => q.eq(q.field("name"), template.name))
        .first();

      if (!existing) {
        const templateId = await ctx.db.insert("smsTemplates", {
          ...template,
          createdBy: userId,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
        createdTemplates.push(templateId);
      }
    }

    return createdTemplates;
  },
});

// Helper function to get available variables
function getAvailableVariablesList(): string[] {
  return [
    "customerName",
    "companyName",
    "customerPhone",
    "customerEmail",
    "customerAddress",
    "appointmentDate",
    "appointmentTime",
    "jobTitle",
    "jobDescription",
    "technicianName",
    "invoiceNumber",
    "invoiceAmount",
    "dueDate",
    "companyPhone",
    "companyEmail",
    "serviceDate",
    "estimateAmount",
  ];
}

// Helper function to get default templates
function getDefaultTemplates() {
  return [
    {
      name: "Appointment Reminder",
      content: "Hi {customerName}, this is a reminder that your HVAC service appointment is scheduled for {appointmentDate} at {appointmentTime}. Our technician {technicianName} will be there. Please call {companyPhone} if you need to reschedule.",
      category: "appointment",
      description: "Reminder for upcoming service appointments",
      variables: ["customerName", "appointmentDate", "appointmentTime", "technicianName", "companyPhone"],
      isActive: true,
    },
    {
      name: "Service Complete",
      content: "Hello {customerName}, your HVAC service at {customerAddress} has been completed. Thank you for choosing our services! If you have any questions, please call {companyPhone}.",
      category: "service",
      description: "Notification when service is completed",
      variables: ["customerName", "customerAddress", "companyPhone"],
      isActive: true,
    },
    {
      name: "Invoice Sent",
      content: "Hi {customerName}, your invoice #{invoiceNumber} for ${invoiceAmount} has been sent to {customerEmail}. Payment is due by {dueDate}. Thank you for your business!",
      category: "invoice",
      description: "Notification when invoice is sent",
      variables: ["customerName", "invoiceNumber", "invoiceAmount", "customerEmail", "dueDate"],
      isActive: true,
    },
    {
      name: "Payment Reminder",
      content: "Hello {customerName}, this is a friendly reminder that invoice #{invoiceNumber} for ${invoiceAmount} is due on {dueDate}. Please contact us at {companyPhone} if you have any questions.",
      category: "invoice",
      description: "Reminder for overdue payments",
      variables: ["customerName", "invoiceNumber", "invoiceAmount", "dueDate", "companyPhone"],
      isActive: true,
    },
    {
      name: "Emergency Service",
      content: "Hi {customerName}, we've received your emergency HVAC service request. Our technician {technicianName} is on the way and will arrive within the next hour. Emergency contact: {companyPhone}",
      category: "service",
      description: "Emergency service dispatch notification",
      variables: ["customerName", "technicianName", "companyPhone"],
      isActive: true,
    },
    {
      name: "Estimate Ready",
      content: "Hello {customerName}, your HVAC service estimate is ready! The estimated cost is ${estimateAmount}. Please call {companyPhone} to discuss and schedule your service.",
      category: "general",
      description: "Notification when estimate is ready",
      variables: ["customerName", "estimateAmount", "companyPhone"],
      isActive: true,
    },
  ];
}

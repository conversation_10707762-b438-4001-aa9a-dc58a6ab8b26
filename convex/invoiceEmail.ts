import { v } from "convex/values";
import { action, internalMutation } from "./_generated/server";
import { internal } from "./_generated/api";
import { generateNotificationEmail, generateInvoiceHTML } from "./email/templates";
import {
  validateInvoiceForEmail,
  validateCompanySettings,
  validateResendConfig,
  validateFromEmail
} from "./email/validation";
import {
  createResendClient,
  getEmailConfig,
  sendEmail,
  generateEmailSubject,
  logEmailOperation,
  validateEmailContent,
  replaceTemplateVariables,
  generateTemplateVariables
} from "./email/service";

// Send invoice via email with enhanced error handling and validation
export const sendInvoiceEmail = action({
  args: { invoiceId: v.id("invoices") },
  handler: async (ctx: any, args: { invoiceId: string }) => {
    logEmailOperation("start", args.invoiceId, { timestamp: Date.now() });

    try {
      // Validate input
      if (!args.invoiceId) {
        throw new Error("Invoice ID is required");
      }

      // Get invoice details
      const invoice: any = await ctx.runQuery(internal.invoices.getForEmail, {
        invoiceId: args.invoiceId,
      });

      // Validate invoice and customer data
      const invoiceValidation = validateInvoiceForEmail(invoice, args.invoiceId);
      if (!invoiceValidation.isValid) {
        throw new Error(invoiceValidation.error);
      }

      logEmailOperation("invoice-validated", args.invoiceId, {
        invoiceNumber: invoice.invoiceNumber,
        customerName: invoice.customer?.name,
        customerEmail: invoice.customer?.email
      });

      // Get and validate company settings
      const settings: any = await ctx.runQuery(internal.settings.getForInvoice, {
        userId: invoice.createdBy,
      });

      const settingsValidation = validateCompanySettings(settings);
      if (!settingsValidation.isValid) {
        throw new Error(settingsValidation.error);
      }

      // Validate Resend configuration
      const resendValidation = validateResendConfig();
      if (!resendValidation.isValid) {
        throw new Error(resendValidation.error);
      }

      // Validate from email configuration
      const fromEmailValidation = validateFromEmail();
      if (!fromEmailValidation.isValid) {
        throw new Error(fromEmailValidation.error);
      }

      // Create branded invoice data
      const brandedInvoice = {
        ...invoice,
        company: {
          name: settings.companyName,
          logoUrl: settings.logoUrl,
          contactEmail: settings.contactEmail,
          contactPhone: settings.contactPhone,
          address: settings.address,
          city: settings.city,
          state: settings.state,
          zipCode: settings.zipCode,
          taxId: settings.taxId,
          paymentTerms: settings.paymentTerms,
        },
        footer: settings.invoiceFooter,
      };

      // Generate invoice HTML and store it
      const invoiceHtml = generateInvoiceHTML(brandedInvoice);
      const htmlBlob = new Blob([invoiceHtml], { type: 'text/html' });
      const htmlStorageId = await ctx.storage.store(htmlBlob);
      const invoiceUrl = await ctx.storage.getUrl(htmlStorageId);

      logEmailOperation("html-generated", args.invoiceId, {
        storageId: htmlStorageId,
        invoiceUrl: invoiceUrl.substring(0, 50) + "..."
      });

      // Store the storage ID in the invoice record
      await ctx.runMutation(internal.invoiceEmail.updateInvoiceStorage, {
        invoiceId: args.invoiceId,
        storageId: htmlStorageId,
      });

      // Try to get custom email template, fallback to default
      let emailSubject: string;
      let notificationHtml: string;

      try {
        const emailTemplate = await ctx.runQuery(internal.emailTemplates.getByCategoryInternal, {
          category: "invoice"
        });

        if (emailTemplate) {
          // Use custom template
          const templateVariables = generateTemplateVariables(brandedInvoice, settings, invoiceUrl);
          emailSubject = replaceTemplateVariables(emailTemplate.subject, templateVariables);
          notificationHtml = replaceTemplateVariables(emailTemplate.content, templateVariables);

          logEmailOperation("custom-template-used", args.invoiceId, {
            templateId: emailTemplate._id,
            templateName: emailTemplate.name
          });
        } else {
          // Fallback to default template
          emailSubject = generateEmailSubject(invoice.invoiceNumber, settings.companyName);
          notificationHtml = generateNotificationEmail(brandedInvoice, invoiceUrl);

          logEmailOperation("default-template-used", args.invoiceId, {
            reason: "no-custom-template-found"
          });
        }
      } catch (error) {
        // Fallback to default template on any error
        emailSubject = generateEmailSubject(invoice.invoiceNumber, settings.companyName);
        notificationHtml = generateNotificationEmail(brandedInvoice, invoiceUrl);

        logEmailOperation("default-template-fallback", args.invoiceId, {
          error: error instanceof Error ? error.message : "Unknown error"
        });
      }

      // Get email configuration and create Resend client
      const emailConfig = getEmailConfig(settings);
      const resend = createResendClient(emailConfig.apiKey);

      // Validate content
      const contentValidation = validateEmailContent(emailSubject, notificationHtml);
      if (!contentValidation.isValid) {
        throw new Error(contentValidation.error);
      }

      // Send email using the service module
      const emailResult = await sendEmail(
        resend,
        emailConfig,
        invoice.customer.email,
        emailSubject,
        notificationHtml
      );

      if (!emailResult.success) {
        throw new Error(emailResult.error);
      }

      logEmailOperation("email-sent", args.invoiceId, {
        emailId: emailResult.emailId,
        customerEmail: invoice.customer.email,
        subject: emailSubject
      });

      // Mark invoice as sent
      try {
        await ctx.runMutation(internal.invoices.markEmailSent, {
          invoiceId: args.invoiceId,
        });
        logEmailOperation("invoice-marked-sent", args.invoiceId, {});
      } catch (markError) {
        console.error("Error marking invoice as sent:", markError);
        // Don't fail the operation if this update fails, email was sent successfully
      }

      const successMessage = `Invoice ${invoice.invoiceNumber} sent successfully to ${invoice.customer.email}`;
      logEmailOperation("complete", args.invoiceId, {
        success: true,
        emailId: emailResult.emailId,
        message: successMessage
      });

      return {
        success: true,
        emailId: emailResult.emailId,
        message: successMessage,
        customerName: invoice.customer.name,
        customerEmail: invoice.customer.email,
        invoiceNumber: invoice.invoiceNumber,
        invoiceUrl: invoiceUrl,
      };

    } catch (error: any) {
      logEmailOperation("error", args.invoiceId, {
        error: error.message,
        stack: error.stack
      });

      // Re-throw with enhanced error message for user
      const userFriendlyMessage = error.message || "An unexpected error occurred while sending the invoice email.";
      throw new Error(userFriendlyMessage);
    }
  }
});

// Internal mutation to update invoice with storage ID
export const updateInvoiceStorage = internalMutation({
  args: {
    invoiceId: v.id("invoices"),
    storageId: v.string(),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.invoiceId, {
      htmlStorageId: args.storageId,
    });
  },
});
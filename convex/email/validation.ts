// Email validation utilities
// Separated from main email service for better organization

export interface ValidationResult {
  isValid: boolean;
  error?: string;
}

// Validate email format
export function validateEmailFormat(email: string): ValidationResult {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  
  if (!email || email.trim().length === 0) {
    return { isValid: false, error: "Email address is required" };
  }
  
  if (!emailRegex.test(email)) {
    return { isValid: false, error: `Invalid email format: ${email}` };
  }
  
  return { isValid: true };
}

// Validate invoice data for email sending
export function validateInvoiceForEmail(invoice: any, invoiceId: string): ValidationResult {
  if (!invoice) {
    return { isValid: false, error: "Invoice not found" };
  }

  if (!invoice.customer) {
    return { isValid: false, error: "No customer associated with this invoice" };
  }

  if (!invoice.customer.email) {
    return { 
      isValid: false, 
      error: `Customer email not found for ${invoice.customer.name}. Please update the customer's email address before sending the invoice.` 
    };
  }

  const emailValidation = validateEmailFormat(invoice.customer.email);
  if (!emailValidation.isValid) {
    return { 
      isValid: false, 
      error: `${emailValidation.error}. Please update the customer's email address.` 
    };
  }

  return { isValid: true };
}

// Validate company settings for email sending
export function validateCompanySettings(settings: any): ValidationResult {
  if (!settings) {
    return { 
      isValid: false, 
      error: "Company settings not found. Please configure your company settings before sending invoices." 
    };
  }

  if (!settings.companyName) {
    return { 
      isValid: false, 
      error: "Company name is required. Please update your company settings." 
    };
  }

  return { isValid: true };
}

// Validate Resend API configuration
export function validateResendConfig(): ValidationResult {
  const apiKey = process.env.CONVEX_RESEND_API_KEY || process.env.RESEND_API_KEY;
  
  if (!apiKey) {
    return { 
      isValid: false, 
      error: "Email service not configured. Please set CONVEX_RESEND_API_KEY environment variable." 
    };
  }

  if (!apiKey.startsWith('re_')) {
    return { 
      isValid: false, 
      error: "Invalid Resend API key format. API key should start with 're_'" 
    };
  }

  return { isValid: true };
}

// Validate from email configuration
export function validateFromEmail(): ValidationResult {
  const fromEmail = process.env.FROM_EMAIL || process.env.RESEND_FROM_EMAIL || '<EMAIL>';
  
  const emailValidation = validateEmailFormat(fromEmail);
  if (!emailValidation.isValid) {
    return { 
      isValid: false, 
      error: `Invalid from email format: ${fromEmail}. Please update FROM_EMAIL environment variable.` 
    };
  }

  return { isValid: true };
}

// Parse Resend API errors into user-friendly messages
export function parseResendError(error: any): string {
  const errorMessage = error.message || '';
  
  if (errorMessage.includes('Invalid from address')) {
    const fromEmail = process.env.FROM_EMAIL || process.env.RESEND_FROM_EMAIL || '<EMAIL>';
    return `Invalid from email address: ${fromEmail}. Please verify your domain is configured in Resend.`;
  }
  
  if (errorMessage.includes('Invalid to address')) {
    return `Invalid recipient email address. Please update the customer's email.`;
  }
  
  if (errorMessage.includes('Domain not verified')) {
    const fromEmail = process.env.FROM_EMAIL || process.env.RESEND_FROM_EMAIL || '<EMAIL>';
    const domain = fromEmail.split('@')[1];
    return `Email domain not verified in Resend. Please verify ${domain} in your Resend dashboard.`;
  }
  
  if (errorMessage.includes('API key')) {
    return "Invalid Resend API key. Please check your CONVEX_RESEND_API_KEY configuration.";
  }
  
  return `Email delivery failed: ${errorMessage || 'Unknown error'}. Please try again or contact support.`;
}

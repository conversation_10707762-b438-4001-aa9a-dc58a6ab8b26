// Email service utilities
// Separated from main email service for better organization

import { Resend } from "resend";
import { parseResendError } from "./validation";

export interface EmailConfig {
  apiKey: string;
  fromEmail: string;
  fromName: string;
  replyTo?: string;
}

export interface EmailResult {
  success: boolean;
  emailId?: string;
  error?: string;
}

// Initialize Resend client with configuration
export function createResendClient(apiKey: string): Resend {
  return new Resend(apiKey);
}

// Get email configuration from environment and settings
export function getEmailConfig(settings: any): EmailConfig {
  const apiKey = process.env.CONVEX_RESEND_API_KEY || process.env.RESEND_API_KEY;
  const fromEmail = process.env.FROM_EMAIL || process.env.RESEND_FROM_EMAIL || '<EMAIL>';
  const fromName = process.env.FROM_NAME || settings.companyName || 'Invoice System';
  
  return {
    apiKey: apiKey!,
    fromEmail,
    fromName,
    replyTo: settings.contactEmail || fromEmail
  };
}

// Send email using Resend API
export async function sendEmail(
  resend: Resend,
  config: EmailConfig,
  to: string,
  subject: string,
  html: string
): Promise<EmailResult> {
  try {
    const fromAddress = `${config.fromName} <${config.fromEmail}>`;
    
    console.log(`Sending email:`);
    console.log(`- To: ${to}`);
    console.log(`- From: ${fromAddress}`);
    console.log(`- Subject: ${subject}`);
    
    const result = await resend.emails.send({
      from: fromAddress,
      to: [to],
      subject,
      html,
      replyTo: config.replyTo,
    });

    if (result.error) {
      console.error("Resend API error:", result.error);
      return {
        success: false,
        error: parseResendError(result.error)
      };
    }

    if (!result.data?.id) {
      console.error("Email sent but no ID returned:", result);
      return {
        success: false,
        error: "Email may not have been sent properly. Please check with the recipient."
      };
    }

    console.log(`Email sent successfully. Resend ID: ${result.data.id}`);
    
    return {
      success: true,
      emailId: result.data.id
    };

  } catch (error: any) {
    console.error("Email sending error:", error);
    return {
      success: false,
      error: parseResendError(error)
    };
  }
}

// Generate email subject line
export function generateEmailSubject(invoiceNumber: string, companyName: string): string {
  return `Invoice ${invoiceNumber} from ${companyName}`;
}

// Replace template variables with actual values
export function replaceTemplateVariables(content: string, variables: Record<string, string>): string {
  let result = content;

  Object.entries(variables).forEach(([key, value]) => {
    // Handle both {key} and key formats
    const bracketPattern = new RegExp(`\\{${key}\\}`, 'g');
    const plainPattern = new RegExp(`\\{${key.replace(/[{}]/g, '')}\\}`, 'g');

    result = result.replace(bracketPattern, value || '');
    result = result.replace(plainPattern, value || '');
  });

  return result;
}

// Generate template variables from invoice and company data
export function generateTemplateVariables(invoice: any, settings: any, invoiceUrl?: string, pdfOptions?: {
  downloadUrl?: string;
  hasAttachment?: boolean;
  downloadInstructions?: string;
}): Record<string, string> {
  const formatCurrency = (amount: number): string =>
    `$${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;

  const formatDate = (timestamp: number): string =>
    new Date(timestamp).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

  return {
    // Customer variables
    customerName: invoice.customer?.name || '',
    customerEmail: invoice.customer?.email || '',
    customerPhone: invoice.customer?.phone || '',
    customerAddress: invoice.customer?.address || '',
    customerCity: invoice.customer?.city || '',
    customerState: invoice.customer?.state || '',
    customerZipCode: invoice.customer?.zipCode || '',
    customerCompany: invoice.customer?.company || '',

    // Invoice variables
    invoiceNumber: invoice.invoiceNumber || '',
    invoiceDate: invoice.issueDate ? formatDate(invoice.issueDate) : '',
    dueDate: invoice.dueDate ? formatDate(invoice.dueDate) : '',
    subtotal: formatCurrency(invoice.subtotal || 0),
    taxAmount: formatCurrency(invoice.taxAmount || 0),
    total: formatCurrency(invoice.total || 0),
    notes: invoice.notes || '',

    // Company variables
    companyName: settings.companyName || '',
    companyEmail: settings.contactEmail || '',
    companyPhone: settings.contactPhone || '',
    companyAddress: settings.address || '',
    paymentTerms: settings.paymentTerms || '',

    // Additional variables
    invoiceUrl: invoiceUrl || '',
    date: formatDate(Date.now()),
    message: '', // For general notifications
    daysPastDue: '', // For reminders

    // PDF-specific variables
    pdfDownloadUrl: pdfOptions?.downloadUrl || '',
    hasAttachment: pdfOptions?.hasAttachment ? 'true' : 'false',
    downloadInstructions: pdfOptions?.downloadInstructions || '',
    // Only show separate PDF download button if it's different from the main invoice URL
    // or if we want to emphasize the PDF nature of the download
    pdfDownloadButton: pdfOptions?.downloadUrl && pdfOptions.downloadUrl !== invoiceUrl ?
      `<a href="${pdfOptions.downloadUrl}" style="display: inline-block; background: #059669; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; margin: 10px 0;">📄 Download PDF Invoice</a>` : '',
    attachmentInfo: pdfOptions?.hasAttachment ?
      '<p style="color: #059669; font-weight: 600; margin: 10px 0;">📎 PDF invoice is attached to this email</p>' :
      (pdfOptions?.downloadUrl ? '<p style="color: #059669; font-weight: 600; margin: 10px 0;">📄 Your PDF invoice is ready for download</p>' : ''),
  };
}

// Log email operation for debugging
export function logEmailOperation(
  operation: string,
  invoiceId: string,
  details: Record<string, any>
): void {
  console.log(`Email ${operation}:`, {
    invoiceId,
    timestamp: new Date().toISOString(),
    ...details
  });
}

// Validate email content before sending
export function validateEmailContent(subject: string, html: string): { isValid: boolean; error?: string } {
  if (!subject || subject.trim().length === 0) {
    return { isValid: false, error: "Email subject is required" };
  }

  if (!html || html.trim().length === 0) {
    return { isValid: false, error: "Email content generation failed - empty HTML" };
  }

  return { isValid: true };
}

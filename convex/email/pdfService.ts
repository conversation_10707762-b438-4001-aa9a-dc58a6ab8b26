// PDF Email service utilities
// Extended email service for PDF delivery with attachments

import { Resend } from "resend";
import { parseResendError } from "./validation";
import { EmailConfig, EmailResult } from "./service";

export interface PDFEmailResult extends EmailResult {
  attachmentSize?: number;
  hasAttachment?: boolean;
}

export interface PDFAttachment {
  filename: string;
  content: Buffer;
  type: string;
}

// Re-export common functions from base service
export { createResendClient, getEmailConfig, generateEmailSubject, logEmailOperation, validateEmailContent } from "./service";

// Send email with optional PDF attachment
export async function sendEmailWithAttachment(
  resend: Resend,
  config: EmailConfig,
  to: string,
  subject: string,
  html: string,
  attachment?: PDFAttachment
): Promise<PDFEmailResult> {
  try {
    const fromAddress = `${config.fromName} <${config.fromEmail}>`;
    
    console.log(`Sending PDF email:`);
    console.log(`- To: ${to}`);
    console.log(`- From: ${fromAddress}`);
    console.log(`- Subject: ${subject}`);
    console.log(`- Has Attachment: ${!!attachment}`);
    if (attachment) {
      console.log(`- Attachment Size: ${attachment.content.length} bytes`);
    }

    // Prepare email data
    const emailData: any = {
      from: fromAddress,
      to: [to],
      subject,
      html,
      replyTo: config.replyTo,
    };

    // Add attachment if provided
    if (attachment) {
      // Validate attachment size (Resend has limits)
      const maxSize = 25 * 1024 * 1024; // 25MB limit for Resend
      if (attachment.content.length > maxSize) {
        console.warn(`Attachment too large (${attachment.content.length} bytes), skipping attachment`);
      } else {
        emailData.attachments = [{
          filename: attachment.filename,
          content: attachment.content,
          type: attachment.type
        }];
      }
    }

    const result = await resend.emails.send(emailData);

    if (result.error) {
      console.error("Resend API error:", result.error);
      return {
        success: false,
        error: parseResendError(result.error),
        hasAttachment: !!attachment,
        attachmentSize: attachment?.content.length
      };
    }

    if (!result.data?.id) {
      console.error("Email sent but no ID returned:", result);
      return {
        success: false,
        error: "Email may not have been sent properly. Please check with the recipient.",
        hasAttachment: !!attachment,
        attachmentSize: attachment?.content.length
      };
    }

    console.log(`PDF email sent successfully. Resend ID: ${result.data.id}`);
    
    return {
      success: true,
      emailId: result.data.id,
      hasAttachment: !!attachment,
      attachmentSize: attachment?.content.length
    };

  } catch (error: any) {
    console.error("PDF email sending error:", error);
    return {
      success: false,
      error: parseResendError(error),
      hasAttachment: !!attachment,
      attachmentSize: attachment?.content.length
    };
  }
}

// Validate PDF attachment
export function validatePDFAttachment(attachment: PDFAttachment): { isValid: boolean; error?: string } {
  if (!attachment.filename) {
    return { isValid: false, error: "Attachment filename is required" };
  }

  if (!attachment.filename.toLowerCase().endsWith('.pdf')) {
    return { isValid: false, error: "Attachment must be a PDF file" };
  }

  if (!attachment.content || attachment.content.length === 0) {
    return { isValid: false, error: "Attachment content is empty" };
  }

  // Check file size limits
  const maxSize = 25 * 1024 * 1024; // 25MB
  if (attachment.content.length > maxSize) {
    return { 
      isValid: false, 
      error: `Attachment too large (${Math.round(attachment.content.length / 1024 / 1024)}MB). Maximum size is 25MB.` 
    };
  }

  if (attachment.type !== 'application/pdf') {
    return { isValid: false, error: "Attachment type must be 'application/pdf'" };
  }

  return { isValid: true };
}

// Generate PDF-specific email subject
export function generatePDFEmailSubject(invoiceNumber: string, companyName: string): string {
  return `Invoice ${invoiceNumber} - PDF Ready for Download | ${companyName}`;
}

// Log PDF-specific email operations
export function logPDFEmailOperation(
  operation: string, 
  invoiceId: string, 
  details: Record<string, any> = {}
): void {
  const timestamp = new Date().toISOString();
  console.log(`[PDF-EMAIL-${operation.toUpperCase()}] ${timestamp} - Invoice: ${invoiceId}`, details);
}

// Validate PDF email configuration
export function validatePDFEmailConfig(): { isValid: boolean; error?: string } {
  const apiKey = process.env.CONVEX_RESEND_API_KEY || process.env.RESEND_API_KEY;
  
  if (!apiKey) {
    return {
      isValid: false,
      error: "Resend API key not configured. Please set CONVEX_RESEND_API_KEY or RESEND_API_KEY environment variable."
    };
  }

  if (!apiKey.startsWith('re_')) {
    return {
      isValid: false,
      error: "Invalid Resend API key format. API key should start with 're_'."
    };
  }

  return { isValid: true };
}

// Generate download instructions for email
export function generateDownloadInstructions(downloadUrl?: string): string {
  if (!downloadUrl) {
    return "Your invoice PDF is attached to this email. You can also contact us for a download link.";
  }

  return `
    To download your invoice PDF:
    1. Click the download button in this email
    2. Or copy and paste this link in your browser: ${downloadUrl}
    3. The PDF will open in a new tab where you can view, print, or save it
    
    The download link will remain active for 7 days.
  `;
}

// Estimate email size with attachment
export function estimateEmailSize(htmlContent: string, attachment?: PDFAttachment): number {
  const htmlSize = new Blob([htmlContent]).size;
  const attachmentSize = attachment ? attachment.content.length : 0;
  
  // Add some overhead for email headers and encoding
  const overhead = 1024; // 1KB overhead
  
  return htmlSize + attachmentSize + overhead;
}

// Check if email size is within limits
export function isEmailSizeValid(htmlContent: string, attachment?: PDFAttachment): { isValid: boolean; error?: string; size: number } {
  const size = estimateEmailSize(htmlContent, attachment);
  const maxSize = 25 * 1024 * 1024; // 25MB limit
  
  if (size > maxSize) {
    return {
      isValid: false,
      error: `Email too large (${Math.round(size / 1024 / 1024)}MB). Maximum size is 25MB. Consider sending without attachment.`,
      size
    };
  }
  
  return { isValid: true, size };
}

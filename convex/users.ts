import { getAuthUserId } from "@convex-dev/auth/server";
import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { getCurrentUserWithRole, checkStaffOrAdminPermission } from "./auth";

// Get current user helper
async function getCurrentUser(ctx: any) {
  const userId = await getAuthUserId(ctx);
  if (!userId) throw new Error("Not authenticated");
  return userId;
}

// Get user profile with stats and activity
export const getProfile = query({
  args: {},
  handler: async (ctx) => {
    // Get current user with role (this will assign default role if needed)
    const { userId, role, user } = await getCurrentUserWithRole(ctx);

    // Get jobs count for this user
    const jobs = await ctx.db
      .query("jobs")
      .filter((q) => q.eq(q.field("assignedTo"), userId))
      .collect();

    // Get customers count (assuming jobs have customerId)
    const customerIds = new Set(jobs.map(job => job.customerId).filter(Boolean));
    const totalCustomers = customerIds.size;

    // Calculate total revenue from completed jobs
    const completedJobs = jobs.filter(job => job.status === "completed");
    // Note: Jobs don't have a total field, so we'll set revenue to 0 for now
    // In a real implementation, you might calculate this from related invoices
    const totalRevenue = 0;

    // Get recent activity (last 10 jobs)
    const recentJobs = jobs
      .sort((a, b) => (b._creationTime || 0) - (a._creationTime || 0))
      .slice(0, 10);

    const recentActivity = recentJobs.map(job => ({
      type: "job",
      title: `Job #${job._id.slice(-6)}`,
      description: job.description || "HVAC Service",
      timestamp: job._creationTime || Date.now()
    }));

    return {
      phone: user.phone || "", // Get from user record or default empty
      bio: user.bio || "", // Get from user record or default empty
      department: user.department || "", // Get from user record or default empty
      role: role, // Use the actual role from database
      stats: {
        totalJobs: jobs.length,
        completedJobs: completedJobs.length,
        totalCustomers,
        totalRevenue
      },
      recentActivity,
      settings: {
        notifications: {
          email: true,
          push: true,
          sms: false
        },
        language: "en",
        timezone: "America/New_York",
        autoSave: true
      }
    };
  },
});

// Update user profile
export const updateProfile = mutation({
  args: {
    name: v.optional(v.string()),
    phone: v.optional(v.string()),
    bio: v.optional(v.string()),
    department: v.optional(v.string()),
    role: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getCurrentUser(ctx);

    // Prepare update object with only provided fields
    const updates: any = {};
    if (args.name !== undefined) updates.name = args.name;
    if (args.phone !== undefined) updates.phone = args.phone;
    if (args.bio !== undefined) updates.bio = args.bio;
    if (args.department !== undefined) updates.department = args.department;
    if (args.role !== undefined) updates.role = args.role;

    // Update the user record in the database
    if (Object.keys(updates).length > 0) {
      await ctx.db.patch(userId, updates);
    }

    return { success: true };
  },
});

// Update user settings
export const updateSettings = mutation({
  args: {
    notifications: v.optional(v.object({
      email: v.boolean(),
      push: v.boolean(),
      sms: v.boolean(),
    })),
    language: v.optional(v.string()),
    timezone: v.optional(v.string()),
    autoSave: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const userId = await getCurrentUser(ctx);
    
    console.log("Settings update requested for user:", userId, "with data:", args);
    
    // In a real implementation, you would update the user settings in the database
    // This might involve updating a userSettings table or a settings field in the users table
    // await ctx.db.patch(userSettingsRecord._id, {
    //   notifications: args.notifications,
    //   language: args.language,
    //   timezone: args.timezone,
    //   autoSave: args.autoSave,
    // });
    
    return { success: true };
  },
});

// Change password
export const changePassword = mutation({
  args: {
    currentPassword: v.string(),
    newPassword: v.string(),
  },
  handler: async (ctx, args) => {
    const userId = await getCurrentUser(ctx);
    
    console.log("Password change requested for user:", userId);
    
    // In a real implementation, you would:
    // 1. Verify the current password
    // 2. Hash the new password
    // 3. Update the password in the auth system
    // 4. Optionally invalidate existing sessions
    
    // For now, we'll simulate the process
    if (args.currentPassword.length < 1) {
      throw new Error("Current password is required");
    }
    
    if (args.newPassword.length < 8) {
      throw new Error("New password must be at least 8 characters long");
    }
    
    // Simulate password validation and update
    await new Promise(resolve => setTimeout(resolve, 500));
    
    return { success: true };
  },
});

// Enable/Disable Two-Factor Authentication
export const updateTwoFactor = mutation({
  args: {
    enabled: v.boolean(),
    secret: v.optional(v.string()),
    verificationCode: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getCurrentUser(ctx);
    
    console.log("2FA update requested for user:", userId, "enabled:", args.enabled);
    
    if (args.enabled) {
      // Enabling 2FA
      if (!args.verificationCode || args.verificationCode.length !== 6) {
        throw new Error("Valid 6-digit verification code is required");
      }
      
      // In a real implementation, you would:
      // 1. Verify the TOTP code against the secret
      // 2. Store the secret securely in the database
      // 3. Mark 2FA as enabled for the user
      
      console.log("Enabling 2FA with secret:", args.secret, "and code:", args.verificationCode);
    } else {
      // Disabling 2FA
      // In a real implementation, you would:
      // 1. Remove the 2FA secret from the database
      // 2. Mark 2FA as disabled for the user
      // 3. Optionally require password confirmation
      
      console.log("Disabling 2FA for user");
    }
    
    return { success: true };
  },
});

// Get user security settings
export const getSecuritySettings = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getCurrentUser(ctx);
    
    // In a real implementation, you would fetch from the database
    return {
      twoFactorEnabled: false, // Default to false
      lastPasswordChange: Date.now() - (30 * 24 * 60 * 60 * 1000), // 30 days ago
      activeSessions: [
        {
          id: "session1",
          device: "Chrome on Windows",
          location: "New York, NY",
          lastActive: Date.now() - (2 * 60 * 60 * 1000), // 2 hours ago
          current: true,
        },
        {
          id: "session2", 
          device: "Safari on iPhone",
          location: "New York, NY",
          lastActive: Date.now() - (24 * 60 * 60 * 1000), // 1 day ago
          current: false,
        },
      ],
    };
  },
});

// Revoke user session
export const revokeSession = mutation({
  args: {
    sessionId: v.string(),
  },
  handler: async (ctx, args) => {
    const userId = await getCurrentUser(ctx);
    
    console.log("Session revoke requested for user:", userId, "session:", args.sessionId);
    
    // In a real implementation, you would:
    // 1. Find the session in the database
    // 2. Mark it as revoked or delete it
    // 3. Invalidate the session token
    
    return { success: true };
  },
});

// Generate 2FA secret and QR code
export const generateTwoFactorSecret = mutation({
  args: {},
  handler: async (ctx) => {
    const userId = await getCurrentUser(ctx);

    // In a real implementation, you would:
    // 1. Generate a random secret using a library like speakeasy
    // 2. Create a QR code URL for the authenticator app
    // 3. Store the secret temporarily (not permanently until verified)

    const secret = "JBSWY3DPEHPK3PXP"; // This would be randomly generated
    const qrCodeUrl = `otpauth://totp/HVAC%20CRM:<EMAIL>?secret=${secret}&issuer=HVAC%20CRM`;

    console.log("Generated 2FA secret for user:", userId);

    return {
      secret,
      qrCodeUrl,
      manualEntryKey: secret,
    };
  },
});

// Check if user profile is complete for onboarding
export const isProfileComplete = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return { isComplete: false, missingFields: [] };
    }

    const user = await ctx.db.get(userId);
    if (!user) {
      return { isComplete: false, missingFields: [] };
    }

    const requiredFields = ['name', 'phone', 'department', 'role'];
    const missingFields: string[] = [];

    // Check each required field
    if (!user.name || user.name.trim().length === 0) {
      missingFields.push('name');
    }
    if (!user.phone || user.phone.trim().length === 0) {
      missingFields.push('phone');
    }
    if (!user.department || user.department.trim().length === 0) {
      missingFields.push('department');
    }
    if (!user.role || user.role.trim().length === 0) {
      missingFields.push('role');
    }

    const isComplete = missingFields.length === 0;

    return {
      isComplete,
      missingFields,
      user: {
        name: user.name || "",
        email: user.email || "",
        phone: user.phone || "",
        department: user.department || "",
        role: user.role || "",
      }
    };
  },
});
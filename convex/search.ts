import { v } from "convex/values";
import { query } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";

// Get current user helper
async function getCurrentUser(ctx: any) {
  const userId = await getAuthUserId(ctx);
  if (!userId) throw new Error("Not authenticated");
  return userId;
}

// Global search across customers, invoices, jobs, and products
export const globalSearch = query({
  args: { 
    query: v.string(),
    limit: v.optional(v.number()),
    types: v.optional(v.array(v.string())) // Optional filter by data types
  },
  handler: async (ctx, args) => {
    try {
      const userId = await getCurrentUser(ctx);
      const searchTerm = args.query.toLowerCase().trim();
      const limit = args.limit || 20;
      const searchTypes = args.types || ['customers', 'invoices', 'jobs', 'products'];

      if (searchTerm.length < 2) {
        return {
          customers: [],
          invoices: [],
          jobs: [],
          products: [],
          total: 0
        };
      }

    const results: any = {
      customers: [],
      invoices: [],
      jobs: [],
      products: [],
      total: 0
    };

    // Search customers
    if (searchTypes.includes('customers')) {
      const customers = await ctx.db
        .query("customers")
        .withIndex("by_created_by", (q) => q.eq("createdBy", userId))
        .collect();
      
      results.customers = customers
        .filter(customer => 
          customer.name.toLowerCase().includes(searchTerm) ||
          customer.email.toLowerCase().includes(searchTerm) ||
          (customer.company && customer.company.toLowerCase().includes(searchTerm)) ||
          customer.phone.includes(searchTerm)
        )
        .slice(0, Math.floor(limit / 4))
        .map(customer => ({
          ...customer,
          type: 'customer',
          title: customer.name,
          subtitle: customer.email,
          description: customer.company || customer.phone
        }));
    }

    // Search invoices
    if (searchTypes.includes('invoices')) {
      const invoices = await ctx.db
        .query("invoices")
        .withIndex("by_created_by", (q) => q.eq("createdBy", userId))
        .collect();

      // Get customer data for invoices
      const customerIds = [...new Set(invoices.map(inv => inv.customerId))];
      const customers = await Promise.all(
        customerIds.map(id => ctx.db.get(id))
      );
      const customerMap = new Map(
        customers.filter(Boolean).map(customer => [customer!._id, customer])
      );

      results.invoices = invoices
        .filter(invoice => {
          const customer = customerMap.get(invoice.customerId);
          return invoice.invoiceNumber.toLowerCase().includes(searchTerm) ||
                 (customer && customer.name.toLowerCase().includes(searchTerm));
        })
        .slice(0, Math.floor(limit / 4))
        .map(invoice => {
          const customer = customerMap.get(invoice.customerId);
          return {
            ...invoice,
            type: 'invoice',
            title: `Invoice ${invoice.invoiceNumber}`,
            subtitle: customer?.name || 'Unknown Customer',
            description: `$${invoice.total.toFixed(2)} - ${invoice.status}`,
            customer
          };
        });
    }

    // Search jobs
    if (searchTypes.includes('jobs')) {
      const jobs = await ctx.db
        .query("jobs")
        .withIndex("by_created_by", (q) => q.eq("createdBy", userId))
        .collect();

      // Get customer data for jobs
      const customerIds = [...new Set(jobs.map(job => job.customerId))];
      const customers = await Promise.all(
        customerIds.map(id => ctx.db.get(id))
      );
      const customerMap = new Map(
        customers.filter(Boolean).map(customer => [customer!._id, customer])
      );

      results.jobs = jobs
        .filter(job => {
          const customer = customerMap.get(job.customerId);
          return job.title.toLowerCase().includes(searchTerm) ||
                 job.description.toLowerCase().includes(searchTerm) ||
                 (customer && customer.name.toLowerCase().includes(searchTerm));
        })
        .slice(0, Math.floor(limit / 4))
        .map(job => {
          const customer = customerMap.get(job.customerId);
          return {
            ...job,
            type: 'job',
            title: job.title,
            subtitle: customer?.name || 'Unknown Customer',
            description: `${job.status} - ${job.priority} priority`,
            customer
          };
        });
    }

    // Search products
    if (searchTypes.includes('products')) {
      const products = await ctx.db
        .query("products")
        .withIndex("by_created_by", (q) => q.eq("createdBy", userId))
        .filter((q) => q.eq(q.field("isActive"), true))
        .collect();

      results.products = products
        .filter(product =>
          product.name.toLowerCase().includes(searchTerm) ||
          (product.description && product.description.toLowerCase().includes(searchTerm)) ||
          (product.sku && product.sku.toLowerCase().includes(searchTerm)) ||
          product.category.toLowerCase().includes(searchTerm)
        )
        .slice(0, Math.floor(limit / 4))
        .map(product => ({
          ...product,
          type: 'product',
          title: product.name,
          subtitle: product.category,
          description: `$${product.price.toFixed(2)} - ${product.sku || 'No SKU'}`
        }));
    }

      // Calculate total results
      results.total = results.customers.length +
                     results.invoices.length +
                     results.jobs.length +
                     results.products.length;

      return results;
    } catch (error) {
      console.error("Error in globalSearch:", error);
      return {
        customers: [],
        invoices: [],
        jobs: [],
        products: [],
        total: 0
      };
    }
  },
});

// Quick search for autocomplete/suggestions
export const quickSearch = query({
  args: { 
    query: v.string(),
    limit: v.optional(v.number())
  },
  handler: async (ctx, args) => {
    try {
      const userId = await getCurrentUser(ctx);
      const searchTerm = args.query.toLowerCase().trim();
      const limit = args.limit || 8;

      if (searchTerm.length < 2) {
        return [];
      }

    // Quick search primarily in customers and recent invoices
    const customers = await ctx.db
      .query("customers")
      .withIndex("by_created_by", (q) => q.eq("createdBy", userId))
      .collect();
    
    const recentInvoices = await ctx.db
      .query("invoices")
      .withIndex("by_created_by", (q) => q.eq("createdBy", userId))
      .order("desc")
      .take(50); // Only search recent invoices for performance

    const suggestions = [];

    // Add customer suggestions
    const customerSuggestions = customers
      .filter(customer => 
        customer.name.toLowerCase().includes(searchTerm) ||
        customer.email.toLowerCase().includes(searchTerm)
      )
      .slice(0, Math.floor(limit / 2))
      .map(customer => ({
        id: customer._id,
        type: 'customer',
        title: customer.name,
        subtitle: customer.email,
        icon: '👤'
      }));

    suggestions.push(...customerSuggestions);

    // Add invoice suggestions
    const invoiceSuggestions = recentInvoices
      .filter(invoice => 
        invoice.invoiceNumber.toLowerCase().includes(searchTerm)
      )
      .slice(0, Math.floor(limit / 2))
      .map(invoice => ({
        id: invoice._id,
        type: 'invoice',
        title: `Invoice ${invoice.invoiceNumber}`,
        subtitle: `$${invoice.total.toFixed(2)}`,
        icon: '💰'
      }));

      suggestions.push(...invoiceSuggestions);

      return suggestions.slice(0, limit);
    } catch (error) {
      console.error("Error in quickSearch:", error);
      return [];
    }
  },
});

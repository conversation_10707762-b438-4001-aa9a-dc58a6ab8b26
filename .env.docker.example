# Docker Environment Configuration for BH HVAC CRM
# Copy this file to .env.docker and fill in your actual values

# =============================================================================
# REQUIRED CONFIGURATION
# =============================================================================

# Convex Configuration (REQUIRED)
# Get these values from your Convex dashboard: https://dashboard.convex.dev
VITE_CONVEX_URL=https://your-deployment.convex.cloud

# =============================================================================
# OPTIONAL CONFIGURATION
# =============================================================================

# Mapbox Configuration (for address autocomplete)
# Get your API key from: https://account.mapbox.com/access-tokens/
VITE_MAPBOX_API_KEY=pk.your_mapbox_api_key_here

# Node Environment
NODE_ENV=production

# =============================================================================
# CONVEX BACKEND ENVIRONMENT VARIABLES
# =============================================================================
# Note: These are set in the Convex Dashboard, not in Docker
# Listed here for reference only

# Authentication
# AUTH_SECRET=your-auth-secret-here

# Email Configuration (Resend)
# CONVEX_RESEND_API_KEY=re_your_resend_api_key_here
# FROM_EMAIL=<EMAIL>
# FROM_NAME=Your Company Name

# SMS Configuration (Twilio)
# TWILIO_ACCOUNT_SID=your_twilio_account_sid
# TWILIO_AUTH_TOKEN=your_twilio_auth_token
# TWILIO_PHONE_NUMBER=+**********

# Security Configuration
# ENCRYPTION_MASTER_KEY=your-super-secure-64-character-encryption-key-here
# ENCRYPTION_KEY_VERSION=1

# Convex Site URL (for authentication)
# CONVEX_SITE_URL=https://your-app.convex.site

# =============================================================================
# DOCKER-SPECIFIC CONFIGURATION
# =============================================================================

# Container timezone (optional)
TZ=America/New_York

# Container user (optional, for security)
# PUID=1000
# PGID=1000

# Email Template Synchronization Documentation

## Overview

This document describes the relationship between email templates and PDF email functionality, ensuring proper synchronization between template updates and live email sending functionality.

## System Architecture

### Email Template Database
- **Table**: `emailTemplates` in Convex schema
- **Categories**: invoice, notification, reminder, general
- **Variables**: Dynamic placeholders like `{customerName}`, `{invoiceNumber}`, etc.
- **Admin Interface**: Full CRUD operations for template management

### Email Sending Systems

#### 1. Regular Invoice Emails (`convex/invoiceEmail.ts`)
- ✅ Uses database-stored email templates
- ✅ Retrieves templates via `getByCategoryInternal` query
- ✅ Supports template variables and customization
- ✅ Falls back to default templates if none found

#### 2. PDF Invoice Emails (`convex/invoicePDFEmail.ts`)
- ✅ **NOW INTEGRATED** - Uses database-stored email templates
- ✅ Retrieves templates via `getByCategoryInternal` query
- ✅ Supports all template variables plus PDF-specific variables
- ✅ Falls back to hardcoded template if database template fails

## Template Variables

### Standard Invoice Variables
```
{customerName}      - Customer's full name
{customerEmail}     - Customer's email address
{customerPhone}     - Customer's phone number
{customerAddress}   - Customer's address
{customerCity}      - Customer's city
{customerState}     - Customer's state
{customerZipCode}   - Customer's ZIP code
{customerCompany}   - Customer's company name
{invoiceNumber}     - Invoice number
{invoiceDate}       - Invoice issue date
{dueDate}          - Invoice due date
{subtotal}         - Invoice subtotal amount
{taxAmount}        - Tax amount
{total}            - Total invoice amount
{companyName}      - Company name
{companyEmail}     - Company email
{companyPhone}     - Company phone
{companyAddress}   - Company address
{paymentTerms}     - Payment terms
{invoiceUrl}       - Link to view invoice online
{notes}            - Invoice notes
```

### PDF-Specific Variables
```
{pdfDownloadUrl}      - Direct download link for PDF
{hasAttachment}       - "true" if PDF is attached, "false" otherwise
{downloadInstructions} - Instructions for downloading PDF
{pdfDownloadButton}   - Styled HTML download button
{attachmentInfo}      - Information about PDF attachment/download
```

## Implementation Details

### Template Retrieval Process

1. **PDF Email Function Called**
   ```typescript
   // convex/invoicePDFEmail.ts
   const emailTemplate = await ctx.runQuery(internal.emailTemplates.getByCategoryInternal, {
     category: "invoice"
   });
   ```

2. **Template Variables Generated**
   ```typescript
   const templateVariables = generateTemplateVariables(brandedInvoice, settings, undefined, {
     downloadUrl: pdfUrl,
     hasAttachment: options.includeAttachment,
     downloadInstructions: "Click the download button below..."
   });
   ```

3. **Template Content Processed**
   ```typescript
   emailSubject = replaceTemplateVariables(emailTemplate.subject, templateVariables);
   emailHtml = replaceTemplateVariables(emailTemplate.content, templateVariables);
   ```

4. **Fallback Handling**
   ```typescript
   // If template retrieval fails, use hardcoded template
   emailHtml = generatePDFEmailTemplate(brandedInvoice, options);
   ```

### Error Handling and Fallbacks

1. **Template Not Found**: Falls back to hardcoded PDF template
2. **Template Retrieval Error**: Falls back to hardcoded PDF template
3. **Variable Processing Error**: Logs error and continues with available variables
4. **Email Sending Error**: Returns detailed error message to user

## Synchronization Guarantees

### ✅ Automatic Synchronization
- **Real-time Updates**: Template changes are immediately available to PDF emails
- **No Cache**: Templates are retrieved fresh for each email send
- **No Manual Steps**: No restart or cache clearing required
- **Consistent Behavior**: Both regular and PDF emails use same template system

### ✅ Backward Compatibility
- **Existing Templates**: Continue to work without modification
- **New Variables**: PDF-specific variables are optional
- **Graceful Degradation**: Missing variables are replaced with empty strings
- **Fallback Strategy**: Hardcoded templates ensure emails always send

## Dependencies

### Required Components
1. **Database Schema**: `emailTemplates` table with proper indexes
2. **Template Functions**: `getByCategoryInternal` query function
3. **Variable Processing**: `generateTemplateVariables` and `replaceTemplateVariables`
4. **Fallback Templates**: Hardcoded templates in `convex/email/pdfTemplates.ts`

### No Manual Dependencies
- ❌ No cache clearing required
- ❌ No server restart required
- ❌ No manual template synchronization
- ❌ No configuration updates needed

## Testing and Verification

### Template Synchronization Test
1. **Create/Edit Template**: Modify invoice template in admin interface
2. **Include PDF Variables**: Add `{pdfDownloadButton}` or `{attachmentInfo}`
3. **Send PDF Email**: Generate and send PDF invoice via email
4. **Verify Content**: Confirm email uses updated template with PDF features

### Fallback Test
1. **Disable Template**: Mark invoice template as inactive
2. **Send PDF Email**: Attempt to send PDF invoice
3. **Verify Fallback**: Confirm email uses hardcoded template
4. **Re-enable Template**: Activate template and verify it's used again

## Troubleshooting

### Common Issues

1. **PDF Variables Not Showing**
   - Check template includes PDF-specific variables
   - Verify `generateTemplateVariables` receives `pdfOptions`
   - Confirm template is active and in "invoice" category

2. **Template Changes Not Reflected**
   - Verify template is marked as active
   - Check template category is "invoice"
   - Confirm no caching issues (should be none)

3. **Email Sending Fails**
   - Check fallback template is available
   - Verify email configuration is correct
   - Review error logs for specific issues

### Debug Information
All PDF email operations are logged with:
- Template retrieval success/failure
- Template ID and name used
- Fallback reasons
- Variable processing results
- Email sending results

## Maintenance

### Regular Maintenance
- **Monitor Logs**: Check for template retrieval errors
- **Test Templates**: Periodically verify template functionality
- **Update Variables**: Add new variables as features are added

### No Ongoing Synchronization Required
The system is designed to be maintenance-free regarding template synchronization. Changes to email templates are automatically reflected in PDF emails without any manual intervention.
